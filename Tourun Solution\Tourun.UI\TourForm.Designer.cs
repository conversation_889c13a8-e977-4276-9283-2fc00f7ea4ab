﻿namespace Tourun.UI
{
    partial class TourForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.Label tourIDLabel;
            System.Windows.Forms.Label descriptionLabel;
            System.Windows.Forms.Label coordinatorLabel;
            System.Windows.Forms.Label priceAmountLabel;
            System.Windows.Forms.Label statusLabel;
            Janus.Windows.Common.JanusColorScheme janusColorScheme1 = new Janus.Windows.Common.JanusColorScheme();
            Janus.Windows.GridEX.GridEXLayout vehiclesGrid_Layout_0 = new Janus.Windows.GridEX.GridEXLayout();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TourForm));
            Janus.Windows.GridEX.GridEXLayout attractionsGrid_Layout_0 = new Janus.Windows.GridEX.GridEXLayout();
            Janus.Windows.GridEX.GridEXLayout accommodationsGrid_Layout_0 = new Janus.Windows.GridEX.GridEXLayout();
            this.tourVehiclesBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.toursBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.tourunDataSet = new Tourun.UI.TourunDataSet();
            this.tourAttractionsBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.tourAccommodationsBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.tourCodeLabel = new System.Windows.Forms.Label();
            this.arrivalDateLabel = new System.Windows.Forms.Label();
            this.departureDateLabel = new System.Windows.Forms.Label();
            this.allotmentLabel = new System.Windows.Forms.Label();
            this.paxBookedLabel = new System.Windows.Forms.Label();
            this.visualStyleManager = new Janus.Windows.Common.VisualStyleManager(this.components);
            this.label1 = new System.Windows.Forms.Label();
            this.tourIDTextBox = new System.Windows.Forms.TextBox();
            this.errorProvider = new System.Windows.Forms.ErrorProvider(this.components);
            this.commandManager = new Janus.Windows.UI.CommandBars.UICommandManager(this.components);
            this.BottomRebar1 = new Janus.Windows.UI.CommandBars.UIRebar();
            this.toolbar = new Janus.Windows.UI.CommandBars.UICommandBar();
            this.SaveAndClose1 = new Janus.Windows.UI.CommandBars.UICommand("SaveAndClose");
            this.SaveAndNew1 = new Janus.Windows.UI.CommandBars.UICommand("SaveAndNew");
            this.Delete1 = new Janus.Windows.UI.CommandBars.UICommand("Delete");
            this.saveAndCloseCmd = new Janus.Windows.UI.CommandBars.UICommand("SaveAndClose");
            this.saveAndNewCmd = new Janus.Windows.UI.CommandBars.UICommand("SaveAndNew");
            this.deleteCmd = new Janus.Windows.UI.CommandBars.UICommand("Delete");
            this.LeftRebar1 = new Janus.Windows.UI.CommandBars.UIRebar();
            this.RightRebar1 = new Janus.Windows.UI.CommandBars.UIRebar();
            this.TopRebar1 = new Janus.Windows.UI.CommandBars.UIRebar();
            this.label7 = new System.Windows.Forms.Label();
            this.label23 = new System.Windows.Forms.Label();
            this.cancelBtn = new System.Windows.Forms.Button();
            this.okBtn = new System.Windows.Forms.Button();
            this.tourCodeTextBox = new System.Windows.Forms.TextBox();
            this.descriptionTextBox = new System.Windows.Forms.TextBox();
            this.arrivalDatePicker = new System.Windows.Forms.DateTimePicker();
            this.departureDatePicker = new System.Windows.Forms.DateTimePicker();
            this.coordinatorComboBox = new System.Windows.Forms.ComboBox();
            this.coordinatorsBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.allotmentNumericUpDown = new System.Windows.Forms.NumericUpDown();
            this.priceAmountNumericUpDown = new System.Windows.Forms.NumericUpDown();
            this.tabControl = new System.Windows.Forms.TabControl();
            this.vehiclesTabPage = new System.Windows.Forms.TabPage();
            this.vehiclesGrid = new Janus.Windows.GridEX.GridEX();
            this.attractionsTabPage = new System.Windows.Forms.TabPage();
            this.attractionsGrid = new Janus.Windows.GridEX.GridEX();
            this.accommodationsTabPage = new System.Windows.Forms.TabPage();
            this.accommodationsGrid = new Janus.Windows.GridEX.GridEX();
            this.statusComboBox = new System.Windows.Forms.ComboBox();
            this.paxBookedNumericUpDown = new System.Windows.Forms.NumericUpDown();
            tourIDLabel = new System.Windows.Forms.Label();
            descriptionLabel = new System.Windows.Forms.Label();
            coordinatorLabel = new System.Windows.Forms.Label();
            priceAmountLabel = new System.Windows.Forms.Label();
            statusLabel = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.tourVehiclesBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.toursBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tourunDataSet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tourAttractionsBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tourAccommodationsBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.errorProvider)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.commandManager)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BottomRebar1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.toolbar)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.LeftRebar1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.RightRebar1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.TopRebar1)).BeginInit();
            this.TopRebar1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.coordinatorsBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.allotmentNumericUpDown)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.priceAmountNumericUpDown)).BeginInit();
            this.tabControl.SuspendLayout();
            this.vehiclesTabPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.vehiclesGrid)).BeginInit();
            this.attractionsTabPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.attractionsGrid)).BeginInit();
            this.accommodationsTabPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.accommodationsGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.paxBookedNumericUpDown)).BeginInit();
            this.SuspendLayout();
            // 
            // tourIDLabel
            // 
            tourIDLabel.Location = new System.Drawing.Point(23, 338);
            tourIDLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            tourIDLabel.Name = "tourIDLabel";
            tourIDLabel.Size = new System.Drawing.Size(119, 16);
            tourIDLabel.TabIndex = 0;
            tourIDLabel.Text = "Α/Α:";
            tourIDLabel.TextAlign = System.Drawing.ContentAlignment.TopRight;
            tourIDLabel.Visible = false;
            // 
            // descriptionLabel
            // 
            descriptionLabel.Location = new System.Drawing.Point(16, 160);
            descriptionLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            descriptionLabel.Name = "descriptionLabel";
            descriptionLabel.Size = new System.Drawing.Size(143, 16);
            descriptionLabel.TabIndex = 83;
            descriptionLabel.Text = "Σημειώσεις:";
            descriptionLabel.TextAlign = System.Drawing.ContentAlignment.TopRight;
            // 
            // coordinatorLabel
            // 
            coordinatorLabel.Location = new System.Drawing.Point(13, 130);
            coordinatorLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            coordinatorLabel.Name = "coordinatorLabel";
            coordinatorLabel.Size = new System.Drawing.Size(145, 16);
            coordinatorLabel.TabIndex = 89;
            coordinatorLabel.Text = "Συντονιστής:";
            coordinatorLabel.TextAlign = System.Drawing.ContentAlignment.TopRight;
            // 
            // priceAmountLabel
            // 
            priceAmountLabel.Location = new System.Drawing.Point(694, 128);
            priceAmountLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            priceAmountLabel.Name = "priceAmountLabel";
            priceAmountLabel.Size = new System.Drawing.Size(145, 16);
            priceAmountLabel.TabIndex = 93;
            priceAmountLabel.Text = "Τιμή:";
            priceAmountLabel.TextAlign = System.Drawing.ContentAlignment.TopRight;
            // 
            // statusLabel
            // 
            statusLabel.Location = new System.Drawing.Point(697, 44);
            statusLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            statusLabel.Name = "statusLabel";
            statusLabel.Size = new System.Drawing.Size(137, 16);
            statusLabel.TabIndex = 95;
            statusLabel.Text = "Κατάσταση:";
            statusLabel.TextAlign = System.Drawing.ContentAlignment.TopRight;
            // 
            // tourVehiclesBindingSource
            // 
            this.tourVehiclesBindingSource.DataMember = "FK_TourVehicle_Tour";
            this.tourVehiclesBindingSource.DataSource = this.toursBindingSource;
            // 
            // toursBindingSource
            // 
            this.toursBindingSource.DataMember = "Tour";
            this.toursBindingSource.DataSource = this.tourunDataSet;
            // 
            // tourunDataSet
            // 
            this.tourunDataSet.DataSetName = "TourunDataSet";
            this.tourunDataSet.SchemaSerializationMode = System.Data.SchemaSerializationMode.IncludeSchema;
            // 
            // tourAttractionsBindingSource
            // 
            this.tourAttractionsBindingSource.DataMember = "FK_TourAttraction_Tour";
            this.tourAttractionsBindingSource.DataSource = this.toursBindingSource;
            // 
            // tourAccommodationsBindingSource
            // 
            this.tourAccommodationsBindingSource.DataMember = "FK_TourAccommodation_Tour";
            this.tourAccommodationsBindingSource.DataSource = this.toursBindingSource;
            // 
            // tourCodeLabel
            // 
            this.tourCodeLabel.Location = new System.Drawing.Point(13, 44);
            this.tourCodeLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.tourCodeLabel.Name = "tourCodeLabel";
            this.tourCodeLabel.Size = new System.Drawing.Size(145, 16);
            this.tourCodeLabel.TabIndex = 18;
            this.tourCodeLabel.Text = "Κωδικός:";
            this.tourCodeLabel.TextAlign = System.Drawing.ContentAlignment.TopRight;
            // 
            // arrivalDateLabel
            // 
            this.arrivalDateLabel.Location = new System.Drawing.Point(13, 68);
            this.arrivalDateLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.arrivalDateLabel.Name = "arrivalDateLabel";
            this.arrivalDateLabel.Size = new System.Drawing.Size(145, 16);
            this.arrivalDateLabel.TabIndex = 85;
            this.arrivalDateLabel.Text = "Ημ/νία άφιξης:";
            this.arrivalDateLabel.TextAlign = System.Drawing.ContentAlignment.TopRight;
            // 
            // departureDateLabel
            // 
            this.departureDateLabel.Location = new System.Drawing.Point(13, 98);
            this.departureDateLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.departureDateLabel.Name = "departureDateLabel";
            this.departureDateLabel.Size = new System.Drawing.Size(145, 16);
            this.departureDateLabel.TabIndex = 87;
            this.departureDateLabel.Text = "Ημ/νία αναχώρησης:";
            this.departureDateLabel.TextAlign = System.Drawing.ContentAlignment.TopRight;
            // 
            // allotmentLabel
            // 
            this.allotmentLabel.Location = new System.Drawing.Point(694, 98);
            this.allotmentLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.allotmentLabel.Name = "allotmentLabel";
            this.allotmentLabel.Size = new System.Drawing.Size(145, 16);
            this.allotmentLabel.TabIndex = 91;
            this.allotmentLabel.Text = "Allotment:";
            this.allotmentLabel.TextAlign = System.Drawing.ContentAlignment.TopRight;
            // 
            // paxBookedLabel
            // 
            this.paxBookedLabel.Location = new System.Drawing.Point(694, 72);
            this.paxBookedLabel.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.paxBookedLabel.Name = "paxBookedLabel";
            this.paxBookedLabel.Size = new System.Drawing.Size(145, 16);
            this.paxBookedLabel.TabIndex = 97;
            this.paxBookedLabel.Text = "Κρατήσεις:";
            this.paxBookedLabel.TextAlign = System.Drawing.ContentAlignment.TopRight;
            // 
            // visualStyleManager
            // 
            janusColorScheme1.HighlightTextColor = System.Drawing.SystemColors.HighlightText;
            janusColorScheme1.Name = "Office 2010 Blue";
            janusColorScheme1.OfficeColorScheme = Janus.Windows.Common.OfficeColorScheme.Blue;
            janusColorScheme1.OfficeCustomColor = System.Drawing.Color.Empty;
            janusColorScheme1.VisualStyle = Janus.Windows.Common.VisualStyle.Office2010;
            this.visualStyleManager.ColorSchemes.Add(janusColorScheme1);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("Microsoft Sans Serif", 20.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(161)));
            this.label1.ForeColor = System.Drawing.Color.Orange;
            this.label1.Location = new System.Drawing.Point(601, 41);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(30, 39);
            this.label1.TabIndex = 79;
            this.label1.Text = "*";
            // 
            // tourIDTextBox
            // 
            this.tourIDTextBox.DataBindings.Add(new System.Windows.Forms.Binding("Text", this.toursBindingSource, "TourId", true));
            this.tourIDTextBox.Location = new System.Drawing.Point(152, 335);
            this.tourIDTextBox.Margin = new System.Windows.Forms.Padding(4);
            this.tourIDTextBox.Name = "tourIDTextBox";
            this.tourIDTextBox.ReadOnly = true;
            this.tourIDTextBox.Size = new System.Drawing.Size(429, 22);
            this.tourIDTextBox.TabIndex = 0;
            this.tourIDTextBox.TabStop = false;
            this.tourIDTextBox.Visible = false;
            // 
            // errorProvider
            // 
            this.errorProvider.ContainerControl = this;
            // 
            // commandManager
            // 
            this.commandManager.AllowClose = Janus.Windows.UI.InheritableBoolean.False;
            this.commandManager.AllowCustomize = Janus.Windows.UI.InheritableBoolean.False;
            this.commandManager.BottomRebar = this.BottomRebar1;
            this.commandManager.CommandBars.AddRange(new Janus.Windows.UI.CommandBars.UICommandBar[] {
            this.toolbar});
            this.commandManager.Commands.AddRange(new Janus.Windows.UI.CommandBars.UICommand[] {
            this.saveAndCloseCmd,
            this.saveAndNewCmd,
            this.deleteCmd});
            this.commandManager.ContainerControl = this;
            // 
            // 
            // 
            this.commandManager.EditContextMenu.Key = "";
            this.commandManager.Id = new System.Guid("422659ed-f48f-4fed-af6e-3a20d3fd167b");
            this.commandManager.LeftRebar = this.LeftRebar1;
            this.commandManager.LockCommandBars = true;
            this.commandManager.OfficeColorScheme = Janus.Windows.UI.OfficeColorScheme.Blue;
            this.commandManager.RightRebar = this.RightRebar1;
            this.commandManager.ShowAddRemoveButton = Janus.Windows.UI.InheritableBoolean.False;
            this.commandManager.ShowCustomizeButton = Janus.Windows.UI.InheritableBoolean.False;
            this.commandManager.ShowQuickCustomizeMenu = false;
            this.commandManager.ShowShadowUnderMenus = false;
            this.commandManager.TopRebar = this.TopRebar1;
            this.commandManager.UseCompatibleTextRendering = false;
            this.commandManager.VisualStyle = Janus.Windows.UI.VisualStyle.Office2010;
            this.commandManager.VisualStyleManager = this.visualStyleManager;
            this.commandManager.CommandClick += new Janus.Windows.UI.CommandBars.CommandEventHandler(this.commandManager_CommandClick);
            // 
            // BottomRebar1
            // 
            this.BottomRebar1.CommandManager = this.commandManager;
            this.BottomRebar1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.BottomRebar1.Location = new System.Drawing.Point(0, 608);
            this.BottomRebar1.Margin = new System.Windows.Forms.Padding(4);
            this.BottomRebar1.Name = "BottomRebar1";
            this.BottomRebar1.OfficeColorScheme = Janus.Windows.UI.OfficeColorScheme.Blue;
            this.BottomRebar1.Size = new System.Drawing.Size(1198, 0);
            // 
            // toolbar
            // 
            this.toolbar.CommandManager = this.commandManager;
            this.toolbar.Commands.AddRange(new Janus.Windows.UI.CommandBars.UICommand[] {
            this.SaveAndClose1,
            this.SaveAndNew1,
            this.Delete1});
            this.toolbar.FullRow = true;
            this.toolbar.Key = "CommandBar1";
            this.toolbar.Location = new System.Drawing.Point(0, 0);
            this.toolbar.Margin = new System.Windows.Forms.Padding(4);
            this.toolbar.Name = "toolbar";
            this.toolbar.OfficeColorScheme = Janus.Windows.UI.OfficeColorScheme.Blue;
            this.toolbar.RowIndex = 0;
            this.toolbar.Size = new System.Drawing.Size(1198, 28);
            this.toolbar.Text = "CommandBar1";
            // 
            // SaveAndClose1
            // 
            this.SaveAndClose1.Key = "SaveAndClose";
            this.SaveAndClose1.Name = "SaveAndClose1";
            // 
            // SaveAndNew1
            // 
            this.SaveAndNew1.Key = "SaveAndNew";
            this.SaveAndNew1.Name = "SaveAndNew1";
            // 
            // Delete1
            // 
            this.Delete1.Key = "Delete";
            this.Delete1.Name = "Delete1";
            // 
            // saveAndCloseCmd
            // 
            this.saveAndCloseCmd.Image = ((System.Drawing.Image)(resources.GetObject("saveAndCloseCmd.Image")));
            this.saveAndCloseCmd.Key = "SaveAndClose";
            this.saveAndCloseCmd.Name = "saveAndCloseCmd";
            this.saveAndCloseCmd.Text = "Αποθήκευση && Κλείσιμο";
            // 
            // saveAndNewCmd
            // 
            this.saveAndNewCmd.Key = "SaveAndNew";
            this.saveAndNewCmd.Name = "saveAndNewCmd";
            this.saveAndNewCmd.Text = "Αποθήκευση && Νέα";
            this.saveAndNewCmd.Visible = Janus.Windows.UI.InheritableBoolean.False;
            // 
            // deleteCmd
            // 
            this.deleteCmd.Image = ((System.Drawing.Image)(resources.GetObject("deleteCmd.Image")));
            this.deleteCmd.Key = "Delete";
            this.deleteCmd.Name = "deleteCmd";
            this.deleteCmd.Text = "Διαγραφή";
            // 
            // LeftRebar1
            // 
            this.LeftRebar1.CommandManager = this.commandManager;
            this.LeftRebar1.Dock = System.Windows.Forms.DockStyle.Left;
            this.LeftRebar1.Location = new System.Drawing.Point(0, 28);
            this.LeftRebar1.Margin = new System.Windows.Forms.Padding(4);
            this.LeftRebar1.Name = "LeftRebar1";
            this.LeftRebar1.OfficeColorScheme = Janus.Windows.UI.OfficeColorScheme.Blue;
            this.LeftRebar1.Size = new System.Drawing.Size(0, 580);
            // 
            // RightRebar1
            // 
            this.RightRebar1.CommandManager = this.commandManager;
            this.RightRebar1.Dock = System.Windows.Forms.DockStyle.Right;
            this.RightRebar1.Location = new System.Drawing.Point(1198, 28);
            this.RightRebar1.Margin = new System.Windows.Forms.Padding(4);
            this.RightRebar1.Name = "RightRebar1";
            this.RightRebar1.OfficeColorScheme = Janus.Windows.UI.OfficeColorScheme.Blue;
            this.RightRebar1.Size = new System.Drawing.Size(0, 580);
            // 
            // TopRebar1
            // 
            this.TopRebar1.CommandBars.AddRange(new Janus.Windows.UI.CommandBars.UICommandBar[] {
            this.toolbar});
            this.TopRebar1.CommandManager = this.commandManager;
            this.TopRebar1.Controls.Add(this.toolbar);
            this.TopRebar1.Dock = System.Windows.Forms.DockStyle.Top;
            this.TopRebar1.Location = new System.Drawing.Point(0, 0);
            this.TopRebar1.Margin = new System.Windows.Forms.Padding(4);
            this.TopRebar1.Name = "TopRebar1";
            this.TopRebar1.OfficeColorScheme = Janus.Windows.UI.OfficeColorScheme.Blue;
            this.TopRebar1.Size = new System.Drawing.Size(1198, 28);
            // 
            // label7
            // 
            this.label7.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("Microsoft Sans Serif", 20.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(161)));
            this.label7.ForeColor = System.Drawing.Color.Orange;
            this.label7.Location = new System.Drawing.Point(16, 572);
            this.label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(30, 39);
            this.label7.TabIndex = 79;
            this.label7.Text = "*";
            // 
            // label23
            // 
            this.label23.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label23.AutoSize = true;
            this.label23.ForeColor = System.Drawing.Color.OrangeRed;
            this.label23.Location = new System.Drawing.Point(45, 577);
            this.label23.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(96, 16);
            this.label23.TabIndex = 78;
            this.label23.Text = "= Υποχρεωτικό";
            // 
            // cancelBtn
            // 
            this.cancelBtn.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.cancelBtn.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.cancelBtn.Location = new System.Drawing.Point(1082, 572);
            this.cancelBtn.Margin = new System.Windows.Forms.Padding(4);
            this.cancelBtn.Name = "cancelBtn";
            this.cancelBtn.Size = new System.Drawing.Size(100, 28);
            this.cancelBtn.TabIndex = 11;
            this.cancelBtn.Text = "Άκυρο";
            this.cancelBtn.UseVisualStyleBackColor = true;
            this.cancelBtn.Visible = false;
            this.cancelBtn.Click += new System.EventHandler(this.cancelBtn_Click);
            // 
            // okBtn
            // 
            this.okBtn.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.okBtn.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.okBtn.Location = new System.Drawing.Point(974, 572);
            this.okBtn.Margin = new System.Windows.Forms.Padding(4);
            this.okBtn.Name = "okBtn";
            this.okBtn.Size = new System.Drawing.Size(100, 28);
            this.okBtn.TabIndex = 10;
            this.okBtn.Text = "Ok";
            this.okBtn.UseVisualStyleBackColor = true;
            this.okBtn.Visible = false;
            this.okBtn.Click += new System.EventHandler(this.okBtn_Click);
            // 
            // tourCodeTextBox
            // 
            this.tourCodeTextBox.DataBindings.Add(new System.Windows.Forms.Binding("Text", this.toursBindingSource, "TourCode", true));
            this.tourCodeTextBox.Location = new System.Drawing.Point(165, 41);
            this.tourCodeTextBox.Name = "tourCodeTextBox";
            this.tourCodeTextBox.Size = new System.Drawing.Size(429, 22);
            this.tourCodeTextBox.TabIndex = 0;
            // 
            // descriptionTextBox
            // 
            this.descriptionTextBox.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.descriptionTextBox.DataBindings.Add(new System.Windows.Forms.Binding("Text", this.toursBindingSource, "Notes", true));
            this.descriptionTextBox.Location = new System.Drawing.Point(165, 157);
            this.descriptionTextBox.Margin = new System.Windows.Forms.Padding(4);
            this.descriptionTextBox.MaxLength = 2000;
            this.descriptionTextBox.Multiline = true;
            this.descriptionTextBox.Name = "descriptionTextBox";
            this.descriptionTextBox.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.descriptionTextBox.Size = new System.Drawing.Size(1003, 83);
            this.descriptionTextBox.TabIndex = 8;
            // 
            // arrivalDatePicker
            // 
            this.arrivalDatePicker.DataBindings.Add(new System.Windows.Forms.Binding("Value", this.toursBindingSource, "ArrivalDate", true));
            this.arrivalDatePicker.Location = new System.Drawing.Point(165, 69);
            this.arrivalDatePicker.Name = "arrivalDatePicker";
            this.arrivalDatePicker.Size = new System.Drawing.Size(276, 22);
            this.arrivalDatePicker.TabIndex = 1;
            // 
            // departureDatePicker
            // 
            this.departureDatePicker.DataBindings.Add(new System.Windows.Forms.Binding("Value", this.toursBindingSource, "DepartureDate", true));
            this.departureDatePicker.Location = new System.Drawing.Point(165, 99);
            this.departureDatePicker.Name = "departureDatePicker";
            this.departureDatePicker.Size = new System.Drawing.Size(276, 22);
            this.departureDatePicker.TabIndex = 2;
            // 
            // coordinatorComboBox
            // 
            this.coordinatorComboBox.DataBindings.Add(new System.Windows.Forms.Binding("SelectedValue", this.toursBindingSource, "CoordinatorId", true));
            this.coordinatorComboBox.DataSource = this.coordinatorsBindingSource;
            this.coordinatorComboBox.DisplayMember = "UserFullName";
            this.coordinatorComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.coordinatorComboBox.FormattingEnabled = true;
            this.coordinatorComboBox.Location = new System.Drawing.Point(165, 127);
            this.coordinatorComboBox.Name = "coordinatorComboBox";
            this.coordinatorComboBox.Size = new System.Drawing.Size(429, 24);
            this.coordinatorComboBox.TabIndex = 3;
            this.coordinatorComboBox.ValueMember = "CoordinatorId";
            // 
            // coordinatorsBindingSource
            // 
            this.coordinatorsBindingSource.DataMember = "Coordinator";
            this.coordinatorsBindingSource.DataSource = this.tourunDataSet;
            // 
            // allotmentNumericUpDown
            // 
            this.allotmentNumericUpDown.DataBindings.Add(new System.Windows.Forms.Binding("Value", this.toursBindingSource, "Allotment", true));
            this.allotmentNumericUpDown.Location = new System.Drawing.Point(846, 96);
            this.allotmentNumericUpDown.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.allotmentNumericUpDown.Name = "allotmentNumericUpDown";
            this.allotmentNumericUpDown.Size = new System.Drawing.Size(199, 22);
            this.allotmentNumericUpDown.TabIndex = 5;
            // 
            // priceAmountNumericUpDown
            // 
            this.priceAmountNumericUpDown.DataBindings.Add(new System.Windows.Forms.Binding("Value", this.toursBindingSource, "PriceAmount", true));
            this.priceAmountNumericUpDown.DecimalPlaces = 2;
            this.priceAmountNumericUpDown.Location = new System.Drawing.Point(846, 124);
            this.priceAmountNumericUpDown.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.priceAmountNumericUpDown.Name = "priceAmountNumericUpDown";
            this.priceAmountNumericUpDown.Size = new System.Drawing.Size(199, 22);
            this.priceAmountNumericUpDown.TabIndex = 6;
            // 
            // tabControl
            // 
            this.tabControl.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControl.Controls.Add(this.vehiclesTabPage);
            this.tabControl.Controls.Add(this.attractionsTabPage);
            this.tabControl.Controls.Add(this.accommodationsTabPage);
            this.tabControl.Location = new System.Drawing.Point(12, 260);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new System.Drawing.Size(1174, 300);
            this.tabControl.TabIndex = 9;
            // 
            // vehiclesTabPage
            // 
            this.vehiclesTabPage.Controls.Add(this.vehiclesGrid);
            this.vehiclesTabPage.Location = new System.Drawing.Point(4, 25);
            this.vehiclesTabPage.Name = "vehiclesTabPage";
            this.vehiclesTabPage.Padding = new System.Windows.Forms.Padding(3);
            this.vehiclesTabPage.Size = new System.Drawing.Size(1166, 271);
            this.vehiclesTabPage.TabIndex = 0;
            this.vehiclesTabPage.Text = "Οχήματα";
            this.vehiclesTabPage.UseVisualStyleBackColor = true;
            // 
            // vehiclesGrid
            // 
            this.vehiclesGrid.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.vehiclesGrid.DataSource = this.tourVehiclesBindingSource;
            this.vehiclesGrid.GridLineStyle = Janus.Windows.GridEX.GridLineStyle.Solid;
            this.vehiclesGrid.GroupByBoxVisible = false;
            vehiclesGrid_Layout_0.DataSource = this.tourVehiclesBindingSource;
            vehiclesGrid_Layout_0.IsCurrentLayout = true;
            vehiclesGrid_Layout_0.Key = "Simple";
            vehiclesGrid_Layout_0.LayoutString = resources.GetString("vehiclesGrid_Layout_0.LayoutString");
            this.vehiclesGrid.Layouts.AddRange(new Janus.Windows.GridEX.GridEXLayout[] {
            vehiclesGrid_Layout_0});
            this.vehiclesGrid.Location = new System.Drawing.Point(7, 7);
            this.vehiclesGrid.Margin = new System.Windows.Forms.Padding(4);
            this.vehiclesGrid.Name = "vehiclesGrid";
            this.vehiclesGrid.OfficeColorScheme = Janus.Windows.GridEX.OfficeColorScheme.Blue;
            this.vehiclesGrid.SaveSettings = true;
            this.vehiclesGrid.SettingsKey = "TourVehiclesGrid";
            this.vehiclesGrid.Size = new System.Drawing.Size(1022, 257);
            this.vehiclesGrid.TabIndex = 49;
            this.vehiclesGrid.VisualStyle = Janus.Windows.GridEX.VisualStyle.Office2010;
            // 
            // attractionsTabPage
            // 
            this.attractionsTabPage.Controls.Add(this.attractionsGrid);
            this.attractionsTabPage.Location = new System.Drawing.Point(4, 25);
            this.attractionsTabPage.Name = "attractionsTabPage";
            this.attractionsTabPage.Padding = new System.Windows.Forms.Padding(3);
            this.attractionsTabPage.Size = new System.Drawing.Size(1166, 271);
            this.attractionsTabPage.TabIndex = 1;
            this.attractionsTabPage.Text = "Αξιοθέατα";
            this.attractionsTabPage.UseVisualStyleBackColor = true;
            // 
            // attractionsGrid
            // 
            this.attractionsGrid.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.attractionsGrid.DataSource = this.tourAttractionsBindingSource;
            this.attractionsGrid.GridLineStyle = Janus.Windows.GridEX.GridLineStyle.Solid;
            this.attractionsGrid.GroupByBoxVisible = false;
            attractionsGrid_Layout_0.DataSource = this.tourAttractionsBindingSource;
            attractionsGrid_Layout_0.IsCurrentLayout = true;
            attractionsGrid_Layout_0.Key = "Simple";
            attractionsGrid_Layout_0.LayoutString = resources.GetString("attractionsGrid_Layout_0.LayoutString");
            this.attractionsGrid.Layouts.AddRange(new Janus.Windows.GridEX.GridEXLayout[] {
            attractionsGrid_Layout_0});
            this.attractionsGrid.Location = new System.Drawing.Point(7, 7);
            this.attractionsGrid.Margin = new System.Windows.Forms.Padding(4);
            this.attractionsGrid.Name = "attractionsGrid";
            this.attractionsGrid.OfficeColorScheme = Janus.Windows.GridEX.OfficeColorScheme.Blue;
            this.attractionsGrid.SaveSettings = true;
            this.attractionsGrid.SettingsKey = "TourAttractionsGrid";
            this.attractionsGrid.Size = new System.Drawing.Size(1022, 257);
            this.attractionsGrid.TabIndex = 50;
            this.attractionsGrid.VisualStyle = Janus.Windows.GridEX.VisualStyle.Office2010;
            // 
            // accommodationsTabPage
            // 
            this.accommodationsTabPage.Controls.Add(this.accommodationsGrid);
            this.accommodationsTabPage.Location = new System.Drawing.Point(4, 25);
            this.accommodationsTabPage.Name = "accommodationsTabPage";
            this.accommodationsTabPage.Padding = new System.Windows.Forms.Padding(3);
            this.accommodationsTabPage.Size = new System.Drawing.Size(1166, 271);
            this.accommodationsTabPage.TabIndex = 2;
            this.accommodationsTabPage.Text = "Διαμονή";
            this.accommodationsTabPage.UseVisualStyleBackColor = true;
            // 
            // accommodationsGrid
            // 
            this.accommodationsGrid.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.accommodationsGrid.DataSource = this.tourAccommodationsBindingSource;
            this.accommodationsGrid.GridLineStyle = Janus.Windows.GridEX.GridLineStyle.Solid;
            this.accommodationsGrid.GroupByBoxVisible = false;
            accommodationsGrid_Layout_0.DataSource = this.tourAccommodationsBindingSource;
            accommodationsGrid_Layout_0.IsCurrentLayout = true;
            accommodationsGrid_Layout_0.Key = "Simple";
            accommodationsGrid_Layout_0.LayoutString = resources.GetString("accommodationsGrid_Layout_0.LayoutString");
            this.accommodationsGrid.Layouts.AddRange(new Janus.Windows.GridEX.GridEXLayout[] {
            accommodationsGrid_Layout_0});
            this.accommodationsGrid.Location = new System.Drawing.Point(7, 7);
            this.accommodationsGrid.Margin = new System.Windows.Forms.Padding(4);
            this.accommodationsGrid.Name = "accommodationsGrid";
            this.accommodationsGrid.OfficeColorScheme = Janus.Windows.GridEX.OfficeColorScheme.Blue;
            this.accommodationsGrid.SaveSettings = true;
            this.accommodationsGrid.SettingsKey = "TourAccommodationsGrid";
            this.accommodationsGrid.Size = new System.Drawing.Size(1022, 257);
            this.accommodationsGrid.TabIndex = 51;
            this.accommodationsGrid.VisualStyle = Janus.Windows.GridEX.VisualStyle.Office2010;
            // 
            // statusComboBox
            // 
            this.statusComboBox.DataBindings.Add(new System.Windows.Forms.Binding("Text", this.toursBindingSource, "Status", true));
            this.statusComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.statusComboBox.FormattingEnabled = true;
            this.statusComboBox.Items.AddRange(new object[] {
            "Confirmed",
            "Canceled",
            "Pending"});
            this.statusComboBox.Location = new System.Drawing.Point(846, 41);
            this.statusComboBox.Name = "statusComboBox";
            this.statusComboBox.Size = new System.Drawing.Size(322, 24);
            this.statusComboBox.TabIndex = 7;
            // 
            // paxBookedNumericUpDown
            // 
            this.paxBookedNumericUpDown.DataBindings.Add(new System.Windows.Forms.Binding("Value", this.toursBindingSource, "PaxBooked", true));
            this.paxBookedNumericUpDown.Location = new System.Drawing.Point(846, 71);
            this.paxBookedNumericUpDown.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.paxBookedNumericUpDown.Name = "paxBookedNumericUpDown";
            this.paxBookedNumericUpDown.Size = new System.Drawing.Size(199, 22);
            this.paxBookedNumericUpDown.TabIndex = 4;
            // 
            // TourForm
            // 
            this.AcceptButton = this.okBtn;
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.cancelBtn;
            this.ClientSize = new System.Drawing.Size(1198, 608);
            this.Controls.Add(this.paxBookedNumericUpDown);
            this.Controls.Add(this.paxBookedLabel);
            this.Controls.Add(this.statusComboBox);
            this.Controls.Add(this.tabControl);
            this.Controls.Add(statusLabel);
            this.Controls.Add(this.priceAmountNumericUpDown);
            this.Controls.Add(priceAmountLabel);
            this.Controls.Add(this.allotmentNumericUpDown);
            this.Controls.Add(this.allotmentLabel);
            this.Controls.Add(this.coordinatorComboBox);
            this.Controls.Add(coordinatorLabel);
            this.Controls.Add(this.departureDatePicker);
            this.Controls.Add(this.departureDateLabel);
            this.Controls.Add(this.arrivalDatePicker);
            this.Controls.Add(this.arrivalDateLabel);
            this.Controls.Add(this.descriptionTextBox);
            this.Controls.Add(descriptionLabel);
            this.Controls.Add(this.tourCodeTextBox);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.okBtn);
            this.Controls.Add(tourIDLabel);
            this.Controls.Add(this.cancelBtn);
            this.Controls.Add(this.tourIDTextBox);
            this.Controls.Add(this.tourCodeLabel);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label23);
            this.Controls.Add(this.LeftRebar1);
            this.Controls.Add(this.RightRebar1);
            this.Controls.Add(this.TopRebar1);
            this.Controls.Add(this.BottomRebar1);
            this.KeyPreview = true;
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "TourForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Περιήγηση";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.TourForm_FormClosing);
            this.Load += new System.EventHandler(this.TourForm_Load);
            this.Shown += new System.EventHandler(this.TourForm_Shown);
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.TourForm_KeyDown);
            ((System.ComponentModel.ISupportInitialize)(this.tourVehiclesBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.toursBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tourunDataSet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tourAttractionsBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tourAccommodationsBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.errorProvider)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.commandManager)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BottomRebar1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.toolbar)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.LeftRebar1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.RightRebar1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.TopRebar1)).EndInit();
            this.TopRebar1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.coordinatorsBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.allotmentNumericUpDown)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.priceAmountNumericUpDown)).EndInit();
            this.tabControl.ResumeLayout(false);
            this.vehiclesTabPage.ResumeLayout(false);
            ((System.Configuration.IPersistComponentSettings)(this.vehiclesGrid)).LoadComponentSettings();
            ((System.ComponentModel.ISupportInitialize)(this.vehiclesGrid)).EndInit();
            this.attractionsTabPage.ResumeLayout(false);
            ((System.Configuration.IPersistComponentSettings)(this.attractionsGrid)).LoadComponentSettings();
            ((System.ComponentModel.ISupportInitialize)(this.attractionsGrid)).EndInit();
            this.accommodationsTabPage.ResumeLayout(false);
            ((System.Configuration.IPersistComponentSettings)(this.accommodationsGrid)).LoadComponentSettings();
            ((System.ComponentModel.ISupportInitialize)(this.accommodationsGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.paxBookedNumericUpDown)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.TextBox tourIDTextBox;
        private System.Windows.Forms.ErrorProvider errorProvider;
        private Janus.Windows.UI.CommandBars.UIRebar LeftRebar1;
        private Janus.Windows.UI.CommandBars.UICommandManager commandManager;
        private Janus.Windows.UI.CommandBars.UIRebar BottomRebar1;
        private Janus.Windows.UI.CommandBars.UICommandBar toolbar;
        private Janus.Windows.UI.CommandBars.UICommand SaveAndClose1;
        private Janus.Windows.UI.CommandBars.UICommand SaveAndNew1;
        private Janus.Windows.UI.CommandBars.UICommand Delete1;
        private Janus.Windows.UI.CommandBars.UICommand saveAndCloseCmd;
        private Janus.Windows.UI.CommandBars.UICommand saveAndNewCmd;
        private Janus.Windows.UI.CommandBars.UICommand deleteCmd;
        private Janus.Windows.UI.CommandBars.UIRebar RightRebar1;
        private Janus.Windows.UI.CommandBars.UIRebar TopRebar1;
        private Janus.Windows.Common.VisualStyleManager visualStyleManager;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.BindingSource toursBindingSource;
        private System.Windows.Forms.Button okBtn;
        private System.Windows.Forms.Button cancelBtn;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox tourCodeTextBox;
        private System.Windows.Forms.TextBox descriptionTextBox;
        private System.Windows.Forms.DateTimePicker arrivalDatePicker;
        private System.Windows.Forms.DateTimePicker departureDatePicker;
        private System.Windows.Forms.ComboBox coordinatorComboBox;
        private System.Windows.Forms.NumericUpDown allotmentNumericUpDown;
        private System.Windows.Forms.NumericUpDown priceAmountNumericUpDown;
        private System.Windows.Forms.TabControl tabControl;
        private System.Windows.Forms.TabPage vehiclesTabPage;
        private System.Windows.Forms.TabPage attractionsTabPage;
        private System.Windows.Forms.TabPage accommodationsTabPage;
        private Janus.Windows.GridEX.GridEX vehiclesGrid;
        private Janus.Windows.GridEX.GridEX attractionsGrid;
        private Janus.Windows.GridEX.GridEX accommodationsGrid;
        private TourunDataSet tourunDataSet;
        private System.Windows.Forms.BindingSource coordinatorsBindingSource;
        private System.Windows.Forms.BindingSource tourVehiclesBindingSource;
        private System.Windows.Forms.BindingSource tourAttractionsBindingSource;
        private System.Windows.Forms.BindingSource tourAccommodationsBindingSource;
        private System.Windows.Forms.ComboBox statusComboBox;
        private System.Windows.Forms.NumericUpDown paxBookedNumericUpDown;
        private System.Windows.Forms.Label tourCodeLabel;
        private System.Windows.Forms.Label arrivalDateLabel;
        private System.Windows.Forms.Label departureDateLabel;
        private System.Windows.Forms.Label allotmentLabel;
        private System.Windows.Forms.Label paxBookedLabel;
    }
}