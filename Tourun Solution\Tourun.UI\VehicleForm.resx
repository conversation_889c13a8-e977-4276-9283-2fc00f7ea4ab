<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="vehicleIDLabel.GenerateMember" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="typeLabel.GenerateMember" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="makeLabel.GenerateMember" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="modelLabel.GenerateMember" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="plateNumberLabel.GenerateMember" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="capacityLabel.GenerateMember" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="providerCompanyLabel.GenerateMember" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="contactPhoneLabel.GenerateMember" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="vehiclesBindingSource.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="tourunDataSet.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>225, 17</value>
  </metadata>
  <metadata name="tourunDataSet.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>225, 17</value>
  </metadata>
  <metadata name="visualStyleManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>381, 17</value>
  </metadata>
  <metadata name="errorProvider.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>569, 17</value>
  </metadata>
  <metadata name="commandManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>716, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="saveAndCloseCmd.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        EQAACxEBf2RfkQAABwNJREFUaEPt2ElsG9cdBvAcjbaXnIwGielKNiW5sS25xx5667WnbnFvbZEuaVG3
        RtG0TZruTlA3XdBDncaOJVumNmqjRImkTDuxndiWJZEUdw7XmeEsHIoUFS8DA1/x3pDScDiFqhEIqIX+
        wAdJF+n76b03b6Rnntmf/dmf/fm/mC/4MwdOLaz9rm+h7Onzlb0k3RNSpNMhKB0k14rKN2byNR9TVhdI
        0tpHH6OovlRJ9SZl1ZOQ1fmEpLpjojobFdSZSFF1hXl1apVTJ4MFdTxQUJ0rOXV0OauOPEirQ4uM+u3p
        WOGYk/P2TEl/+NwU9wljrx0PQZxaWEOfrx5vGV3jImxX+M284s2jWq2hur5RTw2Vag1rlXWU16pQyhWU
        lDVIchmCVEJRkMEVJbCcgDxbRDbPIZ0tIJXOI57KIppM4+xcDD1OFj2TEnomRf+J+eInjd12NKd8ZZ8e
        YQrx5Gnp5lSbEaUyRElBUSyBFyS84riDly7ewNcv3sDX3vXDfT+GZDqHeCqDaILBWXcM3WN1yISE7old
        Yvp8Zb8e0UsgzmbI9z05WpimXr4BkHUIgSLIaojofdOFg78c20z/zRASTBaxZAaROIOfuKMahCLqGRc+
        ODYsfsrY8b8aCtEhej2tkO/N5yCX1mjxRnnyNQFIsmJASGB5ASdbIMH6aqQRjqc0yCirQ4g0dqfot3Rm
        +jwKhTQQppC5LERZoaUbIV9TADkToky3k4YQUWCLLZDLNwKbq7EaS+LHs1F0NSB1BNnS5GfbneLvjT23
        HQLRI8wg353L0N+4PuQsaAAZfFECx4ubhztX4HHyXDPkPf/K5mqEooktiAFBcnRUXDD23HZ6CUSHODmv
        tEC+487Q33hTSPkGgKyCDpHJcy2QS/4VbTWiSQQjcZyZiaBrRIPoEfYxEUfHRL+x57bTO78FIQhzSJqW
        NYaU1wOyeR6ZHEsftSfPTTdBLi4s0y0ViiQQCMdwxhWmECNCgwjWIQ0ECflmesjLs2la1iybAHJX5Fgw
        5L7I5Fsg7/qW6JYKhONYWY3izPQq7ARiQNjHBBwdtQjRI07MtUK+5WLopUYKayGfc7Q8XQEdgNwV5DH7
        8sD7+Mo7C/gyyQUfxm8HEKSIGJaCEfyAQIY1iB5hH7UIOaGDEASFjEpNkM8PMkim87QwTVbbPqQ8k9ED
        yIWn3RXkYGtPKO1wbyJCESwGwvjiUAT2Ec4MsTtIA3HCraBnQm6C2PoL+Op4Ev33Mhh6UM8iSZrGcZ/B
        tXsMBmlSuHo3iSsfJTDwIUkcl2/HcPlWFJduRfDPmxGcHouiY4D5TwgcHbYI0SNIjrtL6HJK6HQI6LhW
        1HKVw+GBPA4P5LT07zy2ejoHWVrcFDFiEXJ8rg7RIY7PbuXFmXpcJXzWJWuZ1nKMZIpEotFeABvvTs03
        ttnTyRQxIuDIMG8RspcQI0UcGbICmdUgewYxvAvInkIMF9FhDVLy7yVE55BVyMwWZC8gdg3ZKwgKcViE
        mCGOu2S8l/oY3IaK0sdPIG88hlR7RCOuP0SxsgF+rQauvA5WqaJQqiAnlZEVFWSEEtJFGQwvIcUJSLEC
        4nkesRyHaJbFElPA+bss7HWIHtHpKOIzDm7nkBcbEMNKnFms4unTp1BVFU+ePMHjx4/x6NEjPHz4EBsb
        G6jValhfX0e1WkWlUkG5XIaiKCiVSpBlGZIkQRRFCIIAnufBcRxYlkWhUEA+n0cul8M3fVwLosPB7wJi
        sp3+HttoKyKbzeLXtwotCAoZtAJxlfxGBDkTf4ustxWRyWTwxgf5FkTHNYuQYy5JgxgONoG0E8EwDH71
        fq4FQSFXrUJMnk5/DVfbikilUhTSghi0CpnSIMZH7F9Wq21FJJNJvH4z24rYDcSIIHfE26FKWxGJREKD
        GBGDPA5bgfQQiMll9+fgWlsR8Xgcr/kzZgiLkEkCab2xCaSdiFgsRiEmCKsQkUKMrx3nA+W2IqLRKH5x
        PW2GwOErFiFGBIWsKG1FhMNh/HyBMUPg0G4gHRdieOH8Io70M/Tl70/LpbYiVldXKYSUf/5CAp/+4108
        //YKbLuCTEg4+DMPnn3pHdjeuk0hby3JbUWEQiG86k1SCEE8e/pfOPijCQ3Sz+4c0j0h+chWOvTmHTx3
        dgKd/1imr+LnHshtRQSDQfzUo0HIShDEc2/4YRtgyb+ffMae2073uPhbs78nTl+X2ooIBAL40mSWngmy
        CjQEMcDihf7Cb4w9tx2bP3OAYLrGhfkup+htxD4mel+/K4a9SUG+niwqCwle8cU5xRtlFU+kQDMfzinu
        UJZmJpBWXCsMzfRySplaSiqTDxI0zvsxZexelGb0o4jiuBOWf+hOhm0DnHcrBe+h/sI8QdguZQ4Ye+7P
        /uzP/vxvzr8BmwVyY4+GAK0AAAAASUVORK5CYII=
</value>
  </data>
  <data name="deleteCmd.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAACxIAAAsSAdLdfvwAAAOaSURBVGhD7dZNTxNBHAbw5YyJFy4eFPpCW6AttF6Ub2C8
        SEj8CmK8mBiB9mC9+QnEe7FQSltLaYFKe5MzmCgsiInat+122xrxSBwzOx1Yprt0OzvloH2SJ4EEZn6d
        THf/HNdLL73855nbvd83u1fom90DHfX5bp6b/XiPXO7KAyEtON3d/UGu1/0EPl3j5g9ucz7+Iefnn3Lz
        nwE3vw843wHg/LB8s4caPx+gv/XtA/l/4RpwLbjms71+cjtjmft6nfMfTXP+w1ecn89wfv47wnSr/B/O
        d/gN7QX3PJqWDdTx8futm1xxoYE6fj7dsuBV18enSFbH8WTrx95cA7SrJ4tbB57tOpjAfX/e8QxsDXWr
        Bty4m6iuTQm4NiTgSktfSAd1vNnGDontGrxZZ6r6gXRQx5Orx0m09wytDR+X4U20XngatxojHdTx5hpv
        VOHb3YDD05fAaEpcIB3U8eQaL2ngZ+gO4GOpqtzRpBggHdTxZGsz+JqcwZX32zAcoeWuo44mq49IB3W8
        mcaUKlz5xVSBu2jgCA9GktUHpIM6E7napCZciWYARxWBIyncJR3UcW3/NF8KV14TBdxJAR9ZQ7WtlU2k
        gzruTLm/LVx52gbgchMigHuSDkMZ36z9bgtXotPnTxR1uKgKdyREYE+IJ+T+huPeko51wRWPwk7hct9V
        gD0usBsjcNwb0o5uuBLdAVzGow/AbozAcaWrcXX4ZafdvN+64ai2mMBujMBxpqWFjuFKtA44wlfAcFR4
        Te5vOM6UFKCCK9Ft4LaYgBoVXpD7G44zWZ2hhjfROuDw9MFwRGA3RuA416UpWjiJ1oTDrsq/sxsjcMZS
        lUnDcBmtDYe1rgrAEmE4RuC4UoK5s0chedpNeFQLXgbWCKotwnCMwIGvdmbwJpqEw1pWysAdZDxG4DjW
        xBMaOHnaWnBYc7j0i9yXWRwJ8bg9XPt+y3AFWgmXGy4D03KJ/RiB44gLOzRw+MW8AFeim3BzuCTXtFRi
        P0bg2OOVODN4+CLcvIw6tFRkP0bg2GPCAg2cPG0LgYY1wS7JH4D9GIFjiwoB5vAlZYtgKFRgP0bgWOPC
        DA2cvCYt8BCE4+bZjxE4wzFhSuvloweOr4kq/C3qYLDIfozAga/4rsFhF2Hzd8h9mcUWKg5YI6VTWvj5
        NVGDF8BgMH96I1QcIPdlGstK+YllpVzXD1febw34YgHcChZqN4OFx+R+vfTSyz+ev+El3Kj2XrBGAAAA
        AElFTkSuQmCC
</value>
  </data>
</root>