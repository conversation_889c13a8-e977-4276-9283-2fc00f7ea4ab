-- Database: <PERSON>un (SQL Server)

-- Create database (skip if your environment creates it separately)
IF DB_ID(N'Tourun') IS NULL
BEGIN
    CREATE DATABASE Tourun;
END

-- Use database (PostgreSQL users: replace with \c Tourun; SQL Server users: USE Tourun;)
USE Tourun;

-- =============================
-- Core reference tables
-- =============================

-- Roles: Application roles (e.g., admin, user)
CREATE TABLE Roles (
    RoleId            UNIQUEIDENTIFIER NOT NULL DEFAULT NEWSEQUENTIALID() PRIMARY KEY,
    Name              VARCHAR(100) NOT NULL UNIQUE,
    Description       VARCHAR(500),
    CreatedAt         DATETIME2(0) NOT NULL DEFAULT SYSUTCDATETIME()
);

-- Users: Application users
CREATE TABLE Users (
    UserId            UNIQUEIDENTIFIER NOT NULL DEFAULT NEWSEQUENTIALID() PRIMARY KEY,
    Username          VARCHAR(100) NOT NULL UNIQUE,
    Email             VARCHAR(255) NOT NULL UNIQUE,
    PasswordHash      VARCHAR(255) NOT NULL,
    FirstName         VARCHAR(100) NOT NULL,
    LastName          VARCHAR(100) NOT NULL,
    Phone             VARCHAR(50),
    IsActive          BIT NOT NULL DEFAULT 1,
    CreatedAt         DATETIME2(0) NOT NULL DEFAULT SYSUTCDATETIME(),
    UpdatedAt         DATETIME2(0) NOT NULL DEFAULT SYSUTCDATETIME()
);

-- Users <-> Roles: Many-to-many
CREATE TABLE UserRoles (
    UserId            UNIQUEIDENTIFIER NOT NULL,
    RoleId            UNIQUEIDENTIFIER NOT NULL,
    AssignedAt        DATETIME2(0) NOT NULL DEFAULT SYSUTCDATETIME(),
    PRIMARY KEY (UserId, RoleId),
    CONSTRAINT FkUserRolesUser    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE,
    CONSTRAINT FkUserRolesRole    FOREIGN KEY (RoleId) REFERENCES Roles(RoleId) ON DELETE CASCADE
);

-- Coordinator: Person responsible for a Tour. Tied to a user account.
CREATE TABLE Coordinator (
    CoordinatorId     UNIQUEIDENTIFIER NOT NULL DEFAULT NEWSEQUENTIALID() PRIMARY KEY,
    UserId            UNIQUEIDENTIFIER NOT NULL UNIQUE,
    HireDate          DATE,
    Notes             VARCHAR(1000),
    CreatedAt         DATETIME2(0) NOT NULL DEFAULT SYSUTCDATETIME(),
    CONSTRAINT FkCoordinatorUser FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE
);

-- =============================
-- Domain entities
-- =============================

-- Tour: A tour of tourists
CREATE TABLE Tour (
    TourId            UNIQUEIDENTIFIER NOT NULL DEFAULT NEWSEQUENTIALID() PRIMARY KEY,
    Name              VARCHAR(200) NOT NULL,
    Description       VARCHAR(2000),
    StartDate         DATE NOT NULL,
    EndDate           DATE NOT NULL,
    CoordinatorId     UNIQUEIDENTIFIER NULL,
    MaxParticipants   INT,
    PriceCurrency     CHAR(3),
    PriceAmount       DECIMAL(12,2),
    Status            VARCHAR(50) NOT NULL DEFAULT 'planned',
    CreatedAt         DATETIME2(0) NOT NULL DEFAULT SYSUTCDATETIME(),
    UpdatedAt         DATETIME2(0) NOT NULL DEFAULT SYSUTCDATETIME(),
    CONSTRAINT FkTourCoordinator FOREIGN KEY (CoordinatorId) REFERENCES Coordinator(CoordinatorId) ON DELETE SET NULL,
    CONSTRAINT ChkTourDates CHECK (EndDate >= StartDate)
);

-- Accommodation: Places where tourists will stay
CREATE TABLE Accommodation (
    AccommodationId   UNIQUEIDENTIFIER NOT NULL DEFAULT NEWSEQUENTIALID() PRIMARY KEY,
    Name              VARCHAR(200) NOT NULL,
    Type              VARCHAR(100), -- e.g., hotel, hostel, apartment
    AddressLine1      VARCHAR(200),
    AddressLine2      VARCHAR(200),
    City              VARCHAR(100),
    StateRegion       VARCHAR(100),
    PostalCode        VARCHAR(20),
    Country           VARCHAR(100),
    Phone             VARCHAR(50),
    Email             VARCHAR(255),
    Website           VARCHAR(255),
    Rating            DECIMAL(3,2), -- 0.00 to 5.00
    CreatedAt         DATETIME2(0) NOT NULL DEFAULT SYSUTCDATETIME()
);

-- Vehicles: Transportation vehicles
CREATE TABLE Vehicles (
    VehicleId         UNIQUEIDENTIFIER NOT NULL DEFAULT NEWSEQUENTIALID() PRIMARY KEY,
    Type              VARCHAR(100) NOT NULL, -- car, van, bus
    Make              VARCHAR(100),
    Model             VARCHAR(100),
    PlateNumber       VARCHAR(50) NOT NULL UNIQUE,
    Capacity          INT,
    ProviderCompany   VARCHAR(200),
    ContactPhone      VARCHAR(50),
    CreatedAt         DATETIME2(0) NOT NULL DEFAULT SYSUTCDATETIME()
);

-- Attractions: Places/POIs that tourists will visit
CREATE TABLE Attractions (
    AttractionId      UNIQUEIDENTIFIER NOT NULL DEFAULT NEWSEQUENTIALID() PRIMARY KEY,
    Name              VARCHAR(200) NOT NULL,
    Type              VARCHAR(100), -- museum, restaurant, viewpoint, etc.
    Description       VARCHAR(2000),
    AddressLine1      VARCHAR(200),
    AddressLine2      VARCHAR(200),
    City              VARCHAR(100),
    StateRegion       VARCHAR(100),
    PostalCode        VARCHAR(20),
    Country           VARCHAR(100),
    Latitude          DECIMAL(9,6),
    Longitude         DECIMAL(9,6),
    Website           VARCHAR(255),
    CreatedAt         DATETIME2(0) NOT NULL DEFAULT SYSUTCDATETIME()
);

-- =============================
-- Relationship/link tables
-- =============================

-- Tour <-> Accommodation (many-to-many) with stay window
CREATE TABLE TourAccommodation (
    TourId            UNIQUEIDENTIFIER NOT NULL,
    AccommodationId   UNIQUEIDENTIFIER NOT NULL,
    CheckIn           DATE NOT NULL,
    CheckOut          DATE NOT NULL,
    Notes             VARCHAR(1000),
    PRIMARY KEY (TourId, AccommodationId, CheckIn),
    CONSTRAINT FkTourAccommodationTour           FOREIGN KEY (TourId) REFERENCES Tour(TourId) ON DELETE CASCADE,
    CONSTRAINT FkTourAccommodationAccommodation  FOREIGN KEY (AccommodationId) REFERENCES Accommodation(AccommodationId) ON DELETE NO ACTION,
    CONSTRAINT ChkTourAccommodationDates CHECK (CheckOut >= CheckIn)
);

-- Tour <-> Vehicles (many-to-many) with assignment window
CREATE TABLE TourVehicle (
    TourId            UNIQUEIDENTIFIER NOT NULL,
    VehicleId         UNIQUEIDENTIFIER NOT NULL,
    AssignedFrom      DATE,
    AssignedTo        DATE,
    DriverName        VARCHAR(200),
    Notes             VARCHAR(1000),
    PRIMARY KEY (TourId, VehicleId),
    CONSTRAINT FkTourVehicleTour     FOREIGN KEY (TourId) REFERENCES Tour(TourId) ON DELETE CASCADE,
    CONSTRAINT FkTourVehicleVehicle  FOREIGN KEY (VehicleId) REFERENCES Vehicles(VehicleId) ON DELETE NO ACTION,
    CONSTRAINT ChkTourVehicleDates CHECK (AssignedTo IS NULL OR AssignedFrom IS NULL OR AssignedTo >= AssignedFrom)
);

-- Tour <-> Attractions (many-to-many) with schedule
CREATE TABLE TourAttraction (
    TourId            UNIQUEIDENTIFIER NOT NULL,
    AttractionId      UNIQUEIDENTIFIER NOT NULL,
    VisitDateTime     DATETIME2(0),
    SequenceNumber    SMALLINT, -- order in itinerary
    Notes             VARCHAR(1000),
    PRIMARY KEY (TourId, AttractionId),
    CONSTRAINT FkTourAttractionTour        FOREIGN KEY (TourId) REFERENCES Tour(TourId) ON DELETE CASCADE,
    CONSTRAINT FkTourAttractionAttraction  FOREIGN KEY (AttractionId) REFERENCES Attractions(AttractionId) ON DELETE NO ACTION
);

-- =============================
-- Indexes to support lookups
-- =============================

CREATE INDEX IdxTourDates ON Tour(StartDate, EndDate);
CREATE INDEX IdxTourCoordinator ON Tour(CoordinatorId);
CREATE INDEX IdxAccommodationCity ON Accommodation(City, Country);
CREATE INDEX IdxAttractionsCity ON Attractions(City, Country);
CREATE INDEX IdxTourAccommodationCheckIn ON TourAccommodation(CheckIn);
CREATE INDEX IdxTourVehicleAssignedFrom ON TourVehicle(AssignedFrom);
CREATE INDEX IdxTourAttractionSequence ON TourAttraction(TourId, SequenceNumber);

-- =============================
-- ID generation notes
-- =============================
-- For SQL Server, replace BIGINT PKs with BIGINT IDENTITY(1,1) PRIMARY KEY
-- For PostgreSQL, replace with BIGSERIAL PRIMARY KEY
-- For MySQL, replace with BIGINT AUTO_INCREMENT PRIMARY KEY


