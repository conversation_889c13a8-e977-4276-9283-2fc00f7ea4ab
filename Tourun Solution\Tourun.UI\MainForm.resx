﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="vehiclesBindingSource.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="tourunDataSet.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>381, 17</value>
  </metadata>
  <metadata name="toursBindingSource.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>537, 17</value>
  </metadata>
  <metadata name="accommodationsBindingSource.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>726, 17</value>
  </metadata>
  <metadata name="attractionsBindingSource.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>1237, 17</value>
  </metadata>
  <metadata name="coordinatorsBindingSource.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>997, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="ribbon.HelpButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAS
        cQAAEnEB89x6jgAAAY9JREFUOE99k7FLw0AYxfNX1bVLCipCt0KhtUNLQVspGLSTTSVKKlKjdehmBAcn
        KdwiVOFIKVSkcg6CY0fHjG5P7i5nL2n0gzck5Pfe+46LYaTMjjdH4XgG0yIw9wkK3THq/Qmcu3ckv43N
        7uUcphWg4jJUPIbKMJLHkLcZzAZB/WySblI7fxVga8i1EPJGUvydMBRGFAWHxk0aV/MIlqBPFwi/w5hG
        s0XMpNQLliY5K1gm+0uYfoaiQRjKZ+8+MnEZMg2CI5/B2Bu8paZzWCXy9N8W/Hxchmw7alF0XsRHykDs
        q+3MtfiSDWzewGXIc9kU69YYRs4iAkiDbW0d/1GDhQFDpkp0g9VktXs8WYlKg6LzLIEErKpTFqbA8gw2
        +Aqt66l4kWagDkyvrdKzLYqSE8AYPHwg1ySpsNpfT+Zwvi3rd26YvAstj7egK8l8Ba4kzNPLTuJKl3uB
        NPmntoILncRVVlO7mGKtScQBCUDtG4GZGsH2yR8/kz7V/hRbh08C4No8GKNyOkH3NtpZmx/Y21lzAJ9L
        2gAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="newTourCommand.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAACnFJREFUaEPt2XlQFFceB3DEI3IqKCCCSqIoHom3aNCQZMVN1LLcuG4M8Ug0hBiN
        SsyhJBFXBEEBUS5HBDEYVwFBQG4MCgKCyC0eGJUBuWa4Bpie7unxu9WjjEz3DIfR1G6V36r37/TnNb/3
        6/ceGhqv8iqv8ucTgYEaSdQCzSTKWTOBPK+bQNzeVCilnUpl2FZM48vrUtm6XIpYmUnx7dKJiwuTOtZp
        uECT/TN/fZIxZkCa1EMzmarRTKIwMIHEwHgJ9OMl+OiaFF8X0dhRJMO2GzJ8c12GLfk0Nl+j4ZhDw+Zi
        h0grVHhA67jQnP2zLz+ZMBqQLuMNSJOSmilSdMcPiiUw6IIYg6M78U4GqRLvcJXGzJh2aIUIoH28gdTm
        NQTp8h6NZD/m5eQS7AdcopsGpNHoCT84qgOzUgiVePsMCrqhQmgHN0Kb1wCdoHpoB9YJdfxq1rAf9+JS
        gMEal2TBRpkyzM2T4b3rND4ooLEgWwrzVIqDHxLRjrcSxRz8piwaUyLalPA6gXXQ9a/FML9azAlvyRp3
        8sFQ9uP/VExSoLOgQFa8/e5j7P3jMfZUPsbPd2Vwvi3DrgoZfiiXYeU1KQzixQr8kLMiLE4nOPgZ59tV
        4nWPPsLCs+34R7wUtmc76+f6tY1gO54rtg8w9Ivbsvp9D9TjvyuT4dsSGRwLaBjFdMrxr51pxeorlAK/
        /ooUk86pfvMM3jJEKMevjJViRYwUNuEdjebefC22p99ZWyG7qQrvWELDJEkCrVgxJqcSsL1MYkUmBbOY
        Tjn+tdMt+GcGJcevSidhEt6sFm/gV4ulMRIFfvl5CksjKViHtpaxPf3KsjIcUYXfUkpj2EUJp+a73jyD
        HxreDOv4DljHdXAWbHe8nm8N5oS3YeUFCsujxFgW0YmlESQ+iKCw5CyF6UFCL7arT1lcCMtdlfRjNp4p
        mwlpveOHnmqC1knhk1bZA17Ppxp25zqx4nQ1vOPc4R3rhg/DqmB3hsDiMxTeCRM/nuzbaMn29Rr7Mlm5
        KvymQloJrxXZjvfTJdh0TYoP0ggYnqrH0BM10App6BNe/+ADmO3PQv7VGaBuasjHlSt2eC+kCe+fpvBu
        OIUZ/i0lbF+PWVyKOb/c4+KZBfuvfFqBN47pxCdXpU9aZa4Un6c2ISTWGYFn7LEo6Cy0j1RC278aOoG1
        avGj92ch58qbCjwzRGVaWBRUJ8e/c4qCTagElt78eWyn2nxcSueqwjPdZluxDBYJBOakEvgqj1b0eYcs
        CfbFRUOWpKEYZdET8P2JnRjrnQld70ro+lZx8LksPDMuXf47FgU3yfELT5J4O4TENG/hVbZTZWwzMMjp
        Fk2rwjOtUt3exiFLinVJTeBdOAAycZDSRIjEwUj4z0J86ueJ4W7F0D9wFyau2ci8PJODL8qZhSXH72DR
        SUKBnx9MYlZgJ63hkjGI7eVkSQHt0G/804/U55elsE8WYfWJU2iIN1CaRNeojjXCPt6XyMzoAR8qVsLP
        O05izjES410b1rG9nHx0g055LvwVGqvTKLwe3gydo3xY+qQjL2oqZwIdCVooyrTsN35WIImJ7oJktpeT
        dUX0o/7iP06nMDOqHXrBjc+6zVE+DD0LEfbriheCn+lPwsqzhc/2cvJlsVTSF/zGLCmsIkXyj5SOulZ5
        mA9dtwps9d+N5ou6KvG3cifD7tgtTs2z8dP9SEz2EhFsLyfbSqSPe8Mzb372hY4+9vn7MHbNRlbGWxz8
        ndyJaE/SwUZeHGxCxD3i3zwiwWSvDhnbywlzDOwNz5SN8a8q9jZHqqDreRt6nnehd+iBHM90m8sZzz5S
        bDxTWlFnPsH8oJYe8VMPMxMQg+3lZEM+9ZiNX5xGYHm6BKt+p/BZphTrL0u5ZXOUj5leMciOnI7I03aw
        9/WAhWt6r3hmiBJ0YeN7D/N4EvV4bwkmHerDBCYkikXd8e+nEkp7m8kRbfgoleSWjfcfcA12fNZxkjXA
        zzXi4JkFu++UF+ikAUrd6afj3pgd0KEe7yXBhAN9KKEh50Tli1IlirKZGC1S2pgZhTXBLp7g1vzTBbvW
        9wAEccMgva4MZ0Zh9kx5t7HhCRF2+iulCeRFWmO6d6NavOVBCd7Y19r7Ih58ti2a2VVaJ4rlNb8mg8Kq
        SxRMn+7ndXgNMDkhUL1gvfjQd7uL8a4pIMo0lfA3sqY9+8KGSjD/aC2yIm0VE2D+Ist8sjD9KKESP95D
        grHOjb230SG/tTp3bYknnhfJD+DMR2pJAsEtGzb+YBWGeT7E7qQaiO4tV+ClRRpIj7CDDa9Z0SqteWLY
        +t7EwwvjFJMICt2CaV6tKvGvu0sw5sf6RLaXE61w4fzuNa8TKsAbv7XALo7Agqh2WIQJYREqhMUJASyO
        N2JMUAOGezPt8gn+pzQBCAkJMdGGsKQ9qE4zkq+HzoSheDegEm+fkCi6DVPzq3xT0JGgLZ9AbZwJprpV
        q8SP2y/B2B0Pe99KMDdmr50S8lUdRqwj2vFxshSrE6VYdVGqOMMuPU9iCk+InSkCtHaSaBeTcIyqhcHP
        N/G3vcEgEwfKgRsPH8NUrwZYHWzERPd6TDnUhOk+rXAK8Fcs6ncP5Mi7DRtvvqdNKr8B7EvkN2YqPlLG
        vAYOvusM63WNxKNWEg0iEl9E1cHApRKGv9yFwY9F8PH/FPUXDDB1ZzRG7iyD0bflMNpRBuPtpRi76x6m
        HBTicLAT/IO3YIJbIwc/xpWA2Xd12Wyn2jDXfU9vzJRq3jCgTiV+/1USN+tJVApIOEQ3KvCGP9/BCOdb
        GLkjFxZOyRjpVKSEN/mmBCZbimHl0QordyEmuglh6Ulw8Xs7MfqLOzPYzh7DXPexF+wIv1oOfncGid/v
        k8jjk3D+nZIfA+eHdGLqEQFM9/6BEbtuYeQPFRj5XblK/KjNRXjDpV5lzcvx/yZgurOuf0dKJvrBfEPt
        gDoBu9ssjyEV+K2pFMJLKcTdIvFt2hN81zGQ6TYz/Fqf4m+qxY9yLMS47++rx/8kkhl9Uzae7etTmLtK
        dqucfbJVjl8bR2F/FoWAfApfJXLxTKuc4d/WK97U4QbMNpepxrsQMN1e/XzXKl3R86051r3PG3tX48MI
        Cp/FUfK3vjZWNZ5plZM8G3rFm24qwOiN12Hh0sLFO9UWsT39TwQG6h+qiunq86N9amF9sgPLzlFYEaEe
        z/R5c+d7fcKP/iwf5tvuK+FH7Wys19jwgi55TV0eaQ/zeJgw3LMKZofrMClQCNtwSY94q0PCPuPN1ufB
        bGNRN7ygbtyGB8PZjj+XiPIh+h4PSwwPVcPEg68WP5fHbLpq+odfew3m9rkw3d0K0x11RS/szavK8H33
        thvsqRQsDCM4+Hk8EhYuVc+FN1+T02i+qWIr+3kvJXq7Kka85d98nbkx6142lu51/cab2edKzNZk+5mv
        zjFkP+elx+qIYPY0n+bsWQEd9DSfVhg7lfcDn8s3/yTX3Xx1jhn7d//6uGCQ8Q8V8422le4y3locafJ1
        UanJ5sImE8cb5CiHAnLUxoIm08/zS0w35EWarc/70fzT/Ln/G/9mfZVX+f/PfwEZR+7dmH/Z3AAAAABJ
        RU5ErkJggg==
</value>
  </data>
  <data name="newAcommodationCommand.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAABdJJREFUaEPtmFtsFFUYx+slIfqmUbtniuAF9EGiERP1QRIxPlgK8UGIMSG+cDGi
        BPGyZ9oCNQqo5UkLNhGjZHdnZ1nuoFVKG62A2y5SoHdKaXfp/Ua33e1lt6Wf+YZOO/vNbKfdAm1I/8kv
        aXZmzvy+2TPfOduEhNncpREttlXcYmtAUi37VtLjMzqc2TdxZr8hMjvcxDbMmf1Let4MDNwjMlvmmDjB
        YvshIyHjXnrVjMjGBd/PES12WSdNsdgOb57rfoBeP60R50kPicxeIDIHTBDPp0x6hI4zLUm3OOZz5ig3
        kIRdS44o0M8RvAavpePd0aQm2haJFkc9lUOyVpyAmoo6uFpVB9krc3THR4poSmP2xXTcOxKeaHtDZPYA
        lUJ+XZsL13z1EAgEoKurC/y+a2DbkKc772YR9qDV4kim49/WcItzNbc4wiKTQEuqIMGBLf9AY2MjdHd3
        QzgcVsC/8bNj355VzqHXceYY5ExaS+9zW8KZtIkz6QaVSHvcCbnZXmhqaoKenh6IRCKjqEXgsVN7vZA+
        X9YVITLHMGfS7VsrViW47xOZtFt/Ywm2Pu2CAmcxNDc3QzAYjBLXgsfwnNPuYti2cL9uHIRbpJ8zEv66
        n95/Stm4IGeOlTndXHAC5asXDsK53DJobW2F3t5eQ3GVgYEBpYiWlhb4L68Mti8+pBsPsQrOoxns+IPU
        I65snut+mAvO0/QmyHevHYUy72Vob2+Hvr4+nbBWXEsoFFKKKPVWwq6lx3TjKkUwqTDV4n6U+kwqnyXa
        nrQKzko6OJL19u9QU1ELnZ2d0N/fr5M2EkfwXASLaGtrg8vlV2DPOzm68RWYVPNFkrSQek0onyc5n7cy
        ZwMXZKD8si4PfHV+pUUayVNprbgWnHL47V2tqYV9H+bp7oNYBblFZNJL1G/ciILrTasgd9PBxCQZDmw5
        DQ0NDXD9+nVFLB5xBKccgkV0dHSAz+eDIzvPgDjXpSuCC3KIJzlTqKdheJLrfc7kCB0kdZ4LTmZ7lX6O
        C5RWnkpPRFwLTicswu/3w8m9RZD2xH5aAE6pIZHJ66lvVKzM9QkX5GEu4FMYY9uzB+HMoYtKH8d+Hgk1
        wlBlOgwXvwtDVVsgHGxQpEt3dkLRurZJU/ZNp/JN4LdaX18PBe7zsPWZA1EON5GHOZM3Ue/RWJkrn160
        ffEROJ9frvRvXKDwiQ+WcxgqSh5lsEJUnm7h+ta4wW8BwSJwinpzS+DrFw/rirAy+RT1Hg1n7nlckP3q
        yZlLjkNpUZXS43FwdXqEC5dB+OzSMQpTblkB2iIuesoh83VssyMFMFej+Jj0FPWOijXR+RwXXP6fVp+C
        KxVXlVanlUf6LnwEfX+/OsaFj0cKaIkbvAcucirY4XDKVlyqguz3TmIBPnSjvjGDLxS+WDg3dS9ooBZC
        59ZCKP8VCJ1bB/2BOuVlLFzfHDeqOE5TFbWI6upqoH6mwQUKpYw6y+7l+ERol4ifzEU5UeLYKFTUIqif
        aVCUiqvQDditgIoj2K7V3xTUzzRG4mof/3HFH/of61Ngz4ocQ3FVPq4CjMSNwHeEou0o9OXUThX61I3E
        EexK1M80M0VchfqZhgpPlziCDYX6mWY88b6AHwYr0pStxGBlOvR2+RTxkh0dum3CRCjZ0R5TXIX6mcZI
        XCVSZo3aSkTKufK06eo6GWKJ41qEUD/TUGntVBnwJEdtJQY8y0YK0K+wEyWWOIK/GaifaYzEVXqLN0Rt
        JULnNyjzm66ukyGWuAr1M42R+OgL2lENQe8aCOa9DD1FayDYcUV5KT0fNMVNLHHciyHUzzSG4iNkpfyp
        2w5MBdxKUHGt/JQKMGqJdBtwK4gljlt5hPqZxkhc7eN7luM/a/VbgnjJSvktpjiC/4KhfqYxEjdagMZb
        hGJ1FTpdxhNXoX6mKSythZkE9TMNHWC6oX6m8ZTW1tNBpgtPSe016mcaz6W65JlQBMr/W1b3FvWbzWxm
        c5fkf/h5i86yk0FgAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="newAttractionCommand.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAABjhJREFUaEPtWOlPVFcU589o0r+h/d4P/dBW0+LSRNuYaBWQfRZmgWEWRHGmi6aA
        OgzLwAAyAyKbW1zikpimtYlWxKUJ/aACCoJRZn+D0Gp+zbnDu8zcGRwkjNpmfskv8zLv3HN+97xzz73v
        ZWVlkEEGGfwvoLbYtqpN1qkysw2vI9loTNYt4vh3CpXZ+onabJ0XxS5P699qi/VT0c/ao3L09yzjX0hg
        5ehvsonSUPPBSjKfhDP6qh8+jA+41hCFxzIrK0tjqflYbbLdTiJuRaSxKpPtIzHsqqE2WTeuMptvhWqz
        bVJttGaLujnIQBz03tFkfSzq5kgwXiVVxv1QVu6DwrCXka7pP9FutRR1c4iGb0KVsQYl5XtQpDWjoMyI
        3SpDHOk/ukc2ZCuOfxOKujlEw5VQbdqPEv0eJlBlqIbL3Yvh2/cwPfMU8/MLjHQ9PHKX3VNVRG1pDI0V
        /REd7d149GQGE1PTaHB1J9wXdXOIhqmorKxhWS3WmjF4+hzm5l4gFSJzcxg4dZaNobHkQ/RLwn2hMOP4
        1HTCfVE3h2j4OlJdF2pM0Jhq8HB8QtSZEhOPJ6GvsjEf5CvWd9onQDVcqDVDZ9kPr88fJyz0/BXuXV3A
        JXcEA00SPC0S+nsl/HJtHs+8r+JsaazWHPUVuy6obGgSJN7e5kmIL+rmEA2T04pinQVFGhPGJh5zMa9e
        AqNXFnCpLoKzdRJOHJLQdziMLnsYTkcIR5pDONAaxNlrc3gZM4+H449QqDEyn4mxklPUzSEaJiO1Reoq
        VPOx4kf65nHl5wgu1UZwrk7CyXoJ/YfDcNvDaHWEYG8K4YAziOq2AJovSPgnZhJ9J84wn4qKvQnxklHU
        zSEaJtKKIp0FioqquAU7enmBib9cG8HF2ugTOJnkCfy0OAF9hx+91+f4eFrYivIq5ptiJMZdowlQx8hX
        V8LVdYwHp5qnsqHMk/jzdRGcqZcwdCiM3iNhdDaE0dIYwuHmEH50BlHlCkDX4Uex249J/0vup7Wzm/lO
        1pVEiro5REORpeXVyFOU4+bIXR74z6sLLOPnFmufxFP9U/l47GG4HCE0NoVQ1xKCrTUIsyuAsk4/drt9
        6Bpeegp/3LrNfFMMMa5IUTeHaCiyWFeFnFIdZp4+44EvuyOsXE7VR8uGMk/ie2Kyb28O4aAziH1tQRja
        A1B2+pHj8UF7PsD9PJl5ynxTDDGuSFE3h2gokmp0V4kWL17M88DUKqnWSTT9Utl026PiafE6mkKobQnh
        +9YgLIvlU9Tlx/ZuH7YN+rgf8km+o+sgMfbaTEBrxq7i+An0OCXWaWQebQijvSEMZ2NUfP1i7dPipeyr
        Ov3IdfvwbY8XW4eWJkBNgXxTDDGuSFE3h2goUn4CdLaR0dcrsUzLpJJppLJZrHsST6VjbA+grMOPwsXs
        f93rRcHFpU1wanom/U9AXgO04GTQDktiZVK3IeFU81Q2e9sCTLyGOs9RP3Z6fNh6zIsv+7xw3JW4nxvD
        I+lfA3IXcnZ4eGA6HhxsDbJNikgZp25DWaeap7KRxe/y+PBNjxcbjnvx2eAsxoJLbbTZ1ZX+LiTvAyVa
        IyKRpRZIxwPKNNX5HleAtUoSTguWap7KhjJPdb+RxPfPov7OUvalSARFmsr07wPyTkyP+vjQaS6AzjYt
        FyToO6KiKePUKqnb5Ll9rOapbFjm+2dR9msg7ihxbOAE85n2nZgon4XyFHo8GBvnIkjQ8etzbIfN7/Ih
        x+3Dju5o1mnBUs1/PhjNfKz4+w/HmC92FjKk/SwUJZ0ccxXlUFVUYda71AoJdDygHVZ3PoBtAz5sGfSx
        buO4I8XVPOH5rBfK8qivt3YaJcrvA/TYFXoL7j8YixO2EoxNPILaUM18iO8DqSjq5hANX0f5jSynVI/c
        Uh2r49iFvRxowfb0DSG3RMvGJnsjS0VRN4domIryO3GesgI7izUoUBvQ5OpiPX3qyTTbsYl0ff3mLTS2
        HUW+KmpLY5Z7J05FUTeHaLgSxn6VoD5Ou+l3hWXYUaDC9nwlI13Tf3SPbFJ9lUhFUTeHaPgm/M9+F0rG
        d/Zlbl325sn12ZvxPnNd9qblv42uz96U/T5PgsR/sWHzV6LuDDLIIIMMVo1/AU0SPb/3hru0AAAAAElF
        TkSuQmCC
</value>
  </data>
  <data name="newCoordinatorCommand.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAACGFJREFUaEPtV3tQVNcZp52pRWEFZO/Kig9WSBTkTYy1MVg1RhF8JK1NrQZEbZI2
        acc6004bp7HTmaamaU1M66hRUbCJSiapuiyvXfbJmxWjUEiTUUR86z5QERTv/XW+c3fjcvDVlgX/8Dfz
        DXfP+b7v/r7v/M45l4CAx3iMx3gMX9hyhSTjKtWn5tXK8+ZVyh7zagGmVQJMuQKMKwVU5AioyBZgWCG4
        +dghh2WNMs+8RoCF7CfyX/rtLYIKMHoK0K8Q7Hz8kMH2M/WMunWqJiLKCiDyr/gU4bsKngJKlylryuaO
        DuJzDTosa8JfM68WRCJoXuXp/isCrK/eKYKXkeFlASUvCNAtFhqLMkPC+JyDBtNq5cY+5HJlsr4r0EdC
        uQIqiPxSAcVyAdAuFIr5vIMCY65ypnGVIBFxb2dJHsZs+bdXTl7yJJvyZQJKfiCg5EUBxUvkAooWCijK
        FJby+f0OY46gY6RXCmhcHwnHwVi4dYlo3zEZxzZoYPvpGBizVTBmj0bl62PRsjEGF/bFo7MkEZc/i4Xt
        VTV0i+QCDmcq9Xx+v8OQLbjoSKx6IwKdpQm4bkjBDeNUdJuno9v6DHqsM9Bje1Y26ww2RnPkQ74u7RSU
        /VCFoiwBhxcIDj6/31HxsvI66blte7RM3vQ0ui3fZYRvVs7EzapZuFk122Oz2BjNkQ/5UkzL2xpoM6kA
        1TU+v99hWK48acxRiVfLktBFnf+a/Czcqp6D3pq56K19XraauWyM5rxFUIzjYDy0tAIZqq/4/H5H+XJl
        df2vI291VaTihvk7TCY3K7+HW9XPobd2Hm7XLcDt+kzZ6hawMZojH/KlGIo1rxzTe2i+ysrn9zvKlwm7
        Wt7RiF3GpzzdT5c7T+TrMyE2LIRoXyRbw0I2Jhcxh/nKq/AUGn8bJR6ap9rF5/c7yn+kXNu27UncME1l
        G9Tb/dt1GYywZF8M6cgLstkXy0XUZXy9ChRDsSf+NgmHnlet5fP7FacKY+Ia/6w+c/7jOLYhvfIhrZNc
        xIZFkI4sARq/z4yeaYxJqWbuHRmZnsbF/fFo+KP6TNu+CbH8e/yGtkLNuubtkXBrE+TTx6t/VoBnBY4s
        gdT4omysAM8KeAqgGIrt1CWgeVskKCf/Hr+hbd/EjNY9kbiuT5TPfgtJaKZnA8+HWJ/FtM9kRPKhfVCf
        xeZkCc1kMexO0CeCcrUVRs/n3+M3fLk3ZmTLnsjWa+UJ6KpIYxcUO0KrZnlklHFnI3s2MI0x+VR5jlLz
        dBZLOSgX5eTf43dcK4vDdUOy5xJ7Bj22mbhVNZvJhE4c6rhs89gYzZEP6z67zJJBOfi8g4bOksk35FWg
        u2Ca/AlhS2ddpuOS5CLbHE/n0+XTxzyNxVAs5eDzDhrcuuiaq6WxTMckhxumafJKWOkbKF3+pGCfEOls
        TO48kU9jMRTr1sVU83kHDZ1FmhWdxU/gKkmJFZHKNibdst2W6eyykm06G6M58mHky+JAse6iicv4vIMG
        bAj4pqsoSseKKI3FtfJ4XNcnMZLUZbppmdEzI57EfMiXYlzaKC3l4PMOKtxF48PcRVH2Tl0MrpZMkgsp
        m8L0TZ0mo2caoznyIV+XNqrB9c8JoXy+IYFTPzHEpdUUu4s06NRFs+6yYnyMxmiOfGjVrhQPwbF5PwAB
        32g/oIFTq2Ek72Y0Rz7ky8c/EmjJ16B1r0zy0kGZMBk9ny7UoLVAA/Lh43jg9NjhaIr4Cq3hY/i5AUdS
        iSsqudz5ZmKpw0DkHsYmfnxOr/no7JtR/zg3gc9HwPGI19A0GjimepufGzCk6Z2ZyXqXJbnUISWWOpBQ
        7OhH9F6m+egcovaexYT8M9L43WcskXkdC7x5SV44pmrBMRXwucqJZiG475v/T6QYnIkpeqcxRe9CcpkT
        XvLxuiuo2ju1H1nebAVTZfIFZzF+zxmM292ByF0dUO9oN6q2nEhE0+gsD3ngqADYw1/nOfzPSDW6l6cY
        XF13Iz9FewXrCrf2I8zbLwre70d+zM7TUH94GqpNx7vba6Z8gc8FmXyjErCP+hIYgLsizehen2pw4V7k
        4w5dRuzBS/jd/j+hOT+6H/Gm/BhsKNiAqPwOjNtxAmO3/RtjtrRA/UETIjYdQ8RfGpC1PQ99yB8JpxUA
        7GE/hj3gWzynh0ZahXtpqt4pPYj85M8uYdKnFzHvgBV5e1eioSCB2a6CHMwuMLPOj9vairUHNmKnbg10
        hiWwW9Nxri4WvUfVPsSp8+FAwyjZ6sOA2jBRrAk5J1aH2MWqkYfESsUW2BQP/lc0Te8MSdU7XQ9L/slP
        LiCm8AKi95/HxH3n72xYj2zGbm3Buwd/CXbKHGcnzd27zoiPAurCINWGQqoJhVQdAqmKbCSkSgVEW3Ae
        z7cfUg3OtwaKPNP8znaoNzfhN/vfgng04j7kw+5CnoiPhGRTQLQEb36ovZFS7moeMPLeDbu9HaPfb0ZO
        3nvosav7Soa63od8iA95BUSrQoJF8Sue5z2RXOboGVDyH55GBBWw9RSETf/Coq070Fk7/j6SGelL/ias
        Qf/d57e/yKu2nILw9zaEv9uMl7ZtfqBkJGswYAl6g+f3QMTrHB3+Iq/84CRG/fUL/HznhntKxktesgRD
        NAf9gef3QCToHBlxhy93+IX85pMI3Xgcuz95qX/X+5APgmQOgmgaXsrzeyQg1oQe5SUjWoO7qetEXDKR
        jYBoDHQ8cp/isKtHiNUhvXc2anCXaAn6PewBI2AekS2ahl+UjCMgVQyHZAgEyoc9wecYUqAy+FnqumhT
        SKJNUQhb4Pg+86aQULFi+EbRENgj6QOBssAVvvNDDtgU2aI1uBpWxTR+zhcoHTZZLPu2DqXD1vuO/wcY
        K8VSiyj6mAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="newVehicleCommand.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        EAAACxABrSO9dQAABO1JREFUaEPtmF1MW2UYx3vloqLSntbyNafAzLww8TtLdF5omOI0mRe1BaZSOpO2
        LPuMW3QmuMwNthVGodB1g31kZuKNH8QbIxVjYrxzifFqJjLoJ4VNCF44TXzM+56+L+95znl7DgHu+k9+
        yclz6MPvPH3OOQSbrZxyyimnnLXK5pHFHQ2jC+nGiwvQMMr4k1I/ouWRC4TbGh4+z7hF2ZTQ8tA5wryG
        jfF5qIvP/70xPvdzbbzwGnZaUepHF1KlpFVxmbQqjqVVcb30MnNQN1xkqPBf3XChA3tZztpKa8Wl0gK1
        Q3NQE5v9p3q4sA27WYoo7Qx/DZW7Lklxhse5rGLyswQlPM7FHaGvNOfs/mvg/vg61A4VoCZWgJrBQq4u
        Pl+L/Uwj7nVl20VT2KRxXQabNK5Tdl2GqlM31AuIzULVYP5b7GcacUXsbaOmsPXAdRlkRciUcZ3h2P05
        VEdzUD04qxLNt2HHkhH32t46YgrbaVyXwVYE10VcH/wEVQOzFPdAfq46knFiT2nEG9LeesEUdkPiugx1
        PcgF6M8xHG9fBndfCtzRfJFcBHtKIz5BcGMj2E7jugy2GriOUQ4m+QW4+rN/VUWzLuxqGPGx52g5bwp7
        9OG6DHYBuK6jbRRckWl4sD9HcfXnjmNXw4jPa0dLwhSy0wRcl6Hudl5XN8J55Edwnc0Vyd5qjN7YgH11
        EV80Dt85U9hO47oMtha4bsi7V8DVlwZnX5bi6M28iX11Ed+Kii9uCnlek5XAdRnsAnBdhvPYr/wClEj6
        C+yri/hKV7zDprCdxnUZbKdxXcr+CVB6M4w7rthsBXbWhO00QfEOmcKe17gug96QZ8kF6M8Z0nGNyjsi
        KvYz2WbsrAnbaYLrvTFQ3opJce4eozckWQlyjM/rCIyRm5HiDHymP29E8MuifBrsZ9JQeTpV+p3A/g7h
        r/IibNJEmLH8olFh68EfffwJQp8iqnhxn/leL6+HZtJMmolzTqd+wc6aGItbl9aKy6RLiRtI08nT6RPu
        2Lp+uwt78xhNG0u7oyuXtjJtLF1cGQ0PnEqBvWfmcezNI5fWT3stpI3EhWlz6WVm4P6emVbszWNdWi6+
        9tJcHJKeg5BubllcbNpp/DS6/UIzfLdzbwlxmXQpcWvSenFVWoT4UZ5vnsHuNIUnXgSCFWkr08bSqvjK
        pAn3dROmqRsDu9NkG56Gb7a9syppI3E8bSxtJM6kGRUnp4H4UeqfMv4GZOJbuibhlT094AnspZDjLV0/
        rEJaO+3NHyWhqbOb9yfHjUeTVFpkfGsrXH/spX+zm558FbvTaCetTnvr4avg84cMee79T0tK68X1k372
        0BVdX8Yzh65CxcmbcO8Jkaks9ubBK0Imj5tiHu2aXLE0W5HGoxO6fpj6Dye4/D2fEKaWsDeP0ptJiXu9
        fU8Pb9R54AgMDMYo5JjVt3f2GEobieO9bgp3m/YnP1MUp9x9fOp37M1D/tqzRzIptteewD7eiDROJBIU
        cszqnsD+ktPG0uJOezqs9N9HpIv8MbXh2M2Xsbc0vvbgktkv8PqDi/hzVrPe/W0+f2iSNRK/4vCBw8Iv
        CCXx56xmvfvbWtpDb7BGUjpCO/DnrGa9+9P4/KETuqZFvP6QtX93lMh696fx+oOv+9qD39OdbQ8uka91
        1ZMRst79yymnnHLKkeZ/A9JNNpEWW74AAAAASUVORK5CYII=
</value>
  </data>
  <data name="refreshDataCommand.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAABO5JREFUaEPtme9PW1UYx48dJiaQZeEFsxZNQWjpYAz/gr3a/gBfucxELYOZbW7g
        Agoayd5oYmQwl2GULRJ1QqAUHIV2ZY4F5McGdKObbiWTweLglh81mSYLhHrMc8rpbp/e297be5do0k/y
        TUh47pPv097ee873EJImTZo0/3tyvwqYXr5w/6z5+wV/TktgPvPTmT8zPvFtkLopSup9I7helgZqKOxc
        ehsEf+N/647x3Oz+Vy8uBHb3L/9jbAnQbR9MUFIzQUntdUo+nKRsgI98FF8nSQM1FHQKbYVdAgUVOIT2
        vUM0A5fpQm7jWHZe29xk2eAatXUv0hfqxympGqHk/dHUBgDzDqGt0CFQsSzPYghjs3/fLufik9cGQ7Sw
        /SE1HB+i5L1rqQ+wZd4SMRxRt0h6DmH8cuZAiUsIlw2GaPElgWYcv0rJ0Z9TH6CBGizdQluMYaZgjKyO
        oPYhjF/49pX0CeEyb4iCdpy6Tsm7XiUDDONejBjzyLBTUqkPAfe8zfHoCTdf1P2IkkpP7AAnhtdJ9WgH
        qZk4QGpuWMnJW5m4T5So+TiT0uqJKrUhzK2ByT3eEN1zGbRGdzbOUFIRM4CDVI3k4+vksDqDrXEm483S
        IgnBtbhfQoynp/aXuleZca6suhE+wCY5cuUkviYZFmfwtlLDTL3LYvlxv4TkXwgEmHFPRKWeNfr8US8f
        QLV5OWTMyj8AlJDbeNNU4lpmpsV6rsJNySG3A9drQWzaJhKuU4XpzMzZqHH3U5Hy/nXyzqDie14JzPBP
        8cJ1qsj7JnCPm97NtMpE7K4OXKsVbFyXAQp/fPA3Mz0QK2LvewPXagXM7rqEtaJtAFvPYhibZwMc6rfg
        Wq2A2Tj1aRwAfsAlA6u0pD9W5MhQFq7VCjfMVbwlXKcKbDw6wDOAG46RS68BXCtM0FBzUxl4byxcpwrc
        TJemMvDe/MPS5dtmTfHXqvW+lAHfpvoMsGVY/OPS/GiT4uDAdmycPTz6NL4HuGEsXKcZu9uKH9Ugm3Mx
        jEtVETEc+3LR/HaUotz1JjYPL9CCHx78hUtVgV/rurzepbC7uvgyBZYsfPlibr33Ky5VBTbOpHWFiCn3
        FBC7a0O8WOQLyJeabp3G5aoQL2t1W6NjKjy9hkp33JIdfsA5zf6duFwVeHPBNx24LmUOX66FjRFskMA0
        3zTBBirvfOAuLk+ItWf5jpRZLPEWkG8LYYuI+yXl2JVactgbhgFgiyrespa6V+iLjb69+JKEwGYZm5Uz
        LCHlG+3qsQJy7FovCwMgFKjwsJAgYj5EITwwf313HF+WHIg6eoQ2BWaffvKQ4ziC7aSTbsPtojTcySJ1
        U0WkZuwgqf6li1QNb7AYhg9Q6WHxJI9sLB0Lj7MbZrfjNsrYymuw0ajZGEViP1nz9dOjLNiCgAuCLgi8
        IPiCAEw0wI5TN6Lmi3uXNk2fj5fiVuoQhU5xhlFmKWsegEgxyQAZJ66yiDISVS5tmppuv47bpIZUZikK
        XyH6TmgeSDIAhMKWjocUQuKizj8em5qnNX7yGBhCIvJWZB5IMADE8ZH7fpXmfXt/LPtMqvd8Mnhuzw8d
        uhSaByQGgAMQOAiBA5H8i/O/5Z6bVfeoTAk49ulafAuk6tgHjpbqpmjGx9PrmZ/5Qzkts3Pm7+ZvvnJ+
        rimn9Xdtb9g0adKk+c/wL7fTUo0BjlInAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="exportToExcelCommand.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAAA1ZJREFUaEPt2WtIU3EYBnC/52VzlzOJNPuel7k505zpzJxzE0wybZWJjjTNS2nT
        TJ2aWpGVGDUjKBdBl28RyJAgNIQEkSKi6GYTIjKKoBJ1e+OccNW7C/tv4xyCPfB82YfD7zn772ywsLBQ
        QgkllP8qmywGTbyleiHeYgB/mjJl8qvSSZNNNmlSYw9xNo4ZbBhFUgwjqXSq+z32EAeDSItRpMUe4mAQ
        aTGItNhDHAwiLQaRFnuIg0GkxSDSYg9xMIi0GERa7CHOhnPl4Etjh/UQd7kC4q9XszuAGtRpqMGiBWpA
        B1S/FqiThSDu04C4twDEPQUgMqlB1J0Poq4dIOzKA2FnHghPbAdBRy4Ijqsguj0HotuyQdCmAkmPDmJH
        9P+MwCDSYq9LqAGdLVB8tDEb+Me2gciYA/KBCsg010HWtSam+EiRFntdEiw8vzUL+K1KkPfvh4yLtSwO
        CCKe15L5e8BIDYsDgojnHeVggDv8+t5CeL24AHQ6x81OfNKZclhaWYafy0uQfKrMBc87spWDAR7u/L6b
        3cyAxe9fIa5Hy9z5O3MTzGu941fc4qOaMzgY4Aa/dmwmXj5mwH3Wq6AcrgK7wwGvPtlA0p7rFh/VlM7+
        AE94+tikX6iE5dUV+PLjG0y/fQIOhwN05kaP+KjGLRwM8IBf+8BemrrLvAt0bs1aveIjG9I4GOAFTz9t
        LDP3nQNuz1q94iMPp4FQmwDiXSkg0SuY8jpyAir2usQbXjvaxBybmfln8OjNHDOieLTZIz6yXsHBAA/4
        mI48ePHxHYPWmRtANWxgxsx//gAxRpVbfER9KgcD3ODpL6mzD24weOvzaeexuff0IfPa0MSYW3xEnZz9
        Ae7w6UMHmKeP3WEH5flK55mXDZbBin2VacbpvS748EMyDgb48PPA25n/Gx9ey8GAYOLDa1I4GBBE/LqD
        UvYH8I3ZtmDhw6uSOBjQqlTzWpS2YOD5OxNAWJQIVOmfAYEWe4kj0GwGXyvUJYK4JBmoMrkLxN9iD3FE
        xcngS8V0S6RAlcpAsifVBeJvsYc41G4Z+Fz6zgcRTxd7iIMvyHaxhzj4gmwXe4iDL8h2sYc4lF5hwxdl
        q5ReEfgfHJJyhZqLETReok/Lx55QQgklFG7zC9hJKYWZvUn5AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="gridReportCommand.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAAAc9JREFUaEPtmcFKAlEYhec1egCx6TVScswhrUWLlm3NVZYtnXWLNkUFLooWPUOr
        IKI3MEJIG8cxTegR/rjShJzfkGHmjre8H3wr53jOj4FUhqHRaBaHUp/Wiz55RZ9Ihhs+dYs+WdgbG98F
        rDhmXeyNjSllUsTe2MAiWWJvbGCRLLE3GtXmo3HwTEIskmXQZ1SbDzgnPMGbzeMAYWT0AeFd7AMqNSdf
        rjndvZpDKik2lQ/qs7+lVRz/42F99rc0Cykm7mVgQDVxLwMDqol7GRhI2tPGDbW9HrU6Lh2fNdjruJeB
        gSStHDnkDT5o5+mTtu9H9PLaYc/gXgYGklQc0BuOxuO37gbUarvsGdzLwEDSnl/fkvc+pLden04urtjr
        uJeBAdXEvQwMqCbuZWBANXEvAwOqiXsZGFBN3MvAgGriXgYG1golSqfTlEqlaOmyk4iiS3SKbtyDexkY
        WDbN8RsmfYDQNFeiHzD5CeBvTrIMPoGcvRn9gEmxSJbYqw/QB4QUe0MdsFvep9/EIlli76S4l5Gx7G7W
        smmaWCRL7A3MWIXZf5XIWgVLPIjheR8gNq3m7RzuDQUWyRJ7Y+M//IvJknyEW/Ip2o+JRqP5W3wBaRB0
        ocEuVsUAAAAASUVORK5CYII=
</value>
  </data>
  <data name="clearFiltersCommand.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAABL1JREFUaEPtme1PW1Ucx89i1GSWUgo1mugb96BCXCSdkSjbQCVOGGzrLWxIoZD4
        lPjCP2BvdcFE3ZAB47kUSpWYbTpgdpUxgcGAPsyHLQZXwCp1kEFsaRkPbX/m3tJxOedCL6U09wXf5PPm
        e37p+dyTk6a5RWg7ZKANPTalQ59P6pBjsgVNTOlQKd3hc4LNvWZUeq8ZAUYpPifYTGiQw9GEgM1EE/oX
        nxNs/m5EwAU+J9j8VY+AC3xOsBmrRcAFPifY2KoRcIHPCTYjVQi4wOcEmz8qEHCBzwk2d8oRcIHPCTa/
        lyHgAp8TbH45g4ALfE6weVLxBXCBzwk2uPj2A0Q7uLjwHiC37RFRSVuiqKRNKSrRnxKp9Q1itb4jRt16
        K6aodVxc1AziohaIDVKohdhCDYhV2jGxqskaW6hpl6g09ZKCxlOxqkZKWlj7Iv2Z+DYRj6hYrxAVf2sQ
        lXzjjCnWg5ihFcRqGh2I1UHp5gCMeBNIaFQakKgaAxQ0QBxDPcS9W8cgya9xSvJrDNKTNcfxfSMSkVr/
        ASms4xDWBoQLaeGgdMOydD1LupZBml+zTDVIT55niMuvfA/ff9OJUetGyVMOCK99ymzhgPRq4aB0FcTT
        nKiE+BMVIM07Z8P333Rii7R+7muxLM2+FgUrwivS1atOeUU4IB2fdw4SGMohIfdrP77/phOQZl8LrlNe
        +1oEhFdOOT4vKF0elF6mDGTKs5H/xiJOea1r8VCafS04T5klHJCWKc+AjKL5KrIP4OrcLXN27IJo4vph
        bwLuEXZc7XsOuq48D1Glc28q7hF2XIbED2cNiRBNXD8mvo97hB23cd9Zt3EfRJWrL32Je4SdWWOywf1T
        MkSTWWNyJ+4Rdtxdcrvn2n6INM6rLxNdEHeXfBT3CCvQfUjk6U7xz11PgUgzdTmJ6IJ4ulN8YJLvxH02
        HE9P6qsPfn4dtoJ/2nYTHRtP92v7cZ8NZ7734CfzvQdgKxhveYbo2DzoPfAx7rPhzPeltS70pcFWMFKX
        QHRs5vvStLjPhrN4I922eOMNWA93dyrR8eF25U6iW0Vf+gjus6FAz2HZ4kAGhOJ+h5zo+PBb2Q5Y6H+L
        6IMs9Gf4wXQo/J8US/0Z2Us334ZQOL57gej4cLscged6KtGvYuBwJu7FO0uDmae9g5kQCpvmaaLjA/2+
        1GmQEz2bpZvvfIp78Y53MGvIN3QEQnGn4nGi48Of5xHcv7SH6Nl4h7IGcC9egV+z4rzD2V7fcA6sx1xP
        OvPiFu/5YKtBMKGXET0b2gH6c6W4X8jAcI7SbzoKoZi+nMS8+8d7PozXI7BrHgX/cA6xxgbM2QrcL2R8
        5mPVfvMxCIVDHw+jtYjo+RD882+x/01ijY3PdLQK9wsZn+W4zW9RwHp4h46AvXEH2BsQscYHhxYxeLrk
        xBobn0VxF/cLGRg6+iyYqDwwU6fBTLWDhbKDhQI2c9deeSiBr/FhsgUxuDqew9fszJ5mxWdgVebSLrhf
        WIFbqifASiWBRZEJZuojtzHx4swF2djMBek0WCkTWJQ2sFDTYKFmwErNgVUJAag5pqPXrNRdZtaqNP53
        STbm/P6pwdkru+qYz7TkJtJ74PtuZztbmP8Bg8IPYgY+SGcAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="panelManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>225, 17</value>
  </metadata>
  <data name="coordinatorsGrid_Layout_0.LayoutString" xml:space="preserve">
    <value>&lt;GridEXLayoutData&gt;&lt;RootTable&gt;&lt;Key&gt;Coordinators&lt;/Key&gt;&lt;Caption&gt;Coordinator&lt;/Caption&gt;&lt;Columns Collection="true" ElementName="Column"&gt;&lt;Column0 ID="CoordinatorId"&gt;&lt;Caption&gt;Coordinator Id&lt;/Caption&gt;&lt;DataTypeCode&gt;Int64&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;CoordinatorId&lt;/DataMember&gt;&lt;Key&gt;CoordinatorId&lt;/Key&gt;&lt;Position&gt;0&lt;/Position&gt;&lt;Visible&gt;False&lt;/Visible&gt;&lt;/Column0&gt;&lt;Column1 ID="UserId"&gt;&lt;Caption&gt;User Id&lt;/Caption&gt;&lt;DataTypeCode&gt;Int64&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;UserId&lt;/DataMember&gt;&lt;Key&gt;UserId&lt;/Key&gt;&lt;Position&gt;1&lt;/Position&gt;&lt;ShowInFieldChooser&gt;False&lt;/ShowInFieldChooser&gt;&lt;Visible&gt;False&lt;/Visible&gt;&lt;/Column1&gt;&lt;Column2 ID="UserFullName"&gt;&lt;Caption&gt;User Full Name&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;UserFullName&lt;/DataMember&gt;&lt;EditType&gt;NoEdit&lt;/EditType&gt;&lt;Key&gt;UserFullName&lt;/Key&gt;&lt;Position&gt;2&lt;/Position&gt;&lt;Width&gt;296&lt;/Width&gt;&lt;/Column2&gt;&lt;Column3 ID="HireDate"&gt;&lt;Caption&gt;Hire Date&lt;/Caption&gt;&lt;DataTypeCode&gt;DateTime&lt;/DataTypeCode&gt;&lt;DataMember&gt;HireDate&lt;/DataMember&gt;&lt;DefaultGroupInterval&gt;Date&lt;/DefaultGroupInterval&gt;&lt;EditType&gt;CalendarCombo&lt;/EditType&gt;&lt;FormatString&gt;d&lt;/FormatString&gt;&lt;Key&gt;HireDate&lt;/Key&gt;&lt;Position&gt;3&lt;/Position&gt;&lt;Visible&gt;False&lt;/Visible&gt;&lt;/Column3&gt;&lt;Column4 ID="Notes"&gt;&lt;Caption&gt;Notes&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;Notes&lt;/DataMember&gt;&lt;Key&gt;Notes&lt;/Key&gt;&lt;MaxLength&gt;1000&lt;/MaxLength&gt;&lt;Position&gt;4&lt;/Position&gt;&lt;Width&gt;845&lt;/Width&gt;&lt;/Column4&gt;&lt;Column5 ID="CreatedAt"&gt;&lt;Caption&gt;Created At&lt;/Caption&gt;&lt;DataTypeCode&gt;DateTime&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;CreatedAt&lt;/DataMember&gt;&lt;DefaultGroupInterval&gt;Date&lt;/DefaultGroupInterval&gt;&lt;EditType&gt;CalendarCombo&lt;/EditType&gt;&lt;FormatString&gt;d&lt;/FormatString&gt;&lt;Key&gt;CreatedAt&lt;/Key&gt;&lt;Position&gt;5&lt;/Position&gt;&lt;Visible&gt;False&lt;/Visible&gt;&lt;/Column5&gt;&lt;Column6 ID="UserFirstName"&gt;&lt;Caption&gt;User First Name&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;UserFirstName&lt;/DataMember&gt;&lt;EditType&gt;NoEdit&lt;/EditType&gt;&lt;Key&gt;UserFirstName&lt;/Key&gt;&lt;Position&gt;6&lt;/Position&gt;&lt;Visible&gt;False&lt;/Visible&gt;&lt;/Column6&gt;&lt;Column7 ID="UserLastName"&gt;&lt;Caption&gt;User Last Name&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;UserLastName&lt;/DataMember&gt;&lt;EditType&gt;NoEdit&lt;/EditType&gt;&lt;Key&gt;UserLastName&lt;/Key&gt;&lt;Position&gt;7&lt;/Position&gt;&lt;Visible&gt;False&lt;/Visible&gt;&lt;/Column7&gt;&lt;/Columns&gt;&lt;SortKeys Collection="true" ElementName="SortKey"&gt;&lt;SortKey0 ID="SortKey0"&gt;&lt;ColIndex&gt;2&lt;/ColIndex&gt;&lt;/SortKey0&gt;&lt;/SortKeys&gt;&lt;GroupCondition /&gt;&lt;/RootTable&gt;&lt;RowWithErrorsFormatStyle&gt;&lt;PredefinedStyle&gt;RowWithErrorsFormatStyle&lt;/PredefinedStyle&gt;&lt;/RowWithErrorsFormatStyle&gt;&lt;LinkFormatStyle&gt;&lt;PredefinedStyle&gt;LinkFormatStyle&lt;/PredefinedStyle&gt;&lt;/LinkFormatStyle&gt;&lt;CardCaptionFormatStyle&gt;&lt;PredefinedStyle&gt;CardCaptionFormatStyle&lt;/PredefinedStyle&gt;&lt;/CardCaptionFormatStyle&gt;&lt;GroupByBoxFormatStyle&gt;&lt;PredefinedStyle&gt;GroupByBoxFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupByBoxFormatStyle&gt;&lt;GroupByBoxInfoFormatStyle&gt;&lt;PredefinedStyle&gt;GroupByBoxInfoFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupByBoxInfoFormatStyle&gt;&lt;GroupRowFormatStyle&gt;&lt;PredefinedStyle&gt;GroupRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupRowFormatStyle&gt;&lt;GroupTotalRowFormatStyle&gt;&lt;FontBold&gt;True&lt;/FontBold&gt;&lt;PredefinedStyle&gt;GroupTotalRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupTotalRowFormatStyle&gt;&lt;HeaderFormatStyle&gt;&lt;PredefinedStyle&gt;HeaderFormatStyle&lt;/PredefinedStyle&gt;&lt;/HeaderFormatStyle&gt;&lt;PreviewRowFormatStyle&gt;&lt;PredefinedStyle&gt;PreviewRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/PreviewRowFormatStyle&gt;&lt;RowFormatStyle&gt;&lt;PredefinedStyle&gt;RowFormatStyle&lt;/PredefinedStyle&gt;&lt;/RowFormatStyle&gt;&lt;SelectedFormatStyle&gt;&lt;PredefinedStyle&gt;SelectedFormatStyle&lt;/PredefinedStyle&gt;&lt;/SelectedFormatStyle&gt;&lt;SelectedInactiveFormatStyle&gt;&lt;PredefinedStyle&gt;SelectedInactiveFormatStyle&lt;/PredefinedStyle&gt;&lt;/SelectedInactiveFormatStyle&gt;&lt;TotalRowFormatStyle&gt;&lt;FontBold&gt;True&lt;/FontBold&gt;&lt;PredefinedStyle&gt;TotalRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/TotalRowFormatStyle&gt;&lt;FocusCellFormatStyle&gt;&lt;BackColor&gt;167, 205, 240&lt;/BackColor&gt;&lt;BackColorGradient&gt;167, 205, 240&lt;/BackColorGradient&gt;&lt;PredefinedStyle&gt;FocusCellFormatStyle&lt;/PredefinedStyle&gt;&lt;/FocusCellFormatStyle&gt;&lt;WatermarkImage /&gt;&lt;GridLineStyle&gt;Solid&lt;/GridLineStyle&gt;&lt;BorderStyle&gt;None&lt;/BorderStyle&gt;&lt;BackColor&gt;White&lt;/BackColor&gt;&lt;VisualStyle&gt;Office2010&lt;/VisualStyle&gt;&lt;OfficeColorScheme&gt;Blue&lt;/OfficeColorScheme&gt;&lt;AllowDelete&gt;True&lt;/AllowDelete&gt;&lt;AllowEdit&gt;False&lt;/AllowEdit&gt;&lt;FilterMode&gt;Automatic&lt;/FilterMode&gt;&lt;FocusStyle&gt;None&lt;/FocusStyle&gt;&lt;HideSelection&gt;Highlight&lt;/HideSelection&gt;&lt;RowHeaders&gt;True&lt;/RowHeaders&gt;&lt;SelectionMode&gt;MultipleSelectionSameTable&lt;/SelectionMode&gt;&lt;BuiltInTexts ID="LocalizableStrings" Collection="true"&gt;&lt;GroupByBoxInfo&gt;Σύρετε μια στήλη εδώ για να ταξινομήσετε κατά αυτή τη στήλη.&lt;/GroupByBoxInfo&gt;&lt;CalendarNoneButton&gt;Καμία&lt;/CalendarNoneButton&gt;&lt;CalendarTodayButton&gt;Σήμερα&lt;/CalendarTodayButton&gt;&lt;/BuiltInTexts&gt;&lt;DefaultFilterRowComparison&gt;Contains&lt;/DefaultFilterRowComparison&gt;&lt;FilterRowUpdateMode&gt;WhenValueChanges&lt;/FilterRowUpdateMode&gt;&lt;/GridEXLayoutData&gt;</value>
  </data>
  <data name="attractionsGrid_Layout_0.LayoutString" xml:space="preserve">
    <value>&lt;GridEXLayoutData&gt;&lt;RootTable&gt;&lt;Key&gt;Attractions&lt;/Key&gt;&lt;Caption&gt;Attraction&lt;/Caption&gt;&lt;Columns Collection="true" ElementName="Column"&gt;&lt;Column0 ID="AttractionId"&gt;&lt;Caption&gt;Attraction Id&lt;/Caption&gt;&lt;DataTypeCode&gt;Int64&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;AttractionId&lt;/DataMember&gt;&lt;Key&gt;AttractionId&lt;/Key&gt;&lt;Position&gt;0&lt;/Position&gt;&lt;/Column0&gt;&lt;Column1 ID="Name"&gt;&lt;Caption&gt;Name&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;System.String&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;Name&lt;/DataMember&gt;&lt;Key&gt;Name&lt;/Key&gt;&lt;MaxLength&gt;200&lt;/MaxLength&gt;&lt;Position&gt;1&lt;/Position&gt;&lt;/Column1&gt;&lt;Column2 ID="Type"&gt;&lt;Caption&gt;Type&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;Type&lt;/DataMember&gt;&lt;Key&gt;Type&lt;/Key&gt;&lt;MaxLength&gt;100&lt;/MaxLength&gt;&lt;Position&gt;2&lt;/Position&gt;&lt;/Column2&gt;&lt;Column3 ID="Description"&gt;&lt;Caption&gt;Description&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;Description&lt;/DataMember&gt;&lt;Key&gt;Description&lt;/Key&gt;&lt;MaxLength&gt;2000&lt;/MaxLength&gt;&lt;Position&gt;3&lt;/Position&gt;&lt;/Column3&gt;&lt;Column4 ID="AddressLine1"&gt;&lt;Caption&gt;Address Line1&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;AddressLine1&lt;/DataMember&gt;&lt;Key&gt;AddressLine1&lt;/Key&gt;&lt;MaxLength&gt;200&lt;/MaxLength&gt;&lt;Position&gt;4&lt;/Position&gt;&lt;/Column4&gt;&lt;Column5 ID="AddressLine2"&gt;&lt;Caption&gt;Address Line2&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;AddressLine2&lt;/DataMember&gt;&lt;Key&gt;AddressLine2&lt;/Key&gt;&lt;MaxLength&gt;200&lt;/MaxLength&gt;&lt;Position&gt;5&lt;/Position&gt;&lt;/Column5&gt;&lt;Column6 ID="City"&gt;&lt;Caption&gt;City&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;City&lt;/DataMember&gt;&lt;Key&gt;City&lt;/Key&gt;&lt;MaxLength&gt;100&lt;/MaxLength&gt;&lt;Position&gt;6&lt;/Position&gt;&lt;/Column6&gt;&lt;Column7 ID="StateRegion"&gt;&lt;Caption&gt;State Region&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;StateRegion&lt;/DataMember&gt;&lt;Key&gt;StateRegion&lt;/Key&gt;&lt;MaxLength&gt;100&lt;/MaxLength&gt;&lt;Position&gt;7&lt;/Position&gt;&lt;/Column7&gt;&lt;Column8 ID="PostalCode"&gt;&lt;Caption&gt;Postal Code&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;PostalCode&lt;/DataMember&gt;&lt;Key&gt;PostalCode&lt;/Key&gt;&lt;MaxLength&gt;20&lt;/MaxLength&gt;&lt;Position&gt;8&lt;/Position&gt;&lt;/Column8&gt;&lt;Column9 ID="Country"&gt;&lt;Caption&gt;Country&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;Country&lt;/DataMember&gt;&lt;Key&gt;Country&lt;/Key&gt;&lt;MaxLength&gt;100&lt;/MaxLength&gt;&lt;Position&gt;9&lt;/Position&gt;&lt;/Column9&gt;&lt;Column10 ID="Latitude"&gt;&lt;Caption&gt;Latitude&lt;/Caption&gt;&lt;DataTypeCode&gt;Decimal&lt;/DataTypeCode&gt;&lt;DataMember&gt;Latitude&lt;/DataMember&gt;&lt;FormatString&gt;c&lt;/FormatString&gt;&lt;Key&gt;Latitude&lt;/Key&gt;&lt;Position&gt;10&lt;/Position&gt;&lt;/Column10&gt;&lt;Column11 ID="Longitude"&gt;&lt;Caption&gt;Longitude&lt;/Caption&gt;&lt;DataTypeCode&gt;Decimal&lt;/DataTypeCode&gt;&lt;DataMember&gt;Longitude&lt;/DataMember&gt;&lt;FormatString&gt;c&lt;/FormatString&gt;&lt;Key&gt;Longitude&lt;/Key&gt;&lt;Position&gt;11&lt;/Position&gt;&lt;/Column11&gt;&lt;Column12 ID="Website"&gt;&lt;Caption&gt;Website&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;Website&lt;/DataMember&gt;&lt;Key&gt;Website&lt;/Key&gt;&lt;MaxLength&gt;255&lt;/MaxLength&gt;&lt;Position&gt;12&lt;/Position&gt;&lt;/Column12&gt;&lt;Column13 ID="CreatedAt"&gt;&lt;Caption&gt;Created At&lt;/Caption&gt;&lt;DataTypeCode&gt;DateTime&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;CreatedAt&lt;/DataMember&gt;&lt;DefaultGroupInterval&gt;Date&lt;/DefaultGroupInterval&gt;&lt;EditType&gt;CalendarCombo&lt;/EditType&gt;&lt;FormatString&gt;d&lt;/FormatString&gt;&lt;Key&gt;CreatedAt&lt;/Key&gt;&lt;Position&gt;13&lt;/Position&gt;&lt;/Column13&gt;&lt;/Columns&gt;&lt;GroupCondition /&gt;&lt;/RootTable&gt;&lt;RowWithErrorsFormatStyle&gt;&lt;PredefinedStyle&gt;RowWithErrorsFormatStyle&lt;/PredefinedStyle&gt;&lt;/RowWithErrorsFormatStyle&gt;&lt;LinkFormatStyle&gt;&lt;PredefinedStyle&gt;LinkFormatStyle&lt;/PredefinedStyle&gt;&lt;/LinkFormatStyle&gt;&lt;CardCaptionFormatStyle&gt;&lt;PredefinedStyle&gt;CardCaptionFormatStyle&lt;/PredefinedStyle&gt;&lt;/CardCaptionFormatStyle&gt;&lt;GroupByBoxFormatStyle&gt;&lt;PredefinedStyle&gt;GroupByBoxFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupByBoxFormatStyle&gt;&lt;GroupByBoxInfoFormatStyle&gt;&lt;PredefinedStyle&gt;GroupByBoxInfoFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupByBoxInfoFormatStyle&gt;&lt;GroupRowFormatStyle&gt;&lt;PredefinedStyle&gt;GroupRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupRowFormatStyle&gt;&lt;GroupTotalRowFormatStyle&gt;&lt;FontBold&gt;True&lt;/FontBold&gt;&lt;PredefinedStyle&gt;GroupTotalRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupTotalRowFormatStyle&gt;&lt;HeaderFormatStyle&gt;&lt;PredefinedStyle&gt;HeaderFormatStyle&lt;/PredefinedStyle&gt;&lt;/HeaderFormatStyle&gt;&lt;PreviewRowFormatStyle&gt;&lt;PredefinedStyle&gt;PreviewRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/PreviewRowFormatStyle&gt;&lt;RowFormatStyle&gt;&lt;PredefinedStyle&gt;RowFormatStyle&lt;/PredefinedStyle&gt;&lt;/RowFormatStyle&gt;&lt;SelectedFormatStyle&gt;&lt;PredefinedStyle&gt;SelectedFormatStyle&lt;/PredefinedStyle&gt;&lt;/SelectedFormatStyle&gt;&lt;SelectedInactiveFormatStyle&gt;&lt;PredefinedStyle&gt;SelectedInactiveFormatStyle&lt;/PredefinedStyle&gt;&lt;/SelectedInactiveFormatStyle&gt;&lt;TotalRowFormatStyle&gt;&lt;FontBold&gt;True&lt;/FontBold&gt;&lt;PredefinedStyle&gt;TotalRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/TotalRowFormatStyle&gt;&lt;FocusCellFormatStyle&gt;&lt;BackColor&gt;167, 205, 240&lt;/BackColor&gt;&lt;BackColorGradient&gt;167, 205, 240&lt;/BackColorGradient&gt;&lt;PredefinedStyle&gt;FocusCellFormatStyle&lt;/PredefinedStyle&gt;&lt;/FocusCellFormatStyle&gt;&lt;WatermarkImage /&gt;&lt;GridLineStyle&gt;Solid&lt;/GridLineStyle&gt;&lt;BorderStyle&gt;None&lt;/BorderStyle&gt;&lt;BackColor&gt;White&lt;/BackColor&gt;&lt;VisualStyle&gt;Office2010&lt;/VisualStyle&gt;&lt;OfficeColorScheme&gt;Blue&lt;/OfficeColorScheme&gt;&lt;AllowDelete&gt;True&lt;/AllowDelete&gt;&lt;AllowEdit&gt;False&lt;/AllowEdit&gt;&lt;FilterMode&gt;Automatic&lt;/FilterMode&gt;&lt;FocusStyle&gt;None&lt;/FocusStyle&gt;&lt;HideSelection&gt;Highlight&lt;/HideSelection&gt;&lt;RowHeaders&gt;True&lt;/RowHeaders&gt;&lt;SelectionMode&gt;MultipleSelectionSameTable&lt;/SelectionMode&gt;&lt;BuiltInTexts ID="LocalizableStrings" Collection="true"&gt;&lt;GroupByBoxInfo&gt;Σύρετε μια στήλη εδώ για να ταξινομήσετε κατά αυτή τη στήλη.&lt;/GroupByBoxInfo&gt;&lt;CalendarNoneButton&gt;Καμία&lt;/CalendarNoneButton&gt;&lt;CalendarTodayButton&gt;Σήμερα&lt;/CalendarTodayButton&gt;&lt;/BuiltInTexts&gt;&lt;DefaultFilterRowComparison&gt;Contains&lt;/DefaultFilterRowComparison&gt;&lt;FilterRowUpdateMode&gt;WhenValueChanges&lt;/FilterRowUpdateMode&gt;&lt;/GridEXLayoutData&gt;</value>
  </data>
  <data name="accommodationsGrid_Layout_0.LayoutString" xml:space="preserve">
    <value>&lt;GridEXLayoutData&gt;&lt;RootTable&gt;&lt;Key&gt;Accommodations&lt;/Key&gt;&lt;Caption&gt;Διαμονή&lt;/Caption&gt;&lt;Columns Collection="true" ElementName="Column"&gt;&lt;Column0 ID="AccommodationId"&gt;&lt;Caption&gt;Accommodation Id&lt;/Caption&gt;&lt;DataTypeCode&gt;Int64&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;AccommodationId&lt;/DataMember&gt;&lt;Key&gt;AccommodationId&lt;/Key&gt;&lt;Position&gt;0&lt;/Position&gt;&lt;/Column0&gt;&lt;Column1 ID="Name"&gt;&lt;Caption&gt;Name&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;System.String&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;Name&lt;/DataMember&gt;&lt;Key&gt;Name&lt;/Key&gt;&lt;MaxLength&gt;200&lt;/MaxLength&gt;&lt;Position&gt;1&lt;/Position&gt;&lt;/Column1&gt;&lt;Column2 ID="Type"&gt;&lt;Caption&gt;Type&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;Type&lt;/DataMember&gt;&lt;Key&gt;Type&lt;/Key&gt;&lt;MaxLength&gt;100&lt;/MaxLength&gt;&lt;Position&gt;2&lt;/Position&gt;&lt;/Column2&gt;&lt;Column3 ID="AddressLine1"&gt;&lt;Caption&gt;Address Line1&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;AddressLine1&lt;/DataMember&gt;&lt;Key&gt;AddressLine1&lt;/Key&gt;&lt;MaxLength&gt;200&lt;/MaxLength&gt;&lt;Position&gt;3&lt;/Position&gt;&lt;/Column3&gt;&lt;Column4 ID="AddressLine2"&gt;&lt;Caption&gt;Address Line2&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;AddressLine2&lt;/DataMember&gt;&lt;Key&gt;AddressLine2&lt;/Key&gt;&lt;MaxLength&gt;200&lt;/MaxLength&gt;&lt;Position&gt;4&lt;/Position&gt;&lt;/Column4&gt;&lt;Column5 ID="City"&gt;&lt;Caption&gt;City&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;City&lt;/DataMember&gt;&lt;Key&gt;City&lt;/Key&gt;&lt;MaxLength&gt;100&lt;/MaxLength&gt;&lt;Position&gt;5&lt;/Position&gt;&lt;/Column5&gt;&lt;Column6 ID="StateRegion"&gt;&lt;Caption&gt;State Region&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;StateRegion&lt;/DataMember&gt;&lt;Key&gt;StateRegion&lt;/Key&gt;&lt;MaxLength&gt;100&lt;/MaxLength&gt;&lt;Position&gt;6&lt;/Position&gt;&lt;/Column6&gt;&lt;Column7 ID="PostalCode"&gt;&lt;Caption&gt;Postal Code&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;PostalCode&lt;/DataMember&gt;&lt;Key&gt;PostalCode&lt;/Key&gt;&lt;MaxLength&gt;20&lt;/MaxLength&gt;&lt;Position&gt;7&lt;/Position&gt;&lt;/Column7&gt;&lt;Column8 ID="Country"&gt;&lt;Caption&gt;Country&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;Country&lt;/DataMember&gt;&lt;Key&gt;Country&lt;/Key&gt;&lt;MaxLength&gt;100&lt;/MaxLength&gt;&lt;Position&gt;8&lt;/Position&gt;&lt;/Column8&gt;&lt;Column9 ID="Phone"&gt;&lt;Caption&gt;Phone&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;Phone&lt;/DataMember&gt;&lt;Key&gt;Phone&lt;/Key&gt;&lt;MaxLength&gt;50&lt;/MaxLength&gt;&lt;Position&gt;9&lt;/Position&gt;&lt;/Column9&gt;&lt;Column10 ID="Email"&gt;&lt;Caption&gt;Email&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;Email&lt;/DataMember&gt;&lt;Key&gt;Email&lt;/Key&gt;&lt;MaxLength&gt;255&lt;/MaxLength&gt;&lt;Position&gt;10&lt;/Position&gt;&lt;/Column10&gt;&lt;Column11 ID="Website"&gt;&lt;Caption&gt;Website&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;Website&lt;/DataMember&gt;&lt;Key&gt;Website&lt;/Key&gt;&lt;MaxLength&gt;255&lt;/MaxLength&gt;&lt;Position&gt;11&lt;/Position&gt;&lt;/Column11&gt;&lt;Column12 ID="Rating"&gt;&lt;Caption&gt;Rating&lt;/Caption&gt;&lt;DataTypeCode&gt;Decimal&lt;/DataTypeCode&gt;&lt;DataMember&gt;Rating&lt;/DataMember&gt;&lt;FormatString&gt;c&lt;/FormatString&gt;&lt;Key&gt;Rating&lt;/Key&gt;&lt;Position&gt;12&lt;/Position&gt;&lt;/Column12&gt;&lt;Column13 ID="CreatedAt"&gt;&lt;Caption&gt;Created At&lt;/Caption&gt;&lt;DataTypeCode&gt;DateTime&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;CreatedAt&lt;/DataMember&gt;&lt;DefaultGroupInterval&gt;Date&lt;/DefaultGroupInterval&gt;&lt;EditType&gt;CalendarCombo&lt;/EditType&gt;&lt;FormatString&gt;d&lt;/FormatString&gt;&lt;Key&gt;CreatedAt&lt;/Key&gt;&lt;Position&gt;13&lt;/Position&gt;&lt;/Column13&gt;&lt;/Columns&gt;&lt;GroupCondition /&gt;&lt;/RootTable&gt;&lt;RowWithErrorsFormatStyle&gt;&lt;PredefinedStyle&gt;RowWithErrorsFormatStyle&lt;/PredefinedStyle&gt;&lt;/RowWithErrorsFormatStyle&gt;&lt;LinkFormatStyle&gt;&lt;PredefinedStyle&gt;LinkFormatStyle&lt;/PredefinedStyle&gt;&lt;/LinkFormatStyle&gt;&lt;CardCaptionFormatStyle&gt;&lt;PredefinedStyle&gt;CardCaptionFormatStyle&lt;/PredefinedStyle&gt;&lt;/CardCaptionFormatStyle&gt;&lt;GroupByBoxFormatStyle&gt;&lt;PredefinedStyle&gt;GroupByBoxFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupByBoxFormatStyle&gt;&lt;GroupByBoxInfoFormatStyle&gt;&lt;PredefinedStyle&gt;GroupByBoxInfoFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupByBoxInfoFormatStyle&gt;&lt;GroupRowFormatStyle&gt;&lt;PredefinedStyle&gt;GroupRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupRowFormatStyle&gt;&lt;GroupTotalRowFormatStyle&gt;&lt;FontBold&gt;True&lt;/FontBold&gt;&lt;PredefinedStyle&gt;GroupTotalRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupTotalRowFormatStyle&gt;&lt;HeaderFormatStyle&gt;&lt;PredefinedStyle&gt;HeaderFormatStyle&lt;/PredefinedStyle&gt;&lt;/HeaderFormatStyle&gt;&lt;PreviewRowFormatStyle&gt;&lt;PredefinedStyle&gt;PreviewRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/PreviewRowFormatStyle&gt;&lt;RowFormatStyle&gt;&lt;PredefinedStyle&gt;RowFormatStyle&lt;/PredefinedStyle&gt;&lt;/RowFormatStyle&gt;&lt;SelectedFormatStyle&gt;&lt;PredefinedStyle&gt;SelectedFormatStyle&lt;/PredefinedStyle&gt;&lt;/SelectedFormatStyle&gt;&lt;SelectedInactiveFormatStyle&gt;&lt;PredefinedStyle&gt;SelectedInactiveFormatStyle&lt;/PredefinedStyle&gt;&lt;/SelectedInactiveFormatStyle&gt;&lt;TotalRowFormatStyle&gt;&lt;FontBold&gt;True&lt;/FontBold&gt;&lt;PredefinedStyle&gt;TotalRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/TotalRowFormatStyle&gt;&lt;FocusCellFormatStyle&gt;&lt;BackColor&gt;167, 205, 240&lt;/BackColor&gt;&lt;BackColorGradient&gt;167, 205, 240&lt;/BackColorGradient&gt;&lt;PredefinedStyle&gt;FocusCellFormatStyle&lt;/PredefinedStyle&gt;&lt;/FocusCellFormatStyle&gt;&lt;WatermarkImage /&gt;&lt;GridLineStyle&gt;Solid&lt;/GridLineStyle&gt;&lt;BorderStyle&gt;None&lt;/BorderStyle&gt;&lt;BackColor&gt;White&lt;/BackColor&gt;&lt;VisualStyle&gt;Office2010&lt;/VisualStyle&gt;&lt;OfficeColorScheme&gt;Blue&lt;/OfficeColorScheme&gt;&lt;AllowDelete&gt;True&lt;/AllowDelete&gt;&lt;AllowEdit&gt;False&lt;/AllowEdit&gt;&lt;FilterMode&gt;Automatic&lt;/FilterMode&gt;&lt;FocusStyle&gt;None&lt;/FocusStyle&gt;&lt;HideSelection&gt;Highlight&lt;/HideSelection&gt;&lt;RowHeaders&gt;True&lt;/RowHeaders&gt;&lt;SelectionMode&gt;MultipleSelectionSameTable&lt;/SelectionMode&gt;&lt;BuiltInTexts ID="LocalizableStrings" Collection="true"&gt;&lt;GroupByBoxInfo&gt;Σύρετε μια στήλη εδώ για να ταξινομήσετε κατά αυτή τη στήλη.&lt;/GroupByBoxInfo&gt;&lt;CalendarNoneButton&gt;Καμία&lt;/CalendarNoneButton&gt;&lt;CalendarTodayButton&gt;Σήμερα&lt;/CalendarTodayButton&gt;&lt;/BuiltInTexts&gt;&lt;DefaultFilterRowComparison&gt;Contains&lt;/DefaultFilterRowComparison&gt;&lt;FilterRowUpdateMode&gt;WhenValueChanges&lt;/FilterRowUpdateMode&gt;&lt;/GridEXLayoutData&gt;</value>
  </data>
  <data name="toursGrid_Layout_0.LayoutString" xml:space="preserve">
    <value>&lt;GridEXLayoutData&gt;&lt;RootTable&gt;&lt;Key&gt;Tour&lt;/Key&gt;&lt;Caption&gt;Tour&lt;/Caption&gt;&lt;Columns Collection="true" ElementName="Column"&gt;&lt;Column0 ID="TourId"&gt;&lt;Caption&gt;Tour Id&lt;/Caption&gt;&lt;DataTypeCode&gt;Int64&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;TourId&lt;/DataMember&gt;&lt;EditType&gt;NoEdit&lt;/EditType&gt;&lt;Key&gt;TourId&lt;/Key&gt;&lt;Position&gt;0&lt;/Position&gt;&lt;/Column0&gt;&lt;Column1 ID="CoordinatorId"&gt;&lt;Caption&gt;Coordinator Id&lt;/Caption&gt;&lt;DataTypeCode&gt;Int64&lt;/DataTypeCode&gt;&lt;DataMember&gt;CoordinatorId&lt;/DataMember&gt;&lt;Key&gt;CoordinatorId&lt;/Key&gt;&lt;Position&gt;1&lt;/Position&gt;&lt;/Column1&gt;&lt;Column2 ID="PriceAmount"&gt;&lt;Caption&gt;Price Amount&lt;/Caption&gt;&lt;DataTypeCode&gt;Decimal&lt;/DataTypeCode&gt;&lt;DataMember&gt;PriceAmount&lt;/DataMember&gt;&lt;FormatString&gt;c&lt;/FormatString&gt;&lt;Key&gt;PriceAmount&lt;/Key&gt;&lt;Position&gt;2&lt;/Position&gt;&lt;/Column2&gt;&lt;Column3 ID="Status"&gt;&lt;Caption&gt;Status&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;System.String&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;Status&lt;/DataMember&gt;&lt;Key&gt;Status&lt;/Key&gt;&lt;MaxLength&gt;50&lt;/MaxLength&gt;&lt;Position&gt;3&lt;/Position&gt;&lt;/Column3&gt;&lt;Column4 ID="CreatedAt"&gt;&lt;Caption&gt;Created At&lt;/Caption&gt;&lt;DataTypeCode&gt;DateTime&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;CreatedAt&lt;/DataMember&gt;&lt;DefaultGroupInterval&gt;Date&lt;/DefaultGroupInterval&gt;&lt;EditType&gt;CalendarCombo&lt;/EditType&gt;&lt;FormatString&gt;d&lt;/FormatString&gt;&lt;Key&gt;CreatedAt&lt;/Key&gt;&lt;Position&gt;4&lt;/Position&gt;&lt;/Column4&gt;&lt;Column5 ID="UpdatedAt"&gt;&lt;Caption&gt;Updated At&lt;/Caption&gt;&lt;DataTypeCode&gt;DateTime&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;UpdatedAt&lt;/DataMember&gt;&lt;DefaultGroupInterval&gt;Date&lt;/DefaultGroupInterval&gt;&lt;EditType&gt;CalendarCombo&lt;/EditType&gt;&lt;FormatString&gt;d&lt;/FormatString&gt;&lt;Key&gt;UpdatedAt&lt;/Key&gt;&lt;Position&gt;5&lt;/Position&gt;&lt;/Column5&gt;&lt;Column6 ID="PaxBooked"&gt;&lt;Caption&gt;Pax Booked&lt;/Caption&gt;&lt;DataTypeCode&gt;Int32&lt;/DataTypeCode&gt;&lt;DataMember&gt;PaxBooked&lt;/DataMember&gt;&lt;Key&gt;PaxBooked&lt;/Key&gt;&lt;Position&gt;6&lt;/Position&gt;&lt;/Column6&gt;&lt;Column7 ID="TourCode"&gt;&lt;Caption&gt;Tour Code&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;System.String&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;TourCode&lt;/DataMember&gt;&lt;Key&gt;TourCode&lt;/Key&gt;&lt;MaxLength&gt;200&lt;/MaxLength&gt;&lt;Position&gt;7&lt;/Position&gt;&lt;/Column7&gt;&lt;Column8 ID="ArrivalDate"&gt;&lt;Caption&gt;Arrival Date&lt;/Caption&gt;&lt;DataTypeCode&gt;DateTime&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;ArrivalDate&lt;/DataMember&gt;&lt;DefaultGroupInterval&gt;Date&lt;/DefaultGroupInterval&gt;&lt;EditType&gt;CalendarCombo&lt;/EditType&gt;&lt;FormatString&gt;d&lt;/FormatString&gt;&lt;Key&gt;ArrivalDate&lt;/Key&gt;&lt;Position&gt;8&lt;/Position&gt;&lt;/Column8&gt;&lt;Column9 ID="DepartureDate"&gt;&lt;Caption&gt;Departure Date&lt;/Caption&gt;&lt;DataTypeCode&gt;DateTime&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;DepartureDate&lt;/DataMember&gt;&lt;DefaultGroupInterval&gt;Date&lt;/DefaultGroupInterval&gt;&lt;EditType&gt;CalendarCombo&lt;/EditType&gt;&lt;FormatString&gt;d&lt;/FormatString&gt;&lt;Key&gt;DepartureDate&lt;/Key&gt;&lt;Position&gt;9&lt;/Position&gt;&lt;/Column9&gt;&lt;Column10 ID="Allotment"&gt;&lt;Caption&gt;Allotment&lt;/Caption&gt;&lt;DataTypeCode&gt;Int32&lt;/DataTypeCode&gt;&lt;DataMember&gt;Allotment&lt;/DataMember&gt;&lt;Key&gt;Allotment&lt;/Key&gt;&lt;Position&gt;10&lt;/Position&gt;&lt;/Column10&gt;&lt;Column11 ID="Notes"&gt;&lt;Caption&gt;Notes&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;System.String&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;Notes&lt;/DataMember&gt;&lt;Key&gt;Notes&lt;/Key&gt;&lt;MaxLength&gt;2000&lt;/MaxLength&gt;&lt;Position&gt;11&lt;/Position&gt;&lt;/Column11&gt;&lt;/Columns&gt;&lt;GroupCondition /&gt;&lt;/RootTable&gt;&lt;RowWithErrorsFormatStyle&gt;&lt;PredefinedStyle&gt;RowWithErrorsFormatStyle&lt;/PredefinedStyle&gt;&lt;/RowWithErrorsFormatStyle&gt;&lt;LinkFormatStyle&gt;&lt;PredefinedStyle&gt;LinkFormatStyle&lt;/PredefinedStyle&gt;&lt;/LinkFormatStyle&gt;&lt;CardCaptionFormatStyle&gt;&lt;PredefinedStyle&gt;CardCaptionFormatStyle&lt;/PredefinedStyle&gt;&lt;/CardCaptionFormatStyle&gt;&lt;GroupByBoxFormatStyle&gt;&lt;PredefinedStyle&gt;GroupByBoxFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupByBoxFormatStyle&gt;&lt;GroupByBoxInfoFormatStyle&gt;&lt;PredefinedStyle&gt;GroupByBoxInfoFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupByBoxInfoFormatStyle&gt;&lt;GroupRowFormatStyle&gt;&lt;PredefinedStyle&gt;GroupRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupRowFormatStyle&gt;&lt;GroupTotalRowFormatStyle&gt;&lt;FontBold&gt;True&lt;/FontBold&gt;&lt;PredefinedStyle&gt;GroupTotalRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupTotalRowFormatStyle&gt;&lt;HeaderFormatStyle&gt;&lt;PredefinedStyle&gt;HeaderFormatStyle&lt;/PredefinedStyle&gt;&lt;/HeaderFormatStyle&gt;&lt;PreviewRowFormatStyle&gt;&lt;PredefinedStyle&gt;PreviewRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/PreviewRowFormatStyle&gt;&lt;RowFormatStyle&gt;&lt;PredefinedStyle&gt;RowFormatStyle&lt;/PredefinedStyle&gt;&lt;/RowFormatStyle&gt;&lt;SelectedFormatStyle&gt;&lt;PredefinedStyle&gt;SelectedFormatStyle&lt;/PredefinedStyle&gt;&lt;/SelectedFormatStyle&gt;&lt;SelectedInactiveFormatStyle&gt;&lt;PredefinedStyle&gt;SelectedInactiveFormatStyle&lt;/PredefinedStyle&gt;&lt;/SelectedInactiveFormatStyle&gt;&lt;TotalRowFormatStyle&gt;&lt;FontBold&gt;True&lt;/FontBold&gt;&lt;PredefinedStyle&gt;TotalRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/TotalRowFormatStyle&gt;&lt;FocusCellFormatStyle&gt;&lt;BackColor&gt;167, 205, 240&lt;/BackColor&gt;&lt;BackColorGradient&gt;167, 205, 240&lt;/BackColorGradient&gt;&lt;PredefinedStyle&gt;FocusCellFormatStyle&lt;/PredefinedStyle&gt;&lt;/FocusCellFormatStyle&gt;&lt;WatermarkImage /&gt;&lt;GridLineStyle&gt;Solid&lt;/GridLineStyle&gt;&lt;BorderStyle&gt;None&lt;/BorderStyle&gt;&lt;BackColor&gt;White&lt;/BackColor&gt;&lt;VisualStyle&gt;Office2010&lt;/VisualStyle&gt;&lt;OfficeColorScheme&gt;Blue&lt;/OfficeColorScheme&gt;&lt;AllowDelete&gt;True&lt;/AllowDelete&gt;&lt;AllowEdit&gt;False&lt;/AllowEdit&gt;&lt;FilterMode&gt;Automatic&lt;/FilterMode&gt;&lt;FocusStyle&gt;None&lt;/FocusStyle&gt;&lt;HideSelection&gt;Highlight&lt;/HideSelection&gt;&lt;RowHeaders&gt;True&lt;/RowHeaders&gt;&lt;SelectionMode&gt;MultipleSelectionSameTable&lt;/SelectionMode&gt;&lt;BuiltInTexts ID="LocalizableStrings" Collection="true"&gt;&lt;GroupByBoxInfo&gt;Σύρετε μια στήλη εδώ για να ταξινομήσετε κατά αυτή τη στήλη.&lt;/GroupByBoxInfo&gt;&lt;CalendarNoneButton&gt;Καμία&lt;/CalendarNoneButton&gt;&lt;CalendarTodayButton&gt;Σήμερα&lt;/CalendarTodayButton&gt;&lt;/BuiltInTexts&gt;&lt;DefaultFilterRowComparison&gt;Contains&lt;/DefaultFilterRowComparison&gt;&lt;FilterRowUpdateMode&gt;WhenValueChanges&lt;/FilterRowUpdateMode&gt;&lt;/GridEXLayoutData&gt;</value>
  </data>
  <data name="vehiclesGrid_Layout_0.LayoutString" xml:space="preserve">
    <value>&lt;GridEXLayoutData&gt;&lt;RootTable&gt;&lt;Key&gt;Vehicles&lt;/Key&gt;&lt;Caption&gt;Vehicle&lt;/Caption&gt;&lt;Columns Collection="true" ElementName="Column"&gt;&lt;Column0 ID="VehicleId"&gt;&lt;Caption&gt;Vehicle Id&lt;/Caption&gt;&lt;DataTypeCode&gt;Int64&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;VehicleId&lt;/DataMember&gt;&lt;EditType&gt;NoEdit&lt;/EditType&gt;&lt;Key&gt;VehicleId&lt;/Key&gt;&lt;Position&gt;0&lt;/Position&gt;&lt;/Column0&gt;&lt;Column1 ID="Type"&gt;&lt;Caption&gt;Type&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;System.String&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;Type&lt;/DataMember&gt;&lt;Key&gt;Type&lt;/Key&gt;&lt;MaxLength&gt;100&lt;/MaxLength&gt;&lt;Position&gt;4&lt;/Position&gt;&lt;/Column1&gt;&lt;Column2 ID="Make"&gt;&lt;Caption&gt;Make&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;Make&lt;/DataMember&gt;&lt;Key&gt;Make&lt;/Key&gt;&lt;MaxLength&gt;100&lt;/MaxLength&gt;&lt;Position&gt;2&lt;/Position&gt;&lt;/Column2&gt;&lt;Column3 ID="Model"&gt;&lt;Caption&gt;Model&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;Model&lt;/DataMember&gt;&lt;Key&gt;Model&lt;/Key&gt;&lt;MaxLength&gt;100&lt;/MaxLength&gt;&lt;Position&gt;3&lt;/Position&gt;&lt;/Column3&gt;&lt;Column4 ID="PlateNumber"&gt;&lt;Caption&gt;Plate Number&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;System.String&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;PlateNumber&lt;/DataMember&gt;&lt;Key&gt;PlateNumber&lt;/Key&gt;&lt;MaxLength&gt;50&lt;/MaxLength&gt;&lt;Position&gt;1&lt;/Position&gt;&lt;/Column4&gt;&lt;Column5 ID="Capacity"&gt;&lt;Caption&gt;Capacity&lt;/Caption&gt;&lt;DataTypeCode&gt;Int32&lt;/DataTypeCode&gt;&lt;DataMember&gt;Capacity&lt;/DataMember&gt;&lt;Key&gt;Capacity&lt;/Key&gt;&lt;Position&gt;5&lt;/Position&gt;&lt;/Column5&gt;&lt;Column6 ID="ProviderCompany"&gt;&lt;Caption&gt;Provider Company&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;ProviderCompany&lt;/DataMember&gt;&lt;Key&gt;ProviderCompany&lt;/Key&gt;&lt;MaxLength&gt;200&lt;/MaxLength&gt;&lt;Position&gt;6&lt;/Position&gt;&lt;/Column6&gt;&lt;Column7 ID="ContactPhone"&gt;&lt;Caption&gt;Contact Phone&lt;/Caption&gt;&lt;DataTypeCode&gt;String&lt;/DataTypeCode&gt;&lt;DataMember&gt;ContactPhone&lt;/DataMember&gt;&lt;Key&gt;ContactPhone&lt;/Key&gt;&lt;MaxLength&gt;50&lt;/MaxLength&gt;&lt;Position&gt;7&lt;/Position&gt;&lt;/Column7&gt;&lt;Column8 ID="CreatedAt"&gt;&lt;Caption&gt;Created At&lt;/Caption&gt;&lt;DataTypeCode&gt;DateTime&lt;/DataTypeCode&gt;&lt;TypeNameEmptyStringValue&gt;NULL&lt;/TypeNameEmptyStringValue&gt;&lt;EmptyStringValue /&gt;&lt;DataMember&gt;CreatedAt&lt;/DataMember&gt;&lt;DefaultGroupInterval&gt;Date&lt;/DefaultGroupInterval&gt;&lt;EditType&gt;CalendarCombo&lt;/EditType&gt;&lt;FormatString&gt;d&lt;/FormatString&gt;&lt;Key&gt;CreatedAt&lt;/Key&gt;&lt;Position&gt;8&lt;/Position&gt;&lt;/Column8&gt;&lt;/Columns&gt;&lt;GroupCondition /&gt;&lt;/RootTable&gt;&lt;RowWithErrorsFormatStyle&gt;&lt;PredefinedStyle&gt;RowWithErrorsFormatStyle&lt;/PredefinedStyle&gt;&lt;/RowWithErrorsFormatStyle&gt;&lt;LinkFormatStyle&gt;&lt;PredefinedStyle&gt;LinkFormatStyle&lt;/PredefinedStyle&gt;&lt;/LinkFormatStyle&gt;&lt;CardCaptionFormatStyle&gt;&lt;PredefinedStyle&gt;CardCaptionFormatStyle&lt;/PredefinedStyle&gt;&lt;/CardCaptionFormatStyle&gt;&lt;GroupByBoxFormatStyle&gt;&lt;PredefinedStyle&gt;GroupByBoxFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupByBoxFormatStyle&gt;&lt;GroupByBoxInfoFormatStyle&gt;&lt;PredefinedStyle&gt;GroupByBoxInfoFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupByBoxInfoFormatStyle&gt;&lt;GroupRowFormatStyle&gt;&lt;PredefinedStyle&gt;GroupRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupRowFormatStyle&gt;&lt;GroupTotalRowFormatStyle&gt;&lt;FontBold&gt;True&lt;/FontBold&gt;&lt;PredefinedStyle&gt;GroupTotalRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/GroupTotalRowFormatStyle&gt;&lt;HeaderFormatStyle&gt;&lt;PredefinedStyle&gt;HeaderFormatStyle&lt;/PredefinedStyle&gt;&lt;/HeaderFormatStyle&gt;&lt;PreviewRowFormatStyle&gt;&lt;PredefinedStyle&gt;PreviewRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/PreviewRowFormatStyle&gt;&lt;RowFormatStyle&gt;&lt;PredefinedStyle&gt;RowFormatStyle&lt;/PredefinedStyle&gt;&lt;/RowFormatStyle&gt;&lt;SelectedFormatStyle&gt;&lt;PredefinedStyle&gt;SelectedFormatStyle&lt;/PredefinedStyle&gt;&lt;/SelectedFormatStyle&gt;&lt;SelectedInactiveFormatStyle&gt;&lt;PredefinedStyle&gt;SelectedInactiveFormatStyle&lt;/PredefinedStyle&gt;&lt;/SelectedInactiveFormatStyle&gt;&lt;TotalRowFormatStyle&gt;&lt;FontBold&gt;True&lt;/FontBold&gt;&lt;PredefinedStyle&gt;TotalRowFormatStyle&lt;/PredefinedStyle&gt;&lt;/TotalRowFormatStyle&gt;&lt;FocusCellFormatStyle&gt;&lt;BackColor&gt;167, 205, 240&lt;/BackColor&gt;&lt;BackColorGradient&gt;167, 205, 240&lt;/BackColorGradient&gt;&lt;PredefinedStyle&gt;FocusCellFormatStyle&lt;/PredefinedStyle&gt;&lt;/FocusCellFormatStyle&gt;&lt;WatermarkImage /&gt;&lt;GridLineStyle&gt;Solid&lt;/GridLineStyle&gt;&lt;BorderStyle&gt;None&lt;/BorderStyle&gt;&lt;BackColor&gt;White&lt;/BackColor&gt;&lt;VisualStyle&gt;Office2010&lt;/VisualStyle&gt;&lt;OfficeColorScheme&gt;Blue&lt;/OfficeColorScheme&gt;&lt;AllowDelete&gt;True&lt;/AllowDelete&gt;&lt;AllowEdit&gt;False&lt;/AllowEdit&gt;&lt;FilterMode&gt;Automatic&lt;/FilterMode&gt;&lt;FocusStyle&gt;None&lt;/FocusStyle&gt;&lt;HideSelection&gt;Highlight&lt;/HideSelection&gt;&lt;RowHeaders&gt;True&lt;/RowHeaders&gt;&lt;SelectionMode&gt;MultipleSelectionSameTable&lt;/SelectionMode&gt;&lt;BuiltInTexts ID="LocalizableStrings" Collection="true"&gt;&lt;GroupByBoxInfo&gt;Σύρετε μια στήλη εδώ για να ταξινομήσετε κατά αυτή τη στήλη.&lt;/GroupByBoxInfo&gt;&lt;CalendarNoneButton&gt;Καμία&lt;/CalendarNoneButton&gt;&lt;CalendarTodayButton&gt;Σήμερα&lt;/CalendarTodayButton&gt;&lt;/BuiltInTexts&gt;&lt;DefaultFilterRowComparison&gt;Contains&lt;/DefaultFilterRowComparison&gt;&lt;FilterRowUpdateMode&gt;WhenValueChanges&lt;/FilterRowUpdateMode&gt;&lt;/GridEXLayoutData&gt;</value>
  </data>
  <data name="accommodationsNavigationPanel.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        EAAACxABrSO9dQAABdJJREFUaEPtmFtsFFUYx+slIfqmUbtniuAF9EGiERP1QRIxPlgK8UGIMSG+cDGi
        BPGyZ9oCNQqo5UkLNhGjZHdnZ1nuoFVKG62A2y5SoHdKaXfp/Ua33e1lt6Wf+YZOO/vNbKfdAm1I/8kv
        aXZmzvy+2TPfOduEhNncpREttlXcYmtAUi37VtLjMzqc2TdxZr8hMjvcxDbMmf1Let4MDNwjMlvmmDjB
        YvshIyHjXnrVjMjGBd/PES12WSdNsdgOb57rfoBeP60R50kPicxeIDIHTBDPp0x6hI4zLUm3OOZz5ig3
        kIRdS44o0M8RvAavpePd0aQm2haJFkc9lUOyVpyAmoo6uFpVB9krc3THR4poSmP2xXTcOxKeaHtDZPYA
        lUJ+XZsL13z1EAgEoKurC/y+a2DbkKc772YR9qDV4kim49/WcItzNbc4wiKTQEuqIMGBLf9AY2MjdHd3
        QzgcVsC/8bNj355VzqHXceYY5ExaS+9zW8KZtIkz6QaVSHvcCbnZXmhqaoKenh6IRCKjqEXgsVN7vZA+
        X9YVITLHMGfS7VsrViW47xOZtFt/Ywm2Pu2CAmcxNDc3QzAYjBLXgsfwnNPuYti2cL9uHIRbpJ8zEv66
        n95/Stm4IGeOlTndXHAC5asXDsK53DJobW2F3t5eQ3GVgYEBpYiWlhb4L68Mti8+pBsPsQrOoxns+IPU
        I65snut+mAvO0/QmyHevHYUy72Vob2+Hvr4+nbBWXEsoFFKKKPVWwq6lx3TjKkUwqTDV4n6U+kwqnyXa
        nrQKzko6OJL19u9QU1ELnZ2d0N/fr5M2EkfwXASLaGtrg8vlV2DPOzm68RWYVPNFkrSQek0onyc5n7cy
        ZwMXZKD8si4PfHV+pUUayVNprbgWnHL47V2tqYV9H+bp7oNYBblFZNJL1G/ciILrTasgd9PBxCQZDmw5
        DQ0NDXD9+nVFLB5xBKccgkV0dHSAz+eDIzvPgDjXpSuCC3KIJzlTqKdheJLrfc7kCB0kdZ4LTmZ7lX6O
        C5RWnkpPRFwLTicswu/3w8m9RZD2xH5aAE6pIZHJ66lvVKzM9QkX5GEu4FMYY9uzB+HMoYtKH8d+Hgk1
        wlBlOgwXvwtDVVsgHGxQpEt3dkLRurZJU/ZNp/JN4LdaX18PBe7zsPWZA1EON5GHOZM3Ue/RWJkrn160
        ffEROJ9frvRvXKDwiQ+WcxgqSh5lsEJUnm7h+ta4wW8BwSJwinpzS+DrFw/rirAy+RT1Hg1n7nlckP3q
        yZlLjkNpUZXS43FwdXqEC5dB+OzSMQpTblkB2iIuesoh83VssyMFMFej+Jj0FPWOijXR+RwXXP6fVp+C
        KxVXlVanlUf6LnwEfX+/OsaFj0cKaIkbvAcucirY4XDKVlyqguz3TmIBPnSjvjGDLxS+WDg3dS9ooBZC
        59ZCKP8VCJ1bB/2BOuVlLFzfHDeqOE5TFbWI6upqoH6mwQUKpYw6y+7l+ERol4ifzEU5UeLYKFTUIqif
        aVCUiqvQDditgIoj2K7V3xTUzzRG4mof/3HFH/of61Ngz4ocQ3FVPq4CjMSNwHeEou0o9OXUThX61I3E
        EexK1M80M0VchfqZhgpPlziCDYX6mWY88b6AHwYr0pStxGBlOvR2+RTxkh0dum3CRCjZ0R5TXIX6mcZI
        XCVSZo3aSkTKufK06eo6GWKJ41qEUD/TUGntVBnwJEdtJQY8y0YK0K+wEyWWOIK/GaifaYzEVXqLN0Rt
        JULnNyjzm66ukyGWuAr1M42R+OgL2lENQe8aCOa9DD1FayDYcUV5KT0fNMVNLHHciyHUzzSG4iNkpfyp
        2w5MBdxKUHGt/JQKMGqJdBtwK4gljlt5hPqZxkhc7eN7luM/a/VbgnjJSvktpjiC/4KhfqYxEjdagMZb
        hGJ1FTpdxhNXoX6mKSythZkE9TMNHWC6oX6m8ZTW1tNBpgtPSe016mcaz6W65JlQBMr/W1b3FvWbzWxm
        c5fkf/h5i86yk0FgAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="toursNavigationPanel.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        EAAACxABrSO9dQAACnFJREFUaEPt2XlQFFceB3DEI3IqKCCCSqIoHom3aNCQZMVN1LLcuG4M8Ug0hBiN
        SsyhJBFXBEEBUS5HBDEYVwFBQG4MCgKCyC0eGJUBuWa4Bpie7unxu9WjjEz3DIfR1G6V36r37/TnNb/3
        6/ceGhqv8iqv8ucTgYEaSdQCzSTKWTOBPK+bQNzeVCilnUpl2FZM48vrUtm6XIpYmUnx7dKJiwuTOtZp
        uECT/TN/fZIxZkCa1EMzmarRTKIwMIHEwHgJ9OMl+OiaFF8X0dhRJMO2GzJ8c12GLfk0Nl+j4ZhDw+Zi
        h0grVHhA67jQnP2zLz+ZMBqQLuMNSJOSmilSdMcPiiUw6IIYg6M78U4GqRLvcJXGzJh2aIUIoH28gdTm
        NQTp8h6NZD/m5eQS7AdcopsGpNHoCT84qgOzUgiVePsMCrqhQmgHN0Kb1wCdoHpoB9YJdfxq1rAf9+JS
        gMEal2TBRpkyzM2T4b3rND4ooLEgWwrzVIqDHxLRjrcSxRz8piwaUyLalPA6gXXQ9a/FML9azAlvyRp3
        8sFQ9uP/VExSoLOgQFa8/e5j7P3jMfZUPsbPd2Vwvi3DrgoZfiiXYeU1KQzixQr8kLMiLE4nOPgZ59tV
        4nWPPsLCs+34R7wUtmc76+f6tY1gO54rtg8w9Ivbsvp9D9TjvyuT4dsSGRwLaBjFdMrxr51pxeorlAK/
        /ooUk86pfvMM3jJEKMevjJViRYwUNuEdjebefC22p99ZWyG7qQrvWELDJEkCrVgxJqcSsL1MYkUmBbOY
        Tjn+tdMt+GcGJcevSidhEt6sFm/gV4ulMRIFfvl5CksjKViHtpaxPf3KsjIcUYXfUkpj2EUJp+a73jyD
        HxreDOv4DljHdXAWbHe8nm8N5oS3YeUFCsujxFgW0YmlESQ+iKCw5CyF6UFCL7arT1lcCMtdlfRjNp4p
        mwlpveOHnmqC1knhk1bZA17Ppxp25zqx4nQ1vOPc4R3rhg/DqmB3hsDiMxTeCRM/nuzbaMn29Rr7Mlm5
        KvymQloJrxXZjvfTJdh0TYoP0ggYnqrH0BM10App6BNe/+ADmO3PQv7VGaBuasjHlSt2eC+kCe+fpvBu
        OIUZ/i0lbF+PWVyKOb/c4+KZBfuvfFqBN47pxCdXpU9aZa4Un6c2ISTWGYFn7LEo6Cy0j1RC278aOoG1
        avGj92ch58qbCjwzRGVaWBRUJ8e/c4qCTagElt78eWyn2nxcSueqwjPdZluxDBYJBOakEvgqj1b0eYcs
        CfbFRUOWpKEYZdET8P2JnRjrnQld70ro+lZx8LksPDMuXf47FgU3yfELT5J4O4TENG/hVbZTZWwzMMjp
        Fk2rwjOtUt3exiFLinVJTeBdOAAycZDSRIjEwUj4z0J86ueJ4W7F0D9wFyau2ci8PJODL8qZhSXH72DR
        SUKBnx9MYlZgJ63hkjGI7eVkSQHt0G/804/U55elsE8WYfWJU2iIN1CaRNeojjXCPt6XyMzoAR8qVsLP
        O05izjES410b1rG9nHx0g055LvwVGqvTKLwe3gydo3xY+qQjL2oqZwIdCVooyrTsN35WIImJ7oJktpeT
        dUX0o/7iP06nMDOqHXrBjc+6zVE+DD0LEfbriheCn+lPwsqzhc/2cvJlsVTSF/zGLCmsIkXyj5SOulZ5
        mA9dtwps9d+N5ou6KvG3cifD7tgtTs2z8dP9SEz2EhFsLyfbSqSPe8Mzb372hY4+9vn7MHbNRlbGWxz8
        ndyJaE/SwUZeHGxCxD3i3zwiwWSvDhnbywlzDOwNz5SN8a8q9jZHqqDreRt6nnehd+iBHM90m8sZzz5S
        bDxTWlFnPsH8oJYe8VMPMxMQg+3lZEM+9ZiNX5xGYHm6BKt+p/BZphTrL0u5ZXOUj5leMciOnI7I03aw
        9/WAhWt6r3hmiBJ0YeN7D/N4EvV4bwkmHerDBCYkikXd8e+nEkp7m8kRbfgoleSWjfcfcA12fNZxkjXA
        zzXi4JkFu++UF+ikAUrd6afj3pgd0KEe7yXBhAN9KKEh50Tli1IlirKZGC1S2pgZhTXBLp7g1vzTBbvW
        9wAEccMgva4MZ0Zh9kx5t7HhCRF2+iulCeRFWmO6d6NavOVBCd7Y19r7Ih58ti2a2VVaJ4rlNb8mg8Kq
        SxRMn+7ndXgNMDkhUL1gvfjQd7uL8a4pIMo0lfA3sqY9+8KGSjD/aC2yIm0VE2D+Ist8sjD9KKESP95D
        grHOjb230SG/tTp3bYknnhfJD+DMR2pJAsEtGzb+YBWGeT7E7qQaiO4tV+ClRRpIj7CDDa9Z0SqteWLY
        +t7EwwvjFJMICt2CaV6tKvGvu0sw5sf6RLaXE61w4fzuNa8TKsAbv7XALo7Agqh2WIQJYREqhMUJASyO
        N2JMUAOGezPt8gn+pzQBCAkJMdGGsKQ9qE4zkq+HzoSheDegEm+fkCi6DVPzq3xT0JGgLZ9AbZwJprpV
        q8SP2y/B2B0Pe99KMDdmr50S8lUdRqwj2vFxshSrE6VYdVGqOMMuPU9iCk+InSkCtHaSaBeTcIyqhcHP
        N/G3vcEgEwfKgRsPH8NUrwZYHWzERPd6TDnUhOk+rXAK8Fcs6ncP5Mi7DRtvvqdNKr8B7EvkN2YqPlLG
        vAYOvusM63WNxKNWEg0iEl9E1cHApRKGv9yFwY9F8PH/FPUXDDB1ZzRG7iyD0bflMNpRBuPtpRi76x6m
        HBTicLAT/IO3YIJbIwc/xpWA2Xd12Wyn2jDXfU9vzJRq3jCgTiV+/1USN+tJVApIOEQ3KvCGP9/BCOdb
        GLkjFxZOyRjpVKSEN/mmBCZbimHl0QordyEmuglh6Ulw8Xs7MfqLOzPYzh7DXPexF+wIv1oOfncGid/v
        k8jjk3D+nZIfA+eHdGLqEQFM9/6BEbtuYeQPFRj5XblK/KjNRXjDpV5lzcvx/yZgurOuf0dKJvrBfEPt
        gDoBu9ssjyEV+K2pFMJLKcTdIvFt2hN81zGQ6TYz/Fqf4m+qxY9yLMS47++rx/8kkhl9Uzae7etTmLtK
        dqucfbJVjl8bR2F/FoWAfApfJXLxTKuc4d/WK97U4QbMNpepxrsQMN1e/XzXKl3R86051r3PG3tX48MI
        Cp/FUfK3vjZWNZ5plZM8G3rFm24qwOiN12Hh0sLFO9UWsT39TwQG6h+qiunq86N9amF9sgPLzlFYEaEe
        z/R5c+d7fcKP/iwf5tvuK+FH7Wys19jwgi55TV0eaQ/zeJgw3LMKZofrMClQCNtwSY94q0PCPuPN1ufB
        bGNRN7ygbtyGB8PZjj+XiPIh+h4PSwwPVcPEg68WP5fHbLpq+odfew3m9rkw3d0K0x11RS/szavK8H33
        thvsqRQsDCM4+Hk8EhYuVc+FN1+T02i+qWIr+3kvJXq7Kka85d98nbkx6142lu51/cab2edKzNZk+5mv
        zjFkP+elx+qIYPY0n+bsWQEd9DSfVhg7lfcDn8s3/yTX3Xx1jhn7d//6uGCQ8Q8V8422le4y3locafJ1
        UanJ5sImE8cb5CiHAnLUxoIm08/zS0w35EWarc/70fzT/Ln/G/9mfZVX+f/PfwEZR+7dmH/Z3AAAAABJ
        RU5ErkJggg==
</value>
  </data>
  <data name="attractionsNavigationPanel.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        EAAACxABrSO9dQAABjhJREFUaEPtWOlPVFcU589o0r+h/d4P/dBW0+LSRNuYaBWQfRZmgWEWRHGmi6aA
        OgzLwAAyAyKbW1zikpimtYlWxKUJ/aACCoJRZn+D0Gp+zbnDu8zcGRwkjNpmfskv8zLv3HN+97xzz73v
        ZWVlkEEGGfwvoLbYtqpN1qkysw2vI9loTNYt4vh3CpXZ+onabJ0XxS5P699qi/VT0c/ao3L09yzjX0hg
        5ehvsonSUPPBSjKfhDP6qh8+jA+41hCFxzIrK0tjqflYbbLdTiJuRaSxKpPtIzHsqqE2WTeuMptvhWqz
        bVJttGaLujnIQBz03tFkfSzq5kgwXiVVxv1QVu6DwrCXka7pP9FutRR1c4iGb0KVsQYl5XtQpDWjoMyI
        3SpDHOk/ukc2ZCuOfxOKujlEw5VQbdqPEv0eJlBlqIbL3Yvh2/cwPfMU8/MLjHQ9PHKX3VNVRG1pDI0V
        /REd7d149GQGE1PTaHB1J9wXdXOIhqmorKxhWS3WmjF4+hzm5l4gFSJzcxg4dZaNobHkQ/RLwn2hMOP4
        1HTCfVE3h2j4OlJdF2pM0Jhq8HB8QtSZEhOPJ6GvsjEf5CvWd9onQDVcqDVDZ9kPr88fJyz0/BXuXV3A
        JXcEA00SPC0S+nsl/HJtHs+8r+JsaazWHPUVuy6obGgSJN7e5kmIL+rmEA2T04pinQVFGhPGJh5zMa9e
        AqNXFnCpLoKzdRJOHJLQdziMLnsYTkcIR5pDONAaxNlrc3gZM4+H449QqDEyn4mxklPUzSEaJiO1Reoq
        VPOx4kf65nHl5wgu1UZwrk7CyXoJ/YfDcNvDaHWEYG8K4YAziOq2AJovSPgnZhJ9J84wn4qKvQnxklHU
        zSEaJtKKIp0FioqquAU7enmBib9cG8HF2ugTOJnkCfy0OAF9hx+91+f4eFrYivIq5ptiJMZdowlQx8hX
        V8LVdYwHp5qnsqHMk/jzdRGcqZcwdCiM3iNhdDaE0dIYwuHmEH50BlHlCkDX4Uex249J/0vup7Wzm/lO
        1pVEiro5REORpeXVyFOU4+bIXR74z6sLLOPnFmufxFP9U/l47GG4HCE0NoVQ1xKCrTUIsyuAsk4/drt9
        6Bpeegp/3LrNfFMMMa5IUTeHaCiyWFeFnFIdZp4+44EvuyOsXE7VR8uGMk/ie2Kyb28O4aAziH1tQRja
        A1B2+pHj8UF7PsD9PJl5ynxTDDGuSFE3h2gokmp0V4kWL17M88DUKqnWSTT9Utl026PiafE6mkKobQnh
        +9YgLIvlU9Tlx/ZuH7YN+rgf8km+o+sgMfbaTEBrxq7i+An0OCXWaWQebQijvSEMZ2NUfP1i7dPipeyr
        Ov3IdfvwbY8XW4eWJkBNgXxTDDGuSFE3h2goUn4CdLaR0dcrsUzLpJJppLJZrHsST6VjbA+grMOPwsXs
        f93rRcHFpU1wanom/U9AXgO04GTQDktiZVK3IeFU81Q2e9sCTLyGOs9RP3Z6fNh6zIsv+7xw3JW4nxvD
        I+lfA3IXcnZ4eGA6HhxsDbJNikgZp25DWaeap7KRxe/y+PBNjxcbjnvx2eAsxoJLbbTZ1ZX+LiTvAyVa
        IyKRpRZIxwPKNNX5HleAtUoSTguWap7KhjJPdb+RxPfPov7OUvalSARFmsr07wPyTkyP+vjQaS6AzjYt
        FyToO6KiKePUKqnb5Ll9rOapbFjm+2dR9msg7ihxbOAE85n2nZgon4XyFHo8GBvnIkjQ8etzbIfN7/Ih
        x+3Dju5o1mnBUs1/PhjNfKz4+w/HmC92FjKk/SwUJZ0ccxXlUFVUYda71AoJdDygHVZ3PoBtAz5sGfSx
        buO4I8XVPOH5rBfK8qivt3YaJcrvA/TYFXoL7j8YixO2EoxNPILaUM18iO8DqSjq5hANX0f5jSynVI/c
        Uh2r49iFvRxowfb0DSG3RMvGJnsjS0VRN4domIryO3GesgI7izUoUBvQ5OpiPX3qyTTbsYl0ff3mLTS2
        HUW+KmpLY5Z7J05FUTeHaLgSxn6VoD5Ou+l3hWXYUaDC9nwlI13Tf3SPbFJ9lUhFUTeHaPgm/M9+F0rG
        d/Zlbl325sn12ZvxPnNd9qblv42uz96U/T5PgsR/sWHzV6LuDDLIIIMMVo1/AU0SPb/3hru0AAAAAElF
        TkSuQmCC
</value>
  </data>
  <data name="coordinatorsNavigationPanel.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        EAAACxABrSO9dQAACGFJREFUaEPtV3tQVNcZp52pRWEFZO/Kig9WSBTkTYy1MVg1RhF8JK1NrQZEbZI2
        acc6004bp7HTmaamaU1M66hRUbCJSiapuiyvXfbJmxWjUEiTUUR86z5QERTv/XW+c3fjcvDVlgX/8Dfz
        DXfP+b7v/r7v/M45l4CAx3iMx3gMX9hyhSTjKtWn5tXK8+ZVyh7zagGmVQJMuQKMKwVU5AioyBZgWCG4
        +dghh2WNMs+8RoCF7CfyX/rtLYIKMHoK0K8Q7Hz8kMH2M/WMunWqJiLKCiDyr/gU4bsKngJKlylryuaO
        DuJzDTosa8JfM68WRCJoXuXp/isCrK/eKYKXkeFlASUvCNAtFhqLMkPC+JyDBtNq5cY+5HJlsr4r0EdC
        uQIqiPxSAcVyAdAuFIr5vIMCY65ypnGVIBFxb2dJHsZs+bdXTl7yJJvyZQJKfiCg5EUBxUvkAooWCijK
        FJby+f0OY46gY6RXCmhcHwnHwVi4dYlo3zEZxzZoYPvpGBizVTBmj0bl62PRsjEGF/bFo7MkEZc/i4Xt
        VTV0i+QCDmcq9Xx+v8OQLbjoSKx6IwKdpQm4bkjBDeNUdJuno9v6DHqsM9Bje1Y26ww2RnPkQ74u7RSU
        /VCFoiwBhxcIDj6/31HxsvI66blte7RM3vQ0ui3fZYRvVs7EzapZuFk122Oz2BjNkQ/5UkzL2xpoM6kA
        1TU+v99hWK48acxRiVfLktBFnf+a/Czcqp6D3pq56K19XraauWyM5rxFUIzjYDy0tAIZqq/4/H5H+XJl
        df2vI291VaTihvk7TCY3K7+HW9XPobd2Hm7XLcDt+kzZ6hawMZojH/KlGIo1rxzTe2i+ysrn9zvKlwm7
        Wt7RiF3GpzzdT5c7T+TrMyE2LIRoXyRbw0I2Jhcxh/nKq/AUGn8bJR6ap9rF5/c7yn+kXNu27UncME1l
        G9Tb/dt1GYywZF8M6cgLstkXy0XUZXy9ChRDsSf+NgmHnlet5fP7FacKY+Ia/6w+c/7jOLYhvfIhrZNc
        xIZFkI4sARq/z4yeaYxJqWbuHRmZnsbF/fFo+KP6TNu+CbH8e/yGtkLNuubtkXBrE+TTx6t/VoBnBY4s
        gdT4omysAM8KeAqgGIrt1CWgeVskKCf/Hr+hbd/EjNY9kbiuT5TPfgtJaKZnA8+HWJ/FtM9kRPKhfVCf
        xeZkCc1kMexO0CeCcrUVRs/n3+M3fLk3ZmTLnsjWa+UJ6KpIYxcUO0KrZnlklHFnI3s2MI0x+VR5jlLz
        dBZLOSgX5eTf43dcK4vDdUOy5xJ7Bj22mbhVNZvJhE4c6rhs89gYzZEP6z67zJJBOfi8g4bOksk35FWg
        u2Ca/AlhS2ddpuOS5CLbHE/n0+XTxzyNxVAs5eDzDhrcuuiaq6WxTMckhxumafJKWOkbKF3+pGCfEOls
        TO48kU9jMRTr1sVU83kHDZ1FmhWdxU/gKkmJFZHKNibdst2W6eyykm06G6M58mHky+JAse6iicv4vIMG
        bAj4pqsoSseKKI3FtfJ4XNcnMZLUZbppmdEzI57EfMiXYlzaKC3l4PMOKtxF48PcRVH2Tl0MrpZMkgsp
        m8L0TZ0mo2caoznyIV+XNqrB9c8JoXy+IYFTPzHEpdUUu4s06NRFs+6yYnyMxmiOfGjVrhQPwbF5PwAB
        32g/oIFTq2Ek72Y0Rz7ky8c/EmjJ16B1r0zy0kGZMBk9ny7UoLVAA/Lh43jg9NjhaIr4Cq3hY/i5AUdS
        iSsqudz5ZmKpw0DkHsYmfnxOr/no7JtR/zg3gc9HwPGI19A0GjimepufGzCk6Z2ZyXqXJbnUISWWOpBQ
        7OhH9F6m+egcovaexYT8M9L43WcskXkdC7x5SV44pmrBMRXwucqJZiG475v/T6QYnIkpeqcxRe9CcpkT
        XvLxuiuo2ju1H1nebAVTZfIFZzF+zxmM292ByF0dUO9oN6q2nEhE0+gsD3ngqADYw1/nOfzPSDW6l6cY
        XF13Iz9FewXrCrf2I8zbLwre70d+zM7TUH94GqpNx7vba6Z8gc8FmXyjErCP+hIYgLsizehen2pw4V7k
        4w5dRuzBS/jd/j+hOT+6H/Gm/BhsKNiAqPwOjNtxAmO3/RtjtrRA/UETIjYdQ8RfGpC1PQ99yB8JpxUA
        7GE/hj3gWzynh0ZahXtpqt4pPYj85M8uYdKnFzHvgBV5e1eioSCB2a6CHMwuMLPOj9vairUHNmKnbg10
        hiWwW9Nxri4WvUfVPsSp8+FAwyjZ6sOA2jBRrAk5J1aH2MWqkYfESsUW2BQP/lc0Te8MSdU7XQ9L/slP
        LiCm8AKi95/HxH3n72xYj2zGbm3Buwd/CXbKHGcnzd27zoiPAurCINWGQqoJhVQdAqmKbCSkSgVEW3Ae
        z7cfUg3OtwaKPNP8znaoNzfhN/vfgng04j7kw+5CnoiPhGRTQLQEb36ovZFS7moeMPLeDbu9HaPfb0ZO
        3nvosav7Soa63od8iA95BUSrQoJF8Sue5z2RXOboGVDyH55GBBWw9RSETf/Coq070Fk7/j6SGelL/ias
        Qf/d57e/yKu2nILw9zaEv9uMl7ZtfqBkJGswYAl6g+f3QMTrHB3+Iq/84CRG/fUL/HznhntKxktesgRD
        NAf9gef3QCToHBlxhy93+IX85pMI3Xgcuz95qX/X+5APgmQOgmgaXsrzeyQg1oQe5SUjWoO7qetEXDKR
        jYBoDHQ8cp/isKtHiNUhvXc2anCXaAn6PewBI2AekS2ahl+UjCMgVQyHZAgEyoc9wecYUqAy+FnqumhT
        SKJNUQhb4Pg+86aQULFi+EbRENgj6QOBssAVvvNDDtgU2aI1uBpWxTR+zhcoHTZZLPu2DqXD1vuO/wcY
        K8VSiyj6mAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="vehiclesNavigationPanel.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        EAAACxABrSO9dQAABO1JREFUaEPtmF1MW2UYx3vloqLSntbyNafAzLww8TtLdF5omOI0mRe1BaZSOpO2
        LPuMW3QmuMwNthVGodB1g31kZuKNH8QbIxVjYrxzifFqJjLoJ4VNCF44TXzM+56+L+95znl7DgHu+k9+
        yclz6MPvPH3OOQSbrZxyyimnnLXK5pHFHQ2jC+nGiwvQMMr4k1I/ouWRC4TbGh4+z7hF2ZTQ8tA5wryG
        jfF5qIvP/70xPvdzbbzwGnZaUepHF1KlpFVxmbQqjqVVcb30MnNQN1xkqPBf3XChA3tZztpKa8Wl0gK1
        Q3NQE5v9p3q4sA27WYoo7Qx/DZW7Lklxhse5rGLyswQlPM7FHaGvNOfs/mvg/vg61A4VoCZWgJrBQq4u
        Pl+L/Uwj7nVl20VT2KRxXQabNK5Tdl2GqlM31AuIzULVYP5b7GcacUXsbaOmsPXAdRlkRciUcZ3h2P05
        VEdzUD04qxLNt2HHkhH32t46YgrbaVyXwVYE10VcH/wEVQOzFPdAfq46knFiT2nEG9LeesEUdkPiugx1
        PcgF6M8xHG9fBndfCtzRfJFcBHtKIz5BcGMj2E7jugy2GriOUQ4m+QW4+rN/VUWzLuxqGPGx52g5bwp7
        9OG6DHYBuK6jbRRckWl4sD9HcfXnjmNXw4jPa0dLwhSy0wRcl6Hudl5XN8J55Edwnc0Vyd5qjN7YgH11
        EV80Dt85U9hO47oMtha4bsi7V8DVlwZnX5bi6M28iX11Ed+Kii9uCnlek5XAdRnsAnBdhvPYr/wClEj6
        C+yri/hKV7zDprCdxnUZbKdxXcr+CVB6M4w7rthsBXbWhO00QfEOmcKe17gug96QZ8kF6M8Z0nGNyjsi
        KvYz2WbsrAnbaYLrvTFQ3opJce4eozckWQlyjM/rCIyRm5HiDHymP29E8MuifBrsZ9JQeTpV+p3A/g7h
        r/IibNJEmLH8olFh68EfffwJQp8iqnhxn/leL6+HZtJMmolzTqd+wc6aGItbl9aKy6RLiRtI08nT6RPu
        2Lp+uwt78xhNG0u7oyuXtjJtLF1cGQ0PnEqBvWfmcezNI5fWT3stpI3EhWlz6WVm4P6emVbszWNdWi6+
        9tJcHJKeg5BubllcbNpp/DS6/UIzfLdzbwlxmXQpcWvSenFVWoT4UZ5vnsHuNIUnXgSCFWkr08bSqvjK
        pAn3dROmqRsDu9NkG56Gb7a9syppI3E8bSxtJM6kGRUnp4H4UeqfMv4GZOJbuibhlT094AnspZDjLV0/
        rEJaO+3NHyWhqbOb9yfHjUeTVFpkfGsrXH/spX+zm558FbvTaCetTnvr4avg84cMee79T0tK68X1k372
        0BVdX8Yzh65CxcmbcO8Jkaks9ubBK0Imj5tiHu2aXLE0W5HGoxO6fpj6Dye4/D2fEKaWsDeP0ptJiXu9
        fU8Pb9R54AgMDMYo5JjVt3f2GEobieO9bgp3m/YnP1MUp9x9fOp37M1D/tqzRzIptteewD7eiDROJBIU
        cszqnsD+ktPG0uJOezqs9N9HpIv8MbXh2M2Xsbc0vvbgktkv8PqDi/hzVrPe/W0+f2iSNRK/4vCBw8Iv
        CCXx56xmvfvbWtpDb7BGUjpCO/DnrGa9+9P4/KETuqZFvP6QtX93lMh696fx+oOv+9qD39OdbQ8uka91
        1ZMRst79yymnnHLKkeZ/A9JNNpEWW74AAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="gridExporter.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>1463, 17</value>
  </metadata>
  <metadata name="gridEXPrintDocument.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>1606, 17</value>
  </metadata>
</root>