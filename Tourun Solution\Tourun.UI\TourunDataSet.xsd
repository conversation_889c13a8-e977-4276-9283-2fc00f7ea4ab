﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="TourunDataSet" targetNamespace="http://tempuri.org/TourunDataSet.xsd" xmlns:mstns="http://tempuri.org/TourunDataSet.xsd" xmlns="http://tempuri.org/TourunDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="1" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Settings" AppSettingsPropertyName="TourunConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="TourunConnectionString (Settings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Tourun.UI.Properties.Settings.GlobalReference.Default.TourunConnectionString" Provider="Microsoft.Data.SqlClient" />
          <Connection AppSettingsObjectName="Settings" AppSettingsPropertyName="TourunConnectionString1" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="TourunConnectionString1 (Settings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Tourun.UI.Properties.Settings.GlobalReference.Default.TourunConnectionString1" Provider="Microsoft.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="AccommodationTableAdapter" GeneratorDataComponentClassName="AccommodationTableAdapter" Name="Accommodation" UserDataComponentName="AccommodationTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString1 (Settings)" DbObjectName="Tourun.dbo.Accommodation" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Accommodation] WHERE (([AccommodationId] = @Original_AccommodationId) AND ([Name] = @Original_Name) AND ([Type] = @Original_Type) AND ([AddressLine1] = @Original_AddressLine1) AND ([AddressLine2] = @Original_AddressLine2) AND ([City] = @Original_City) AND ([StateRegion] = @Original_StateRegion) AND ([PostalCode] = @Original_PostalCode) AND ([Country] = @Original_Country) AND ([Phone] = @Original_Phone) AND ([Email] = @Original_Email) AND ([Website] = @Original_Website) AND ((@IsNull_Rating = 1 AND [Rating] IS NULL) OR ([Rating] = @Original_Rating)) AND ([CreatedAt] = @Original_CreatedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_AccommodationId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Type" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_AddressLine1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_AddressLine2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_City" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_StateRegion" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_PostalCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Country" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Phone" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Email" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Website" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Rating" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Rating" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Rating" Precision="3" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Rating" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Accommodation] ([Name], [Type], [AddressLine1], [AddressLine2], [City], [StateRegion], [PostalCode], [Country], [Phone], [Email], [Website], [Rating], [CreatedAt]) VALUES (@Name, @Type, @AddressLine1, @AddressLine2, @City, @StateRegion, @PostalCode, @Country, @Phone, @Email, @Website, @Rating, @CreatedAt);
SELECT AccommodationId, Name, Type, AddressLine1, AddressLine2, City, StateRegion, PostalCode, Country, Phone, Email, Website, Rating, CreatedAt FROM Accommodation WHERE (AccommodationId = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Type" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@AddressLine1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@AddressLine2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@City" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@StateRegion" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@PostalCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Country" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Phone" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Website" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Rating" Precision="3" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Rating" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT AccommodationId, Name, Type, AddressLine1, AddressLine2, City, StateRegion, PostalCode, Country, Phone, Email, Website, Rating, CreatedAt
FROM     Accommodation</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Accommodation] SET [Name] = @Name, [Type] = @Type, [AddressLine1] = @AddressLine1, [AddressLine2] = @AddressLine2, [City] = @City, [StateRegion] = @StateRegion, [PostalCode] = @PostalCode, [Country] = @Country, [Phone] = @Phone, [Email] = @Email, [Website] = @Website, [Rating] = @Rating, [CreatedAt] = @CreatedAt WHERE (([AccommodationId] = @Original_AccommodationId) AND ([Name] = @Original_Name) AND ([Type] = @Original_Type) AND ([AddressLine1] = @Original_AddressLine1) AND ([AddressLine2] = @Original_AddressLine2) AND ([City] = @Original_City) AND ([StateRegion] = @Original_StateRegion) AND ([PostalCode] = @Original_PostalCode) AND ([Country] = @Original_Country) AND ([Phone] = @Original_Phone) AND ([Email] = @Original_Email) AND ([Website] = @Original_Website) AND ((@IsNull_Rating = 1 AND [Rating] IS NULL) OR ([Rating] = @Original_Rating)) AND ([CreatedAt] = @Original_CreatedAt));
SELECT AccommodationId, Name, Type, AddressLine1, AddressLine2, City, StateRegion, PostalCode, Country, Phone, Email, Website, Rating, CreatedAt FROM Accommodation WHERE (AccommodationId = @AccommodationId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Type" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@AddressLine1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@AddressLine2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@City" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@StateRegion" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@PostalCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Country" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Phone" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Website" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Rating" Precision="3" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Rating" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_AccommodationId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Type" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_AddressLine1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_AddressLine2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_City" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_StateRegion" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_PostalCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Country" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Phone" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Email" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Website" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Rating" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Rating" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Rating" Precision="3" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Rating" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AccommodationId" ColumnName="AccommodationId" DataSourceName="Tourun.dbo.Accommodation" DataTypeServer="bigint" DbType="Int64" Direction="Input" ParameterName="@AccommodationId" Precision="0" ProviderType="BigInt" Scale="0" Size="8" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="AccommodationId" DataSetColumn="AccommodationId" />
              <Mapping SourceColumn="Name" DataSetColumn="Name" />
              <Mapping SourceColumn="Type" DataSetColumn="Type" />
              <Mapping SourceColumn="AddressLine1" DataSetColumn="AddressLine1" />
              <Mapping SourceColumn="AddressLine2" DataSetColumn="AddressLine2" />
              <Mapping SourceColumn="City" DataSetColumn="City" />
              <Mapping SourceColumn="StateRegion" DataSetColumn="StateRegion" />
              <Mapping SourceColumn="PostalCode" DataSetColumn="PostalCode" />
              <Mapping SourceColumn="Country" DataSetColumn="Country" />
              <Mapping SourceColumn="Phone" DataSetColumn="Phone" />
              <Mapping SourceColumn="Email" DataSetColumn="Email" />
              <Mapping SourceColumn="Website" DataSetColumn="Website" />
              <Mapping SourceColumn="Rating" DataSetColumn="Rating" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="VehicleTableAdapter" GeneratorDataComponentClassName="VehicleTableAdapter" Name="Vehicle" UserDataComponentName="VehicleTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString1 (Settings)" DbObjectName="Tourun.dbo.Vehicle" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Vehicle] WHERE (([VehicleId] = @Original_VehicleId) AND ([Type] = @Original_Type) AND ([Make] = @Original_Make) AND ([Model] = @Original_Model) AND ([PlateNumber] = @Original_PlateNumber) AND ((@IsNull_Capacity = 1 AND [Capacity] IS NULL) OR ([Capacity] = @Original_Capacity)) AND ([ProviderCompany] = @Original_ProviderCompany) AND ([ContactPhone] = @Original_ContactPhone) AND ([CreatedAt] = @Original_CreatedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_VehicleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Type" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Make" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Make" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Model" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Model" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_PlateNumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PlateNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Capacity" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Capacity" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ProviderCompany" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ProviderCompany" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ContactPhone" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ContactPhone" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Vehicle] ([Type], [Make], [Model], [PlateNumber], [Capacity], [ProviderCompany], [ContactPhone], [CreatedAt]) VALUES (@Type, @Make, @Model, @PlateNumber, @Capacity, @ProviderCompany, @ContactPhone, @CreatedAt);
SELECT VehicleId, Type, Make, Model, PlateNumber, Capacity, ProviderCompany, ContactPhone, CreatedAt FROM Vehicle WHERE (VehicleId = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Type" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Make" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Make" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Model" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Model" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@PlateNumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PlateNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Capacity" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ProviderCompany" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ProviderCompany" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ContactPhone" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ContactPhone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT VehicleId, Type, Make, Model, PlateNumber, Capacity, ProviderCompany, ContactPhone, CreatedAt
FROM     Vehicle</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Vehicle] SET [Type] = @Type, [Make] = @Make, [Model] = @Model, [PlateNumber] = @PlateNumber, [Capacity] = @Capacity, [ProviderCompany] = @ProviderCompany, [ContactPhone] = @ContactPhone, [CreatedAt] = @CreatedAt WHERE (([VehicleId] = @Original_VehicleId) AND ([Type] = @Original_Type) AND ([Make] = @Original_Make) AND ([Model] = @Original_Model) AND ([PlateNumber] = @Original_PlateNumber) AND ((@IsNull_Capacity = 1 AND [Capacity] IS NULL) OR ([Capacity] = @Original_Capacity)) AND ([ProviderCompany] = @Original_ProviderCompany) AND ([ContactPhone] = @Original_ContactPhone) AND ([CreatedAt] = @Original_CreatedAt));
SELECT VehicleId, Type, Make, Model, PlateNumber, Capacity, ProviderCompany, ContactPhone, CreatedAt FROM Vehicle WHERE (VehicleId = @VehicleId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Type" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Make" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Make" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Model" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Model" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@PlateNumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PlateNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Capacity" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ProviderCompany" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ProviderCompany" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ContactPhone" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ContactPhone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_VehicleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Type" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Make" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Make" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Model" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Model" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_PlateNumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PlateNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Capacity" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Capacity" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ProviderCompany" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ProviderCompany" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ContactPhone" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ContactPhone" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="VehicleId" ColumnName="VehicleId" DataSourceName="Tourun.dbo.Vehicle" DataTypeServer="bigint" DbType="Int64" Direction="Input" ParameterName="@VehicleId" Precision="0" ProviderType="BigInt" Scale="0" Size="8" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="VehicleId" DataSetColumn="VehicleId" />
              <Mapping SourceColumn="Type" DataSetColumn="Type" />
              <Mapping SourceColumn="Make" DataSetColumn="Make" />
              <Mapping SourceColumn="Model" DataSetColumn="Model" />
              <Mapping SourceColumn="PlateNumber" DataSetColumn="PlateNumber" />
              <Mapping SourceColumn="Capacity" DataSetColumn="Capacity" />
              <Mapping SourceColumn="ProviderCompany" DataSetColumn="ProviderCompany" />
              <Mapping SourceColumn="ContactPhone" DataSetColumn="ContactPhone" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="AttractionTableAdapter" GeneratorDataComponentClassName="AttractionTableAdapter" Name="Attraction" UserDataComponentName="AttractionTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString1 (Settings)" DbObjectName="Tourun.dbo.Attraction" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Attraction] WHERE (([AttractionId] = @Original_AttractionId) AND ([Name] = @Original_Name) AND ([Type] = @Original_Type) AND ([Description] = @Original_Description) AND ([AddressLine1] = @Original_AddressLine1) AND ([AddressLine2] = @Original_AddressLine2) AND ([City] = @Original_City) AND ([StateRegion] = @Original_StateRegion) AND ([PostalCode] = @Original_PostalCode) AND ([Country] = @Original_Country) AND ([Latitude] = @Original_Latitude) AND ([Longitude] = @Original_Longitude) AND ([Website] = @Original_Website) AND ([CreatedAt] = @Original_CreatedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_AttractionId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Type" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Description" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_AddressLine1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_AddressLine2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_City" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_StateRegion" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_PostalCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Country" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Latitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Latitude" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Longitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Longitude" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Website" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Attraction] ([Name], [Type], [Description], [AddressLine1], [AddressLine2], [City], [StateRegion], [PostalCode], [Country], [Latitude], [Longitude], [Website], [CreatedAt]) VALUES (@Name, @Type, @Description, @AddressLine1, @AddressLine2, @City, @StateRegion, @PostalCode, @Country, @Latitude, @Longitude, @Website, @CreatedAt);
SELECT AttractionId, Name, Type, Description, AddressLine1, AddressLine2, City, StateRegion, PostalCode, Country, Latitude, Longitude, Website, CreatedAt FROM Attraction WHERE (AttractionId = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Type" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Description" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@AddressLine1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@AddressLine2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@City" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@StateRegion" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@PostalCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Country" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Latitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Latitude" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Longitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Longitude" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Website" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT AttractionId, Name, Type, Description, AddressLine1, AddressLine2, City, StateRegion, PostalCode, Country, Latitude, Longitude, Website, CreatedAt
FROM     Attraction</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Attraction] SET [Name] = @Name, [Type] = @Type, [Description] = @Description, [AddressLine1] = @AddressLine1, [AddressLine2] = @AddressLine2, [City] = @City, [StateRegion] = @StateRegion, [PostalCode] = @PostalCode, [Country] = @Country, [Latitude] = @Latitude, [Longitude] = @Longitude, [Website] = @Website, [CreatedAt] = @CreatedAt WHERE (([AttractionId] = @Original_AttractionId) AND ([Name] = @Original_Name) AND ([Type] = @Original_Type) AND ([Description] = @Original_Description) AND ([AddressLine1] = @Original_AddressLine1) AND ([AddressLine2] = @Original_AddressLine2) AND ([City] = @Original_City) AND ([StateRegion] = @Original_StateRegion) AND ([PostalCode] = @Original_PostalCode) AND ([Country] = @Original_Country) AND ([Latitude] = @Original_Latitude) AND ([Longitude] = @Original_Longitude) AND ([Website] = @Original_Website) AND ([CreatedAt] = @Original_CreatedAt));
SELECT AttractionId, Name, Type, Description, AddressLine1, AddressLine2, City, StateRegion, PostalCode, Country, Latitude, Longitude, Website, CreatedAt FROM Attraction WHERE (AttractionId = @AttractionId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Type" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Description" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@AddressLine1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@AddressLine2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@City" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@StateRegion" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@PostalCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Country" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Latitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Latitude" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Longitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Longitude" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Website" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_AttractionId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Type" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Description" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_AddressLine1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_AddressLine2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_City" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_StateRegion" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_PostalCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Country" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Latitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Latitude" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Longitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Longitude" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Website" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AttractionId" ColumnName="AttractionId" DataSourceName="Tourun.dbo.Attraction" DataTypeServer="bigint" DbType="Int64" Direction="Input" ParameterName="@AttractionId" Precision="0" ProviderType="BigInt" Scale="0" Size="8" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="AttractionId" DataSetColumn="AttractionId" />
              <Mapping SourceColumn="Name" DataSetColumn="Name" />
              <Mapping SourceColumn="Type" DataSetColumn="Type" />
              <Mapping SourceColumn="Description" DataSetColumn="Description" />
              <Mapping SourceColumn="AddressLine1" DataSetColumn="AddressLine1" />
              <Mapping SourceColumn="AddressLine2" DataSetColumn="AddressLine2" />
              <Mapping SourceColumn="City" DataSetColumn="City" />
              <Mapping SourceColumn="StateRegion" DataSetColumn="StateRegion" />
              <Mapping SourceColumn="PostalCode" DataSetColumn="PostalCode" />
              <Mapping SourceColumn="Country" DataSetColumn="Country" />
              <Mapping SourceColumn="Latitude" DataSetColumn="Latitude" />
              <Mapping SourceColumn="Longitude" DataSetColumn="Longitude" />
              <Mapping SourceColumn="Website" DataSetColumn="Website" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CoordinatorTableAdapter" GeneratorDataComponentClassName="CoordinatorTableAdapter" Name="Coordinator" UserDataComponentName="CoordinatorTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString1 (Settings)" DbObjectName="Tourun.dbo.Coordinator" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Coordinator] WHERE (([CoordinatorId] = @Original_CoordinatorId) AND ([UserId] = @Original_UserId) AND ((@IsNull_HireDate = 1 AND [HireDate] IS NULL) OR ([HireDate] = @Original_HireDate)) AND ([Notes] = @Original_Notes) AND ([CreatedAt] = @Original_CreatedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_CoordinatorId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_UserId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_HireDate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_HireDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Coordinator] ([UserId], [HireDate], [Notes], [CreatedAt]) VALUES (@UserId, @HireDate, @Notes, @CreatedAt);
SELECT CoordinatorId, UserId, HireDate, Notes, CreatedAt FROM Coordinator WHERE (CoordinatorId = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@UserId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@HireDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT CoordinatorId, UserId, HireDate, Notes, CreatedAt
FROM     Coordinator</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Coordinator] SET [UserId] = @UserId, [HireDate] = @HireDate, [Notes] = @Notes, [CreatedAt] = @CreatedAt WHERE (([CoordinatorId] = @Original_CoordinatorId) AND ([UserId] = @Original_UserId) AND ((@IsNull_HireDate = 1 AND [HireDate] IS NULL) OR ([HireDate] = @Original_HireDate)) AND ([Notes] = @Original_Notes) AND ([CreatedAt] = @Original_CreatedAt));
SELECT CoordinatorId, UserId, HireDate, Notes, CreatedAt FROM Coordinator WHERE (CoordinatorId = @CoordinatorId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@UserId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@HireDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_CoordinatorId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_UserId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_HireDate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_HireDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CoordinatorId" ColumnName="CoordinatorId" DataSourceName="Tourun.dbo.Coordinator" DataTypeServer="bigint" DbType="Int64" Direction="Input" ParameterName="@CoordinatorId" Precision="0" ProviderType="BigInt" Scale="0" Size="8" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="CoordinatorId" DataSetColumn="CoordinatorId" />
              <Mapping SourceColumn="UserId" DataSetColumn="UserId" />
              <Mapping SourceColumn="HireDate" DataSetColumn="HireDate" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="RoleTableAdapter" GeneratorDataComponentClassName="RoleTableAdapter" Name="Role" UserDataComponentName="RoleTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString1 (Settings)" DbObjectName="Tourun.dbo.Role" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Role] WHERE (([RoleId] = @Original_RoleId) AND ([Name] = @Original_Name) AND ([CreatedAt] = @Original_CreatedAt) AND ([Notes] = @Original_Notes))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_RoleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Role] ([Name], [CreatedAt], [Notes]) VALUES (@Name, @CreatedAt, @Notes);
SELECT RoleId, Name, CreatedAt, Notes FROM Role WHERE (RoleId = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT RoleId, Name, CreatedAt, Notes
FROM     Role</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Role] SET [Name] = @Name, [CreatedAt] = @CreatedAt, [Notes] = @Notes WHERE (([RoleId] = @Original_RoleId) AND ([Name] = @Original_Name) AND ([CreatedAt] = @Original_CreatedAt) AND ([Notes] = @Original_Notes));
SELECT RoleId, Name, CreatedAt, Notes FROM Role WHERE (RoleId = @RoleId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_RoleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="RoleId" ColumnName="RoleId" DataSourceName="Tourun.dbo.Role" DataTypeServer="bigint" DbType="Int64" Direction="Input" ParameterName="@RoleId" Precision="0" ProviderType="BigInt" Scale="0" Size="8" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="RoleId" DataSetColumn="RoleId" />
              <Mapping SourceColumn="Name" DataSetColumn="Name" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="TourunConnectionString1 (Settings)" DbObjectName="Tourun.dbo.Role" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillByRoleId" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetDataByRoleId" GeneratorSourceName="FillByRoleId" GetMethodModifier="Public" GetMethodName="GetDataByRoleId" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataByRoleId" UserSourceName="FillByRoleId">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT CreatedAt, Name, Notes, RoleId FROM Role WHERE (RoleId = @RoleId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="RoleId" ColumnName="RoleId" DataSourceName="Tourun.dbo.Role" DataTypeServer="bigint" DbType="Int64" Direction="Input" ParameterName="@RoleId" Precision="0" ProviderType="BigInt" Scale="0" Size="8" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="TourTableAdapter" GeneratorDataComponentClassName="TourTableAdapter" Name="Tour" UserDataComponentName="TourTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString1 (Settings)" DbObjectName="Tourun.dbo.Tour" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Tour] WHERE (([TourId] = @Original_TourId) AND ((@IsNull_CoordinatorId = 1 AND [CoordinatorId] IS NULL) OR ([CoordinatorId] = @Original_CoordinatorId)) AND ((@IsNull_PriceAmount = 1 AND [PriceAmount] IS NULL) OR ([PriceAmount] = @Original_PriceAmount)) AND ([Status] = @Original_Status) AND ([CreatedAt] = @Original_CreatedAt) AND ([UpdatedAt] = @Original_UpdatedAt) AND ((@IsNull_PaxBooked = 1 AND [PaxBooked] IS NULL) OR ([PaxBooked] = @Original_PaxBooked)) AND ([TourCode] = @Original_TourCode) AND ([ArrivalDate] = @Original_ArrivalDate) AND ([DepartureDate] = @Original_DepartureDate) AND ((@IsNull_Allotment = 1 AND [Allotment] IS NULL) OR ([Allotment] = @Original_Allotment)) AND ([Notes] = @Original_Notes))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_CoordinatorId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_CoordinatorId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_PriceAmount" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PriceAmount" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_PriceAmount" Precision="12" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PriceAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Status" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_PaxBooked" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PaxBooked" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_PaxBooked" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PaxBooked" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_TourCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="TourCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_ArrivalDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="ArrivalDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_DepartureDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="DepartureDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Allotment" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Allotment" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Allotment" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Allotment" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Tour] ([CoordinatorId], [PriceAmount], [Status], [CreatedAt], [UpdatedAt], [PaxBooked], [TourCode], [ArrivalDate], [DepartureDate], [Allotment], [Notes]) VALUES (@CoordinatorId, @PriceAmount, @Status, @CreatedAt, @UpdatedAt, @PaxBooked, @TourCode, @ArrivalDate, @DepartureDate, @Allotment, @Notes);
SELECT TourId, CoordinatorId, PriceAmount, Status, CreatedAt, UpdatedAt, PaxBooked, TourCode, ArrivalDate, DepartureDate, Allotment, Notes FROM Tour WHERE (TourId = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@CoordinatorId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@PriceAmount" Precision="12" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PriceAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@PaxBooked" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PaxBooked" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@TourCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="TourCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@ArrivalDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="ArrivalDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@DepartureDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="DepartureDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Allotment" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Allotment" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT TourId, CoordinatorId, PriceAmount, Status, CreatedAt, UpdatedAt, PaxBooked, TourCode, ArrivalDate, DepartureDate, Allotment, Notes
FROM     Tour</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Tour] SET [CoordinatorId] = @CoordinatorId, [PriceAmount] = @PriceAmount, [Status] = @Status, [CreatedAt] = @CreatedAt, [UpdatedAt] = @UpdatedAt, [PaxBooked] = @PaxBooked, [TourCode] = @TourCode, [ArrivalDate] = @ArrivalDate, [DepartureDate] = @DepartureDate, [Allotment] = @Allotment, [Notes] = @Notes WHERE (([TourId] = @Original_TourId) AND ((@IsNull_CoordinatorId = 1 AND [CoordinatorId] IS NULL) OR ([CoordinatorId] = @Original_CoordinatorId)) AND ((@IsNull_PriceAmount = 1 AND [PriceAmount] IS NULL) OR ([PriceAmount] = @Original_PriceAmount)) AND ([Status] = @Original_Status) AND ([CreatedAt] = @Original_CreatedAt) AND ([UpdatedAt] = @Original_UpdatedAt) AND ((@IsNull_PaxBooked = 1 AND [PaxBooked] IS NULL) OR ([PaxBooked] = @Original_PaxBooked)) AND ([TourCode] = @Original_TourCode) AND ([ArrivalDate] = @Original_ArrivalDate) AND ([DepartureDate] = @Original_DepartureDate) AND ((@IsNull_Allotment = 1 AND [Allotment] IS NULL) OR ([Allotment] = @Original_Allotment)) AND ([Notes] = @Original_Notes));
SELECT TourId, CoordinatorId, PriceAmount, Status, CreatedAt, UpdatedAt, PaxBooked, TourCode, ArrivalDate, DepartureDate, Allotment, Notes FROM Tour WHERE (TourId = @TourId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@CoordinatorId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@PriceAmount" Precision="12" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PriceAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@PaxBooked" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PaxBooked" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@TourCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="TourCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@ArrivalDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="ArrivalDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@DepartureDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="DepartureDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Allotment" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Allotment" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_CoordinatorId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_CoordinatorId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_PriceAmount" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PriceAmount" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_PriceAmount" Precision="12" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PriceAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Status" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_PaxBooked" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PaxBooked" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_PaxBooked" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PaxBooked" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_TourCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="TourCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_ArrivalDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="ArrivalDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_DepartureDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="DepartureDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Allotment" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Allotment" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Allotment" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Allotment" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="TourId" ColumnName="TourId" DataSourceName="Tourun.dbo.Tour" DataTypeServer="bigint" DbType="Int64" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="8" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="TourId" DataSetColumn="TourId" />
              <Mapping SourceColumn="CoordinatorId" DataSetColumn="CoordinatorId" />
              <Mapping SourceColumn="PriceAmount" DataSetColumn="PriceAmount" />
              <Mapping SourceColumn="Status" DataSetColumn="Status" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
              <Mapping SourceColumn="UpdatedAt" DataSetColumn="UpdatedAt" />
              <Mapping SourceColumn="PaxBooked" DataSetColumn="PaxBooked" />
              <Mapping SourceColumn="TourCode" DataSetColumn="TourCode" />
              <Mapping SourceColumn="ArrivalDate" DataSetColumn="ArrivalDate" />
              <Mapping SourceColumn="DepartureDate" DataSetColumn="DepartureDate" />
              <Mapping SourceColumn="Allotment" DataSetColumn="Allotment" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="TourAccommodationTableAdapter" GeneratorDataComponentClassName="TourAccommodationTableAdapter" Name="TourAccommodation" UserDataComponentName="TourAccommodationTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString1 (Settings)" DbObjectName="Tourun.dbo.TourAccommodation" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [TourAccommodation] WHERE (([TourAccommodationId] = @Original_TourAccommodationId) AND ([TourId] = @Original_TourId) AND ([AccommodationId] = @Original_AccommodationId) AND ([CheckIn] = @Original_CheckIn) AND ([CheckOut] = @Original_CheckOut) AND ([Notes] = @Original_Notes))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourAccommodationId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourAccommodationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_AccommodationId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_CheckIn" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckIn" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_CheckOut" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckOut" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [TourAccommodation] ([TourId], [AccommodationId], [CheckIn], [CheckOut], [Notes]) VALUES (@TourId, @AccommodationId, @CheckIn, @CheckOut, @Notes);
SELECT TourAccommodationId, TourId, AccommodationId, CheckIn, CheckOut, Notes FROM TourAccommodation WHERE (TourAccommodationId = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@AccommodationId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@CheckIn" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckIn" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@CheckOut" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckOut" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT TourAccommodationId, TourId, AccommodationId, CheckIn, CheckOut, Notes
FROM     TourAccommodation</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [TourAccommodation] SET [TourId] = @TourId, [AccommodationId] = @AccommodationId, [CheckIn] = @CheckIn, [CheckOut] = @CheckOut, [Notes] = @Notes WHERE (([TourAccommodationId] = @Original_TourAccommodationId) AND ([TourId] = @Original_TourId) AND ([AccommodationId] = @Original_AccommodationId) AND ([CheckIn] = @Original_CheckIn) AND ([CheckOut] = @Original_CheckOut) AND ([Notes] = @Original_Notes));
SELECT TourAccommodationId, TourId, AccommodationId, CheckIn, CheckOut, Notes FROM TourAccommodation WHERE (TourAccommodationId = @TourAccommodationId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@AccommodationId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@CheckIn" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckIn" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@CheckOut" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckOut" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourAccommodationId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourAccommodationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_AccommodationId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_CheckIn" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckIn" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_CheckOut" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckOut" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="TourAccommodationId" ColumnName="TourAccommodationId" DataSourceName="Tourun.dbo.TourAccommodation" DataTypeServer="bigint" DbType="Int64" Direction="Input" ParameterName="@TourAccommodationId" Precision="0" ProviderType="BigInt" Scale="0" Size="8" SourceColumn="TourAccommodationId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="TourAccommodationId" DataSetColumn="TourAccommodationId" />
              <Mapping SourceColumn="TourId" DataSetColumn="TourId" />
              <Mapping SourceColumn="AccommodationId" DataSetColumn="AccommodationId" />
              <Mapping SourceColumn="CheckIn" DataSetColumn="CheckIn" />
              <Mapping SourceColumn="CheckOut" DataSetColumn="CheckOut" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="TourAttractionTableAdapter" GeneratorDataComponentClassName="TourAttractionTableAdapter" Name="TourAttraction" UserDataComponentName="TourAttractionTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString1 (Settings)" DbObjectName="Tourun.dbo.TourAttraction" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [TourAttraction] WHERE (([TourAttractionId] = @Original_TourAttractionId) AND ([TourId] = @Original_TourId) AND ([AttractionId] = @Original_AttractionId) AND ((@IsNull_VisitDateTime = 1 AND [VisitDateTime] IS NULL) OR ([VisitDateTime] = @Original_VisitDateTime)) AND ((@IsNull_SequenceNumber = 1 AND [SequenceNumber] IS NULL) OR ([SequenceNumber] = @Original_SequenceNumber)) AND ([Notes] = @Original_Notes))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourAttractionId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourAttractionId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_AttractionId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_VisitDateTime" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="VisitDateTime" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_VisitDateTime" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="VisitDateTime" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_SequenceNumber" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SequenceNumber" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_SequenceNumber" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="SequenceNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [TourAttraction] ([TourId], [AttractionId], [VisitDateTime], [SequenceNumber], [Notes]) VALUES (@TourId, @AttractionId, @VisitDateTime, @SequenceNumber, @Notes);
SELECT TourAttractionId, TourId, AttractionId, VisitDateTime, SequenceNumber, Notes FROM TourAttraction WHERE (TourAttractionId = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@AttractionId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@VisitDateTime" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="VisitDateTime" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@SequenceNumber" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="SequenceNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT TourAttractionId, TourId, AttractionId, VisitDateTime, SequenceNumber, Notes
FROM     TourAttraction</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [TourAttraction] SET [TourId] = @TourId, [AttractionId] = @AttractionId, [VisitDateTime] = @VisitDateTime, [SequenceNumber] = @SequenceNumber, [Notes] = @Notes WHERE (([TourAttractionId] = @Original_TourAttractionId) AND ([TourId] = @Original_TourId) AND ([AttractionId] = @Original_AttractionId) AND ((@IsNull_VisitDateTime = 1 AND [VisitDateTime] IS NULL) OR ([VisitDateTime] = @Original_VisitDateTime)) AND ((@IsNull_SequenceNumber = 1 AND [SequenceNumber] IS NULL) OR ([SequenceNumber] = @Original_SequenceNumber)) AND ([Notes] = @Original_Notes));
SELECT TourAttractionId, TourId, AttractionId, VisitDateTime, SequenceNumber, Notes FROM TourAttraction WHERE (TourAttractionId = @TourAttractionId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@AttractionId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@VisitDateTime" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="VisitDateTime" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@SequenceNumber" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="SequenceNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourAttractionId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourAttractionId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_AttractionId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_VisitDateTime" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="VisitDateTime" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_VisitDateTime" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="VisitDateTime" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_SequenceNumber" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SequenceNumber" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_SequenceNumber" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="SequenceNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="TourAttractionId" ColumnName="TourAttractionId" DataSourceName="Tourun.dbo.TourAttraction" DataTypeServer="bigint" DbType="Int64" Direction="Input" ParameterName="@TourAttractionId" Precision="0" ProviderType="BigInt" Scale="0" Size="8" SourceColumn="TourAttractionId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="TourAttractionId" DataSetColumn="TourAttractionId" />
              <Mapping SourceColumn="TourId" DataSetColumn="TourId" />
              <Mapping SourceColumn="AttractionId" DataSetColumn="AttractionId" />
              <Mapping SourceColumn="VisitDateTime" DataSetColumn="VisitDateTime" />
              <Mapping SourceColumn="SequenceNumber" DataSetColumn="SequenceNumber" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="TourVehicleTableAdapter" GeneratorDataComponentClassName="TourVehicleTableAdapter" Name="TourVehicle" UserDataComponentName="TourVehicleTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString1 (Settings)" DbObjectName="Tourun.dbo.TourVehicle" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [TourVehicle] WHERE (([TourVehicleId] = @Original_TourVehicleId) AND ([TourId] = @Original_TourId) AND ([VehicleId] = @Original_VehicleId) AND ((@IsNull_AssignedFrom = 1 AND [AssignedFrom] IS NULL) OR ([AssignedFrom] = @Original_AssignedFrom)) AND ((@IsNull_AssignedTo = 1 AND [AssignedTo] IS NULL) OR ([AssignedTo] = @Original_AssignedTo)) AND ([DriverName] = @Original_DriverName) AND ([Notes] = @Original_Notes))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourVehicleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourVehicleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_VehicleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AssignedFrom" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AssignedFrom" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_AssignedFrom" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedFrom" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AssignedTo" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AssignedTo" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_AssignedTo" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedTo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_DriverName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="DriverName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [TourVehicle] ([TourId], [VehicleId], [AssignedFrom], [AssignedTo], [DriverName], [Notes]) VALUES (@TourId, @VehicleId, @AssignedFrom, @AssignedTo, @DriverName, @Notes);
SELECT TourVehicleId, TourId, VehicleId, AssignedFrom, AssignedTo, DriverName, Notes FROM TourVehicle WHERE (TourVehicleId = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@VehicleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@AssignedFrom" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedFrom" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@AssignedTo" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedTo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@DriverName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="DriverName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT TourVehicleId, TourId, VehicleId, AssignedFrom, AssignedTo, DriverName, Notes
FROM     TourVehicle</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [TourVehicle] SET [TourId] = @TourId, [VehicleId] = @VehicleId, [AssignedFrom] = @AssignedFrom, [AssignedTo] = @AssignedTo, [DriverName] = @DriverName, [Notes] = @Notes WHERE (([TourVehicleId] = @Original_TourVehicleId) AND ([TourId] = @Original_TourId) AND ([VehicleId] = @Original_VehicleId) AND ((@IsNull_AssignedFrom = 1 AND [AssignedFrom] IS NULL) OR ([AssignedFrom] = @Original_AssignedFrom)) AND ((@IsNull_AssignedTo = 1 AND [AssignedTo] IS NULL) OR ([AssignedTo] = @Original_AssignedTo)) AND ([DriverName] = @Original_DriverName) AND ([Notes] = @Original_Notes));
SELECT TourVehicleId, TourId, VehicleId, AssignedFrom, AssignedTo, DriverName, Notes FROM TourVehicle WHERE (TourVehicleId = @TourVehicleId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@VehicleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@AssignedFrom" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedFrom" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@AssignedTo" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedTo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@DriverName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="DriverName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourVehicleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourVehicleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_VehicleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AssignedFrom" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AssignedFrom" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_AssignedFrom" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedFrom" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AssignedTo" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AssignedTo" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_AssignedTo" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedTo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_DriverName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="DriverName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="TourVehicleId" ColumnName="TourVehicleId" DataSourceName="Tourun.dbo.TourVehicle" DataTypeServer="bigint" DbType="Int64" Direction="Input" ParameterName="@TourVehicleId" Precision="0" ProviderType="BigInt" Scale="0" Size="8" SourceColumn="TourVehicleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="TourVehicleId" DataSetColumn="TourVehicleId" />
              <Mapping SourceColumn="TourId" DataSetColumn="TourId" />
              <Mapping SourceColumn="VehicleId" DataSetColumn="VehicleId" />
              <Mapping SourceColumn="AssignedFrom" DataSetColumn="AssignedFrom" />
              <Mapping SourceColumn="AssignedTo" DataSetColumn="AssignedTo" />
              <Mapping SourceColumn="DriverName" DataSetColumn="DriverName" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="UserTableAdapter" GeneratorDataComponentClassName="UserTableAdapter" Name="User" UserDataComponentName="UserTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString1 (Settings)" DbObjectName="Tourun.dbo.[User]" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [User] WHERE (([UserId] = @Original_UserId) AND ([RoleId] = @Original_RoleId) AND ([Username] = @Original_Username) AND ([Email] = @Original_Email) AND ([Password] = @Original_Password) AND ([FirstName] = @Original_FirstName) AND ([LastName] = @Original_LastName) AND ((@IsNull_Phone = 1 AND [Phone] IS NULL) OR ([Phone] = @Original_Phone)) AND ([IsActive] = @Original_IsActive) AND ([CreatedAt] = @Original_CreatedAt) AND ([UpdatedAt] = @Original_UpdatedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_UserId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_RoleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Username" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Username" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Email" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Password" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Password" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_FirstName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_LastName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Phone" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Phone" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_IsActive" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsActive" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [User] ([RoleId], [Username], [Email], [Password], [FirstName], [LastName], [Phone], [IsActive], [CreatedAt], [UpdatedAt]) VALUES (@RoleId, @Username, @Email, @Password, @FirstName, @LastName, @Phone, @IsActive, @CreatedAt, @UpdatedAt);
SELECT UserId, RoleId, Username, Email, Password, FirstName, LastName, Phone, IsActive, CreatedAt, UpdatedAt FROM [User] WHERE (UserId = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@RoleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Username" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Username" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Password" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Password" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@FirstName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@LastName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Phone" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@IsActive" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsActive" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT UserId, RoleId, Username, Email, Password, FirstName, LastName, Phone, IsActive, CreatedAt, UpdatedAt
FROM     [User]</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [User] SET [RoleId] = @RoleId, [Username] = @Username, [Email] = @Email, [Password] = @Password, [FirstName] = @FirstName, [LastName] = @LastName, [Phone] = @Phone, [IsActive] = @IsActive, [CreatedAt] = @CreatedAt, [UpdatedAt] = @UpdatedAt WHERE (([UserId] = @Original_UserId) AND ([RoleId] = @Original_RoleId) AND ([Username] = @Original_Username) AND ([Email] = @Original_Email) AND ([Password] = @Original_Password) AND ([FirstName] = @Original_FirstName) AND ([LastName] = @Original_LastName) AND ((@IsNull_Phone = 1 AND [Phone] IS NULL) OR ([Phone] = @Original_Phone)) AND ([IsActive] = @Original_IsActive) AND ([CreatedAt] = @Original_CreatedAt) AND ([UpdatedAt] = @Original_UpdatedAt));
SELECT UserId, RoleId, Username, Email, Password, FirstName, LastName, Phone, IsActive, CreatedAt, UpdatedAt FROM [User] WHERE (UserId = @UserId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@RoleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Username" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Username" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Password" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Password" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@FirstName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@LastName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Phone" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@IsActive" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsActive" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_UserId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int64" Direction="Input" ParameterName="@Original_RoleId" Precision="0" ProviderType="BigInt" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Username" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Username" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Email" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Password" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Password" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_FirstName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_LastName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Phone" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Phone" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_IsActive" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsActive" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="UserId" ColumnName="UserId" DataSourceName="Tourun.dbo.[User]" DataTypeServer="bigint" DbType="Int64" Direction="Input" ParameterName="@UserId" Precision="0" ProviderType="BigInt" Scale="0" Size="8" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="UserId" DataSetColumn="UserId" />
              <Mapping SourceColumn="RoleId" DataSetColumn="RoleId" />
              <Mapping SourceColumn="Username" DataSetColumn="Username" />
              <Mapping SourceColumn="Email" DataSetColumn="Email" />
              <Mapping SourceColumn="Password" DataSetColumn="Password" />
              <Mapping SourceColumn="FirstName" DataSetColumn="FirstName" />
              <Mapping SourceColumn="LastName" DataSetColumn="LastName" />
              <Mapping SourceColumn="Phone" DataSetColumn="Phone" />
              <Mapping SourceColumn="IsActive" DataSetColumn="IsActive" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
              <Mapping SourceColumn="UpdatedAt" DataSetColumn="UpdatedAt" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="TourunConnectionString1 (Settings)" DbObjectName="Tourun.dbo.[User]" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillByUsername" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetDataByUsername" GeneratorSourceName="FillByUsername" GetMethodModifier="Public" GetMethodName="GetDataByUsername" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataByUsername" UserSourceName="FillByUsername">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT CreatedAt, Email, FirstName, IsActive, LastName, Password, Phone, RoleId, UpdatedAt, UserId, Username FROM [User] WHERE (Username = @Username)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="Username" ColumnName="Username" DataSourceName="Tourun.dbo.[User]" DataTypeServer="varchar(50)" DbType="AnsiString" Direction="Input" ParameterName="@Username" Precision="0" ProviderType="VarChar" Scale="0" Size="50" SourceColumn="Username" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="TourunDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="TourunDataSet" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="TourunDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Accommodation" msprop:Generator_RowEvHandlerName="AccommodationRowChangeEventHandler" msprop:Generator_RowDeletedName="AccommodationRowDeleted" msprop:Generator_RowDeletingName="AccommodationRowDeleting" msprop:Generator_RowEvArgName="AccommodationRowChangeEvent" msprop:Generator_TablePropName="Accommodation" msprop:Generator_RowChangedName="AccommodationRowChanged" msprop:Generator_UserTableName="Accommodation" msprop:Generator_RowChangingName="AccommodationRowChanging" msprop:Generator_RowClassName="AccommodationRow" msprop:Generator_TableClassName="AccommodationDataTable" msprop:Generator_TableVarName="tableAccommodation">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AccommodationId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInTable="AccommodationIdColumn" msprop:Generator_ColumnPropNameInRow="AccommodationId" msprop:Generator_UserColumnName="AccommodationId" msprop:Generator_ColumnVarNameInTable="columnAccommodationId" type="xs:long" />
              <xs:element name="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_UserColumnName="Name" msprop:Generator_ColumnVarNameInTable="columnName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Type" msprop:Generator_ColumnPropNameInTable="TypeColumn" msprop:Generator_ColumnPropNameInRow="Type" msprop:Generator_UserColumnName="Type" msprop:Generator_ColumnVarNameInTable="columnType">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AddressLine1" msprop:Generator_ColumnPropNameInTable="AddressLine1Column" msprop:Generator_ColumnPropNameInRow="AddressLine1" msprop:Generator_UserColumnName="AddressLine1" msprop:Generator_ColumnVarNameInTable="columnAddressLine1">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AddressLine2" msprop:Generator_ColumnPropNameInTable="AddressLine2Column" msprop:Generator_ColumnPropNameInRow="AddressLine2" msprop:Generator_UserColumnName="AddressLine2" msprop:Generator_ColumnVarNameInTable="columnAddressLine2">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="City" msprop:Generator_ColumnPropNameInTable="CityColumn" msprop:Generator_ColumnPropNameInRow="City" msprop:Generator_UserColumnName="City" msprop:Generator_ColumnVarNameInTable="columnCity">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="StateRegion" msprop:Generator_ColumnPropNameInTable="StateRegionColumn" msprop:Generator_ColumnPropNameInRow="StateRegion" msprop:Generator_UserColumnName="StateRegion" msprop:Generator_ColumnVarNameInTable="columnStateRegion">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PostalCode" msprop:Generator_ColumnPropNameInTable="PostalCodeColumn" msprop:Generator_ColumnPropNameInRow="PostalCode" msprop:Generator_UserColumnName="PostalCode" msprop:Generator_ColumnVarNameInTable="columnPostalCode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Country" msprop:Generator_ColumnPropNameInTable="CountryColumn" msprop:Generator_ColumnPropNameInRow="Country" msprop:Generator_UserColumnName="Country" msprop:Generator_ColumnVarNameInTable="columnCountry">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Phone" msprop:Generator_ColumnPropNameInTable="PhoneColumn" msprop:Generator_ColumnPropNameInRow="Phone" msprop:Generator_UserColumnName="Phone" msprop:Generator_ColumnVarNameInTable="columnPhone">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Email" msprop:Generator_ColumnPropNameInTable="EmailColumn" msprop:Generator_ColumnPropNameInRow="Email" msprop:Generator_UserColumnName="Email" msprop:Generator_ColumnVarNameInTable="columnEmail">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Website" msprop:Generator_ColumnPropNameInTable="WebsiteColumn" msprop:Generator_ColumnPropNameInRow="Website" msprop:Generator_UserColumnName="Website" msprop:Generator_ColumnVarNameInTable="columnWebsite">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Rating" msprop:Generator_ColumnPropNameInTable="RatingColumn" msprop:Generator_ColumnPropNameInRow="Rating" msprop:Generator_UserColumnName="Rating" msprop:Generator_ColumnVarNameInTable="columnRating" type="xs:decimal" minOccurs="0" />
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_UserColumnName="CreatedAt" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Vehicle" msprop:Generator_RowEvHandlerName="VehicleRowChangeEventHandler" msprop:Generator_RowDeletedName="VehicleRowDeleted" msprop:Generator_RowDeletingName="VehicleRowDeleting" msprop:Generator_RowEvArgName="VehicleRowChangeEvent" msprop:Generator_TablePropName="Vehicle" msprop:Generator_RowChangedName="VehicleRowChanged" msprop:Generator_UserTableName="Vehicle" msprop:Generator_RowChangingName="VehicleRowChanging" msprop:Generator_RowClassName="VehicleRow" msprop:Generator_TableClassName="VehicleDataTable" msprop:Generator_TableVarName="tableVehicle">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="VehicleId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInTable="VehicleIdColumn" msprop:Generator_ColumnPropNameInRow="VehicleId" msprop:Generator_UserColumnName="VehicleId" msprop:Generator_ColumnVarNameInTable="columnVehicleId" type="xs:long" />
              <xs:element name="Type" msprop:Generator_ColumnPropNameInTable="TypeColumn" msprop:Generator_ColumnPropNameInRow="Type" msprop:Generator_UserColumnName="Type" msprop:Generator_ColumnVarNameInTable="columnType">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Make" msprop:Generator_ColumnPropNameInTable="MakeColumn" msprop:Generator_ColumnPropNameInRow="Make" msprop:Generator_UserColumnName="Make" msprop:Generator_ColumnVarNameInTable="columnMake">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Model" msprop:Generator_ColumnPropNameInTable="ModelColumn" msprop:Generator_ColumnPropNameInRow="Model" msprop:Generator_UserColumnName="Model" msprop:Generator_ColumnVarNameInTable="columnModel">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PlateNumber" msprop:Generator_ColumnPropNameInTable="PlateNumberColumn" msprop:Generator_ColumnPropNameInRow="PlateNumber" msprop:Generator_UserColumnName="PlateNumber" msprop:Generator_ColumnVarNameInTable="columnPlateNumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Capacity" msprop:Generator_ColumnPropNameInTable="CapacityColumn" msprop:Generator_ColumnPropNameInRow="Capacity" msprop:Generator_UserColumnName="Capacity" msprop:Generator_ColumnVarNameInTable="columnCapacity" type="xs:int" minOccurs="0" />
              <xs:element name="ProviderCompany" msprop:Generator_ColumnPropNameInTable="ProviderCompanyColumn" msprop:Generator_ColumnPropNameInRow="ProviderCompany" msprop:Generator_UserColumnName="ProviderCompany" msprop:Generator_ColumnVarNameInTable="columnProviderCompany">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContactPhone" msprop:Generator_ColumnPropNameInTable="ContactPhoneColumn" msprop:Generator_ColumnPropNameInRow="ContactPhone" msprop:Generator_UserColumnName="ContactPhone" msprop:Generator_ColumnVarNameInTable="columnContactPhone">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_UserColumnName="CreatedAt" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Attraction" msprop:Generator_RowEvHandlerName="AttractionRowChangeEventHandler" msprop:Generator_RowDeletedName="AttractionRowDeleted" msprop:Generator_RowDeletingName="AttractionRowDeleting" msprop:Generator_RowEvArgName="AttractionRowChangeEvent" msprop:Generator_TablePropName="Attraction" msprop:Generator_RowChangedName="AttractionRowChanged" msprop:Generator_UserTableName="Attraction" msprop:Generator_RowChangingName="AttractionRowChanging" msprop:Generator_RowClassName="AttractionRow" msprop:Generator_TableClassName="AttractionDataTable" msprop:Generator_TableVarName="tableAttraction">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AttractionId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInTable="AttractionIdColumn" msprop:Generator_ColumnPropNameInRow="AttractionId" msprop:Generator_UserColumnName="AttractionId" msprop:Generator_ColumnVarNameInTable="columnAttractionId" type="xs:long" />
              <xs:element name="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_UserColumnName="Name" msprop:Generator_ColumnVarNameInTable="columnName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Type" msprop:Generator_ColumnPropNameInTable="TypeColumn" msprop:Generator_ColumnPropNameInRow="Type" msprop:Generator_UserColumnName="Type" msprop:Generator_ColumnVarNameInTable="columnType">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Description" msprop:Generator_ColumnPropNameInTable="DescriptionColumn" msprop:Generator_ColumnPropNameInRow="Description" msprop:Generator_UserColumnName="Description" msprop:Generator_ColumnVarNameInTable="columnDescription">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AddressLine1" msprop:Generator_ColumnPropNameInTable="AddressLine1Column" msprop:Generator_ColumnPropNameInRow="AddressLine1" msprop:Generator_UserColumnName="AddressLine1" msprop:Generator_ColumnVarNameInTable="columnAddressLine1">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AddressLine2" msprop:Generator_ColumnPropNameInTable="AddressLine2Column" msprop:Generator_ColumnPropNameInRow="AddressLine2" msprop:Generator_UserColumnName="AddressLine2" msprop:Generator_ColumnVarNameInTable="columnAddressLine2">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="City" msprop:Generator_ColumnPropNameInTable="CityColumn" msprop:Generator_ColumnPropNameInRow="City" msprop:Generator_UserColumnName="City" msprop:Generator_ColumnVarNameInTable="columnCity">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="StateRegion" msprop:Generator_ColumnPropNameInTable="StateRegionColumn" msprop:Generator_ColumnPropNameInRow="StateRegion" msprop:Generator_UserColumnName="StateRegion" msprop:Generator_ColumnVarNameInTable="columnStateRegion">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PostalCode" msprop:Generator_ColumnPropNameInTable="PostalCodeColumn" msprop:Generator_ColumnPropNameInRow="PostalCode" msprop:Generator_UserColumnName="PostalCode" msprop:Generator_ColumnVarNameInTable="columnPostalCode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Country" msprop:Generator_ColumnPropNameInTable="CountryColumn" msprop:Generator_ColumnPropNameInRow="Country" msprop:Generator_UserColumnName="Country" msprop:Generator_ColumnVarNameInTable="columnCountry">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Latitude" msprop:Generator_ColumnPropNameInTable="LatitudeColumn" msprop:Generator_ColumnPropNameInRow="Latitude" msprop:Generator_UserColumnName="Latitude" msprop:Generator_ColumnVarNameInTable="columnLatitude" type="xs:decimal" />
              <xs:element name="Longitude" msprop:Generator_ColumnPropNameInTable="LongitudeColumn" msprop:Generator_ColumnPropNameInRow="Longitude" msprop:Generator_UserColumnName="Longitude" msprop:Generator_ColumnVarNameInTable="columnLongitude" type="xs:decimal" />
              <xs:element name="Website" msprop:Generator_ColumnPropNameInTable="WebsiteColumn" msprop:Generator_ColumnPropNameInRow="Website" msprop:Generator_UserColumnName="Website" msprop:Generator_ColumnVarNameInTable="columnWebsite">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_UserColumnName="CreatedAt" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Coordinator" msprop:Generator_RowEvHandlerName="CoordinatorRowChangeEventHandler" msprop:Generator_RowDeletedName="CoordinatorRowDeleted" msprop:Generator_RowDeletingName="CoordinatorRowDeleting" msprop:Generator_RowEvArgName="CoordinatorRowChangeEvent" msprop:Generator_TablePropName="Coordinator" msprop:Generator_RowChangedName="CoordinatorRowChanged" msprop:Generator_UserTableName="Coordinator" msprop:Generator_RowChangingName="CoordinatorRowChanging" msprop:Generator_RowClassName="CoordinatorRow" msprop:Generator_TableClassName="CoordinatorDataTable" msprop:Generator_TableVarName="tableCoordinator">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CoordinatorId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInTable="CoordinatorIdColumn" msprop:Generator_ColumnPropNameInRow="CoordinatorId" msprop:Generator_UserColumnName="CoordinatorId" msprop:Generator_ColumnVarNameInTable="columnCoordinatorId" type="xs:long" />
              <xs:element name="UserId" msprop:Generator_ColumnPropNameInTable="UserIdColumn" msprop:Generator_ColumnPropNameInRow="UserId" msprop:Generator_UserColumnName="UserId" msprop:Generator_ColumnVarNameInTable="columnUserId" type="xs:long" />
              <xs:element name="HireDate" msprop:Generator_ColumnPropNameInTable="HireDateColumn" msprop:Generator_ColumnPropNameInRow="HireDate" msprop:Generator_UserColumnName="HireDate" msprop:Generator_ColumnVarNameInTable="columnHireDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_UserColumnName="Notes" msprop:Generator_ColumnVarNameInTable="columnNotes">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_UserColumnName="CreatedAt" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" type="xs:dateTime" />
              <xs:element name="UserFirstName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_Coordinator_User).FirstName" msprop:Generator_ColumnPropNameInTable="UserFirstNameColumn" msprop:Generator_ColumnPropNameInRow="UserFirstName" msprop:Generator_UserColumnName="UserFirstName" msprop:Generator_ColumnVarNameInTable="columnUserFirstName" type="xs:string" minOccurs="0" />
              <xs:element name="UserLastName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_Coordinator_User).LastName" msprop:Generator_ColumnPropNameInTable="UserLastNameColumn" msprop:Generator_ColumnPropNameInRow="UserLastName" msprop:Generator_UserColumnName="UserLastName" msprop:Generator_ColumnVarNameInTable="columnUserLastName" type="xs:string" minOccurs="0" />
              <xs:element name="UserFullName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_Coordinator_User).FullName" msprop:Generator_ColumnPropNameInTable="UserFullNameColumn" msprop:Generator_ColumnPropNameInRow="UserFullName" msprop:Generator_UserColumnName="UserFullName" msprop:Generator_ColumnVarNameInTable="columnUserFullName" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Role" msprop:Generator_RowEvHandlerName="RoleRowChangeEventHandler" msprop:Generator_RowDeletedName="RoleRowDeleted" msprop:Generator_RowDeletingName="RoleRowDeleting" msprop:Generator_RowEvArgName="RoleRowChangeEvent" msprop:Generator_TablePropName="Role" msprop:Generator_RowChangedName="RoleRowChanged" msprop:Generator_UserTableName="Role" msprop:Generator_RowChangingName="RoleRowChanging" msprop:Generator_RowClassName="RoleRow" msprop:Generator_TableClassName="RoleDataTable" msprop:Generator_TableVarName="tableRole">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="RoleId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInTable="RoleIdColumn" msprop:Generator_ColumnPropNameInRow="RoleId" msprop:Generator_UserColumnName="RoleId" msprop:Generator_ColumnVarNameInTable="columnRoleId" type="xs:long" />
              <xs:element name="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_UserColumnName="Name" msprop:Generator_ColumnVarNameInTable="columnName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_UserColumnName="CreatedAt" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" type="xs:dateTime" />
              <xs:element name="Notes" msprop:Generator_UserColumnName="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_ColumnVarNameInTable="columnNotes">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Tour" msprop:Generator_RowEvHandlerName="TourRowChangeEventHandler" msprop:Generator_RowDeletedName="TourRowDeleted" msprop:Generator_RowDeletingName="TourRowDeleting" msprop:Generator_RowEvArgName="TourRowChangeEvent" msprop:Generator_TablePropName="Tour" msprop:Generator_RowChangedName="TourRowChanged" msprop:Generator_UserTableName="Tour" msprop:Generator_RowChangingName="TourRowChanging" msprop:Generator_RowClassName="TourRow" msprop:Generator_TableClassName="TourDataTable" msprop:Generator_TableVarName="tableTour">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TourId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInTable="TourIdColumn" msprop:Generator_ColumnPropNameInRow="TourId" msprop:Generator_UserColumnName="TourId" msprop:Generator_ColumnVarNameInTable="columnTourId" type="xs:long" />
              <xs:element name="CoordinatorId" msprop:Generator_ColumnPropNameInTable="CoordinatorIdColumn" msprop:Generator_ColumnPropNameInRow="CoordinatorId" msprop:Generator_UserColumnName="CoordinatorId" msprop:Generator_ColumnVarNameInTable="columnCoordinatorId" type="xs:long" minOccurs="0" />
              <xs:element name="PriceAmount" msprop:Generator_ColumnPropNameInTable="PriceAmountColumn" msprop:Generator_ColumnPropNameInRow="PriceAmount" msprop:Generator_UserColumnName="PriceAmount" msprop:Generator_ColumnVarNameInTable="columnPriceAmount" type="xs:decimal" minOccurs="0" />
              <xs:element name="Status" msprop:Generator_ColumnPropNameInTable="StatusColumn" msprop:Generator_ColumnPropNameInRow="Status" msprop:Generator_UserColumnName="Status" msprop:Generator_ColumnVarNameInTable="columnStatus">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_UserColumnName="CreatedAt" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" type="xs:dateTime" />
              <xs:element name="UpdatedAt" msprop:Generator_ColumnPropNameInTable="UpdatedAtColumn" msprop:Generator_ColumnPropNameInRow="UpdatedAt" msprop:Generator_UserColumnName="UpdatedAt" msprop:Generator_ColumnVarNameInTable="columnUpdatedAt" type="xs:dateTime" />
              <xs:element name="PaxBooked" msprop:Generator_ColumnPropNameInTable="PaxBookedColumn" msprop:Generator_ColumnPropNameInRow="PaxBooked" msprop:Generator_UserColumnName="PaxBooked" msprop:Generator_ColumnVarNameInTable="columnPaxBooked" type="xs:int" minOccurs="0" />
              <xs:element name="TourCode" msprop:Generator_UserColumnName="TourCode" msprop:Generator_ColumnPropNameInTable="TourCodeColumn" msprop:Generator_ColumnPropNameInRow="TourCode" msprop:Generator_ColumnVarNameInTable="columnTourCode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ArrivalDate" msprop:Generator_UserColumnName="ArrivalDate" msprop:Generator_ColumnPropNameInTable="ArrivalDateColumn" msprop:Generator_ColumnPropNameInRow="ArrivalDate" msprop:Generator_ColumnVarNameInTable="columnArrivalDate" type="xs:dateTime" />
              <xs:element name="DepartureDate" msprop:Generator_UserColumnName="DepartureDate" msprop:Generator_ColumnPropNameInTable="DepartureDateColumn" msprop:Generator_ColumnPropNameInRow="DepartureDate" msprop:Generator_ColumnVarNameInTable="columnDepartureDate" type="xs:dateTime" />
              <xs:element name="Allotment" msprop:Generator_UserColumnName="Allotment" msprop:Generator_ColumnPropNameInTable="AllotmentColumn" msprop:Generator_ColumnPropNameInRow="Allotment" msprop:Generator_ColumnVarNameInTable="columnAllotment" type="xs:int" minOccurs="0" />
              <xs:element name="Notes" msprop:Generator_UserColumnName="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_ColumnVarNameInTable="columnNotes">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="TourAccommodation" msprop:Generator_RowEvHandlerName="TourAccommodationRowChangeEventHandler" msprop:Generator_RowDeletedName="TourAccommodationRowDeleted" msprop:Generator_RowDeletingName="TourAccommodationRowDeleting" msprop:Generator_RowEvArgName="TourAccommodationRowChangeEvent" msprop:Generator_TablePropName="TourAccommodation" msprop:Generator_RowChangedName="TourAccommodationRowChanged" msprop:Generator_UserTableName="TourAccommodation" msprop:Generator_RowChangingName="TourAccommodationRowChanging" msprop:Generator_RowClassName="TourAccommodationRow" msprop:Generator_TableClassName="TourAccommodationDataTable" msprop:Generator_TableVarName="tableTourAccommodation">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TourAccommodationId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInTable="TourAccommodationIdColumn" msprop:Generator_ColumnPropNameInRow="TourAccommodationId" msprop:Generator_UserColumnName="TourAccommodationId" msprop:Generator_ColumnVarNameInTable="columnTourAccommodationId" type="xs:long" />
              <xs:element name="TourId" msprop:Generator_ColumnPropNameInTable="TourIdColumn" msprop:Generator_ColumnPropNameInRow="TourId" msprop:Generator_UserColumnName="TourId" msprop:Generator_ColumnVarNameInTable="columnTourId" type="xs:long" />
              <xs:element name="AccommodationId" msprop:Generator_ColumnPropNameInTable="AccommodationIdColumn" msprop:Generator_ColumnPropNameInRow="AccommodationId" msprop:Generator_UserColumnName="AccommodationId" msprop:Generator_ColumnVarNameInTable="columnAccommodationId" type="xs:long" />
              <xs:element name="CheckIn" msprop:Generator_ColumnPropNameInTable="CheckInColumn" msprop:Generator_ColumnPropNameInRow="CheckIn" msprop:Generator_UserColumnName="CheckIn" msprop:Generator_ColumnVarNameInTable="columnCheckIn" type="xs:dateTime" />
              <xs:element name="CheckOut" msprop:Generator_ColumnPropNameInTable="CheckOutColumn" msprop:Generator_ColumnPropNameInRow="CheckOut" msprop:Generator_UserColumnName="CheckOut" msprop:Generator_ColumnVarNameInTable="columnCheckOut" type="xs:dateTime" />
              <xs:element name="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_UserColumnName="Notes" msprop:Generator_ColumnVarNameInTable="columnNotes">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="TourAttraction" msprop:Generator_RowEvHandlerName="TourAttractionRowChangeEventHandler" msprop:Generator_RowDeletedName="TourAttractionRowDeleted" msprop:Generator_RowDeletingName="TourAttractionRowDeleting" msprop:Generator_RowEvArgName="TourAttractionRowChangeEvent" msprop:Generator_TablePropName="TourAttraction" msprop:Generator_RowChangedName="TourAttractionRowChanged" msprop:Generator_UserTableName="TourAttraction" msprop:Generator_RowChangingName="TourAttractionRowChanging" msprop:Generator_RowClassName="TourAttractionRow" msprop:Generator_TableClassName="TourAttractionDataTable" msprop:Generator_TableVarName="tableTourAttraction">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TourAttractionId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInTable="TourAttractionIdColumn" msprop:Generator_ColumnPropNameInRow="TourAttractionId" msprop:Generator_UserColumnName="TourAttractionId" msprop:Generator_ColumnVarNameInTable="columnTourAttractionId" type="xs:long" />
              <xs:element name="TourId" msprop:Generator_ColumnPropNameInTable="TourIdColumn" msprop:Generator_ColumnPropNameInRow="TourId" msprop:Generator_UserColumnName="TourId" msprop:Generator_ColumnVarNameInTable="columnTourId" type="xs:long" />
              <xs:element name="AttractionId" msprop:Generator_ColumnPropNameInTable="AttractionIdColumn" msprop:Generator_ColumnPropNameInRow="AttractionId" msprop:Generator_UserColumnName="AttractionId" msprop:Generator_ColumnVarNameInTable="columnAttractionId" type="xs:long" />
              <xs:element name="VisitDateTime" msprop:Generator_ColumnPropNameInTable="VisitDateTimeColumn" msprop:Generator_ColumnPropNameInRow="VisitDateTime" msprop:Generator_UserColumnName="VisitDateTime" msprop:Generator_ColumnVarNameInTable="columnVisitDateTime" type="xs:dateTime" minOccurs="0" />
              <xs:element name="SequenceNumber" msprop:Generator_ColumnPropNameInTable="SequenceNumberColumn" msprop:Generator_ColumnPropNameInRow="SequenceNumber" msprop:Generator_UserColumnName="SequenceNumber" msprop:Generator_ColumnVarNameInTable="columnSequenceNumber" type="xs:short" minOccurs="0" />
              <xs:element name="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_UserColumnName="Notes" msprop:Generator_ColumnVarNameInTable="columnNotes">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="TourVehicle" msprop:Generator_RowEvHandlerName="TourVehicleRowChangeEventHandler" msprop:Generator_RowDeletedName="TourVehicleRowDeleted" msprop:Generator_RowDeletingName="TourVehicleRowDeleting" msprop:Generator_RowEvArgName="TourVehicleRowChangeEvent" msprop:Generator_TablePropName="TourVehicle" msprop:Generator_RowChangedName="TourVehicleRowChanged" msprop:Generator_UserTableName="TourVehicle" msprop:Generator_RowChangingName="TourVehicleRowChanging" msprop:Generator_RowClassName="TourVehicleRow" msprop:Generator_TableClassName="TourVehicleDataTable" msprop:Generator_TableVarName="tableTourVehicle">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TourVehicleId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInTable="TourVehicleIdColumn" msprop:Generator_ColumnPropNameInRow="TourVehicleId" msprop:Generator_UserColumnName="TourVehicleId" msprop:Generator_ColumnVarNameInTable="columnTourVehicleId" type="xs:long" />
              <xs:element name="TourId" msprop:Generator_ColumnPropNameInTable="TourIdColumn" msprop:Generator_ColumnPropNameInRow="TourId" msprop:Generator_UserColumnName="TourId" msprop:Generator_ColumnVarNameInTable="columnTourId" type="xs:long" />
              <xs:element name="VehicleId" msprop:Generator_ColumnPropNameInTable="VehicleIdColumn" msprop:Generator_ColumnPropNameInRow="VehicleId" msprop:Generator_UserColumnName="VehicleId" msprop:Generator_ColumnVarNameInTable="columnVehicleId" type="xs:long" />
              <xs:element name="AssignedFrom" msprop:Generator_ColumnPropNameInTable="AssignedFromColumn" msprop:Generator_ColumnPropNameInRow="AssignedFrom" msprop:Generator_UserColumnName="AssignedFrom" msprop:Generator_ColumnVarNameInTable="columnAssignedFrom" type="xs:dateTime" minOccurs="0" />
              <xs:element name="AssignedTo" msprop:Generator_ColumnPropNameInTable="AssignedToColumn" msprop:Generator_ColumnPropNameInRow="AssignedTo" msprop:Generator_UserColumnName="AssignedTo" msprop:Generator_ColumnVarNameInTable="columnAssignedTo" type="xs:dateTime" minOccurs="0" />
              <xs:element name="DriverName" msprop:Generator_ColumnPropNameInTable="DriverNameColumn" msprop:Generator_ColumnPropNameInRow="DriverName" msprop:Generator_UserColumnName="DriverName" msprop:Generator_ColumnVarNameInTable="columnDriverName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_UserColumnName="Notes" msprop:Generator_ColumnVarNameInTable="columnNotes">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="User" msprop:Generator_RowEvHandlerName="UserRowChangeEventHandler" msprop:Generator_RowDeletedName="UserRowDeleted" msprop:Generator_RowDeletingName="UserRowDeleting" msprop:Generator_RowEvArgName="UserRowChangeEvent" msprop:Generator_TablePropName="User" msprop:Generator_RowChangedName="UserRowChanged" msprop:Generator_UserTableName="User" msprop:Generator_RowChangingName="UserRowChanging" msprop:Generator_RowClassName="UserRow" msprop:Generator_TableClassName="UserDataTable" msprop:Generator_TableVarName="tableUser">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="UserId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInTable="UserIdColumn" msprop:Generator_ColumnPropNameInRow="UserId" msprop:Generator_UserColumnName="UserId" msprop:Generator_ColumnVarNameInTable="columnUserId" type="xs:long" />
              <xs:element name="RoleId" msprop:Generator_ColumnPropNameInTable="RoleIdColumn" msprop:Generator_ColumnPropNameInRow="RoleId" msprop:Generator_UserColumnName="RoleId" msprop:Generator_ColumnVarNameInTable="columnRoleId" type="xs:long" />
              <xs:element name="Username" msprop:Generator_ColumnPropNameInTable="UsernameColumn" msprop:Generator_ColumnPropNameInRow="Username" msprop:Generator_UserColumnName="Username" msprop:Generator_ColumnVarNameInTable="columnUsername">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Email" msprop:Generator_ColumnPropNameInTable="EmailColumn" msprop:Generator_ColumnPropNameInRow="Email" msprop:Generator_UserColumnName="Email" msprop:Generator_ColumnVarNameInTable="columnEmail">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Password" msprop:Generator_ColumnPropNameInTable="PasswordColumn" msprop:Generator_ColumnPropNameInRow="Password" msprop:Generator_UserColumnName="Password" msprop:Generator_ColumnVarNameInTable="columnPassword">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FirstName" msprop:Generator_ColumnPropNameInTable="FirstNameColumn" msprop:Generator_ColumnPropNameInRow="FirstName" msprop:Generator_UserColumnName="FirstName" msprop:Generator_ColumnVarNameInTable="columnFirstName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LastName" msprop:Generator_ColumnPropNameInTable="LastNameColumn" msprop:Generator_ColumnPropNameInRow="LastName" msprop:Generator_UserColumnName="LastName" msprop:Generator_ColumnVarNameInTable="columnLastName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Phone" msprop:Generator_ColumnPropNameInTable="PhoneColumn" msprop:Generator_ColumnPropNameInRow="Phone" msprop:Generator_UserColumnName="Phone" msprop:Generator_ColumnVarNameInTable="columnPhone" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="IsActive" msprop:Generator_ColumnPropNameInTable="IsActiveColumn" msprop:Generator_ColumnPropNameInRow="IsActive" msprop:Generator_UserColumnName="IsActive" msprop:Generator_ColumnVarNameInTable="columnIsActive" type="xs:boolean" />
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_UserColumnName="CreatedAt" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" type="xs:dateTime" />
              <xs:element name="UpdatedAt" msprop:Generator_ColumnPropNameInTable="UpdatedAtColumn" msprop:Generator_ColumnPropNameInRow="UpdatedAt" msprop:Generator_UserColumnName="UpdatedAt" msprop:Generator_ColumnVarNameInTable="columnUpdatedAt" type="xs:dateTime" />
              <xs:element name="FullName" msdata:ReadOnly="true" msdata:Expression="FirstName + ' ' + LastName" msprop:Generator_ColumnPropNameInTable="FullNameColumn" msprop:Generator_ColumnPropNameInRow="FullName" msprop:Generator_UserColumnName="FullName" msprop:Generator_ColumnVarNameInTable="columnFullName" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Accommodation" />
      <xs:field xpath="mstns:AccommodationId" />
    </xs:unique>
    <xs:unique name="Vehicle_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Vehicle" />
      <xs:field xpath="mstns:VehicleId" />
    </xs:unique>
    <xs:unique name="Attraction_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Attraction" />
      <xs:field xpath="mstns:AttractionId" />
    </xs:unique>
    <xs:unique name="Coordinator_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Coordinator" />
      <xs:field xpath="mstns:CoordinatorId" />
    </xs:unique>
    <xs:unique name="Role_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Role" />
      <xs:field xpath="mstns:RoleId" />
    </xs:unique>
    <xs:unique name="Tour_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Tour" />
      <xs:field xpath="mstns:TourId" />
    </xs:unique>
    <xs:unique name="TourAccommodation_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:TourAccommodation" />
      <xs:field xpath="mstns:TourAccommodationId" />
    </xs:unique>
    <xs:unique name="TourAttraction_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:TourAttraction" />
      <xs:field xpath="mstns:TourAttractionId" />
    </xs:unique>
    <xs:unique name="TourVehicle_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:TourVehicle" />
      <xs:field xpath="mstns:TourVehicleId" />
    </xs:unique>
    <xs:unique name="User_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:User" />
      <xs:field xpath="mstns:UserId" />
    </xs:unique>
    <xs:keyref name="FK_User_Role" refer="Role_Constraint1" msprop:rel_Generator_UserParentTable="Role" msprop:rel_Generator_UserChildTable="User" msprop:rel_Generator_RelationVarName="relationFK_User_Role" msprop:rel_Generator_ChildPropName="GetUserRows" msprop:rel_Generator_UserRelationName="FK_User_Role" msprop:rel_Generator_ParentPropName="RoleRow" msdata:UpdateRule="None" msdata:DeleteRule="None">
      <xs:selector xpath=".//mstns:User" />
      <xs:field xpath="mstns:RoleId" />
    </xs:keyref>
    <xs:keyref name="FK_TourVehicle_Tour" refer="Tour_Constraint1" msprop:rel_Generator_UserParentTable="Tour" msprop:rel_Generator_UserChildTable="TourVehicle" msprop:rel_Generator_RelationVarName="relationFK_TourVehicle_Tour" msprop:rel_Generator_ChildPropName="GetTourVehicleRows" msprop:rel_Generator_UserRelationName="FK_TourVehicle_Tour" msprop:rel_Generator_ParentPropName="TourRow" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:TourVehicle" />
      <xs:field xpath="mstns:TourId" />
    </xs:keyref>
    <xs:keyref name="FK_TourVehicle_Vehicle" refer="Vehicle_Constraint1" msprop:rel_Generator_UserParentTable="Vehicle" msprop:rel_Generator_UserChildTable="TourVehicle" msprop:rel_Generator_RelationVarName="relationFK_TourVehicle_Vehicle" msprop:rel_Generator_ChildPropName="GetTourVehicleRows" msprop:rel_Generator_UserRelationName="FK_TourVehicle_Vehicle" msprop:rel_Generator_ParentPropName="VehicleRow" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:TourVehicle" />
      <xs:field xpath="mstns:VehicleId" />
    </xs:keyref>
    <xs:keyref name="FK_TourAttraction_Tour" refer="Tour_Constraint1" msprop:rel_Generator_UserParentTable="Tour" msprop:rel_Generator_UserChildTable="TourAttraction" msprop:rel_Generator_RelationVarName="relationFK_TourAttraction_Tour" msprop:rel_Generator_ChildPropName="GetTourAttractionRows" msprop:rel_Generator_UserRelationName="FK_TourAttraction_Tour" msprop:rel_Generator_ParentPropName="TourRow" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:TourAttraction" />
      <xs:field xpath="mstns:TourId" />
    </xs:keyref>
    <xs:keyref name="FK_TourAttraction_Attraction" refer="Attraction_Constraint1" msprop:rel_Generator_UserParentTable="Attraction" msprop:rel_Generator_UserChildTable="TourAttraction" msprop:rel_Generator_RelationVarName="relationFK_TourAttraction_Attraction" msprop:rel_Generator_ChildPropName="GetTourAttractionRows" msprop:rel_Generator_UserRelationName="FK_TourAttraction_Attraction" msprop:rel_Generator_ParentPropName="AttractionRow" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:TourAttraction" />
      <xs:field xpath="mstns:AttractionId" />
    </xs:keyref>
    <xs:keyref name="FK_TourAccommodation_Tour" refer="Tour_Constraint1" msprop:rel_Generator_UserParentTable="Tour" msprop:rel_Generator_UserChildTable="TourAccommodation" msprop:rel_Generator_RelationVarName="relationFK_TourAccommodation_Tour" msprop:rel_Generator_ChildPropName="GetTourAccommodationRows" msprop:rel_Generator_UserRelationName="FK_TourAccommodation_Tour" msprop:rel_Generator_ParentPropName="TourRow" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:TourAccommodation" />
      <xs:field xpath="mstns:TourId" />
    </xs:keyref>
    <xs:keyref name="FK_TourAccommodation_Accommodation" refer="Constraint1" msprop:rel_Generator_UserParentTable="Accommodation" msprop:rel_Generator_UserChildTable="TourAccommodation" msprop:rel_Generator_RelationVarName="relationFK_TourAccommodation_Accommodation" msprop:rel_Generator_ChildPropName="GetTourAccommodationRows" msprop:rel_Generator_UserRelationName="FK_TourAccommodation_Accommodation" msprop:rel_Generator_ParentPropName="AccommodationRow" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:TourAccommodation" />
      <xs:field xpath="mstns:AccommodationId" />
    </xs:keyref>
    <xs:keyref name="FK_Tour_Coordinator" refer="Coordinator_Constraint1" msprop:rel_Generator_UserParentTable="Coordinator" msprop:rel_Generator_UserChildTable="Tour" msprop:rel_Generator_RelationVarName="relationFK_Tour_Coordinator" msprop:rel_Generator_ChildPropName="GetTourRows" msprop:rel_Generator_UserRelationName="FK_Tour_Coordinator" msprop:rel_Generator_ParentPropName="CoordinatorRow" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:Tour" />
      <xs:field xpath="mstns:CoordinatorId" />
    </xs:keyref>
    <xs:keyref name="FK_Coordinator_User" refer="User_Constraint1" msprop:rel_Generator_UserParentTable="User" msprop:rel_Generator_UserChildTable="Coordinator" msprop:rel_Generator_RelationVarName="relationFK_Coordinator_User" msprop:rel_Generator_ChildPropName="GetCoordinatorRows" msprop:rel_Generator_ParentPropName="UserRow" msprop:rel_Generator_UserRelationName="FK_Coordinator_User" msdata:UpdateRule="None" msdata:DeleteRule="None">
      <xs:selector xpath=".//mstns:Coordinator" />
      <xs:field xpath="mstns:UserId" />
    </xs:keyref>
  </xs:element>
</xs:schema>