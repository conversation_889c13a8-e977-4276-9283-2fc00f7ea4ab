﻿using Janus.Windows.GridEX;
using Janus.Windows.UI.Dock;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Tourun.UI
{
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;

                this.Text = Properties.Resources.AppName;

                //this.toursGrid.LoadComponentSettings();
                //this.accommodationsGrid.LoadComponentSettings();
                //this.attractionsGrid.LoadComponentSettings();

                //Ρυθμίζει τα panels.
                this.toursPanel.Closed = false;
                //this.toursReservationsTimelinePanel.Closed = false;
                this.accommodationsPanel.Closed = true;
                this.attractionsPanel.Closed = true;
                this.coordinatorsPanel.Closed = true;
                this.vehiclesPanel.Closed = true;

                #region  Ρυθμίζει όλα τα PrinterSettings και PageSettings
                //Για τα Grids
                if (Properties.Settings.Default.DataGridReportsPageSettings == null)
                {
                    PrinterSettings printerSettings = new PrinterSettings();
                    Properties.Settings.Default.DataGridReportsPageSettings = new PageSettings(printerSettings);
                    Properties.Settings.Default.DataGridReportsPageSettings.Margins = new Margins(50, 50, 50, 50);
                }

                #endregion

                this.UpdateCommands();
                this.UpdateStatusBar();
            }
            catch (CaughtedException exp)
            {
                MessageBox.Show(exp.Message, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void panelManager_GroupSelectedPanelChanged(object sender, Janus.Windows.UI.Dock.GroupSelectedPanelChangedEventArgs e)
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;
                this.toursPanel.Closed = !(e.SelectedPanel == this.toursNavigationPanel);
                this.accommodationsPanel.Closed = !(e.SelectedPanel == this.accommodationsNavigationPanel);
                this.attractionsPanel.Closed = !(e.SelectedPanel == this.attractionsNavigationPanel);
                this.coordinatorsPanel.Closed = !(e.SelectedPanel == this.coordinatorsNavigationPanel);
                this.vehiclesPanel.Closed = !(e.SelectedPanel == this.vehiclesNavigationPanel);

                this.SetDataBindings();
                this.UpdateCommands();
                this.UpdateStatusBar();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void MainForm_Shown(object sender, EventArgs e)
        {
            try
            {
                Cursor.Current = Cursors.Default;

                LoginForm form = new LoginForm();
                form.Owner = this;
                //Αν το username και το password ήταν σωστά.
                if (form.ShowDialog() == DialogResult.OK)
                {

                }
                else
                {
                    this.Close();
                    return;
                }
                Cursor.Current = Cursors.WaitCursor;

                this.SetControlsForRole(Globals.Application.CurrentUserRole);
                this.SetDataBindings();
                Data.GetAllData();

                this.toursGrid.MoveFirst();
                this.accommodationsGrid.MoveFirst();
                this.attractionsGrid.MoveFirst();
                this.coordinatorsGrid.MoveFirst();
                this.vehiclesGrid.MoveFirst();

                //this.vehiclesNavigationPanel.Activate();
                //this.toursNavigationPanel.Activate();
                foreach (UIPanelBase panel in this.navigationPanel.Panels)
                {
                    if (panel.Closed == false)
                    {
                        panel.Activate();
                        return;
                    }
                }

                this.UpdateCommands();
                this.UpdateStatusBar();



                //this.WindowState = FormWindowState.Normal;
                //this.Width = 1280;
                //this.Height = 720;
            }
            catch (CaughtedException exp)
            {
                MessageBox.Show(exp.Message, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {

        }

        private void SetDataBindings()
        {
            try
            {
                this.toursBindingSource.DataSource = Data.DataSet;
                this.toursBindingSource.DataMember = Data.DataSet.Tour.TableName;
                this.toursGrid.DataSource = this.toursBindingSource;

                this.coordinatorsBindingSource.DataSource = Data.DataSet;
                this.coordinatorsBindingSource.DataMember = Data.DataSet.Coordinator.TableName;
                this.coordinatorsGrid.DataSource = this.coordinatorsBindingSource;

                this.accommodationsBindingSource.DataSource = Data.DataSet;
                this.accommodationsBindingSource.DataMember = Data.DataSet.Accommodation.TableName;
                this.accommodationsGrid.DataSource = this.accommodationsBindingSource;

                this.attractionsBindingSource.DataSource = Data.DataSet;
                this.attractionsBindingSource.DataMember = Data.DataSet.Attraction.TableName;
                this.attractionsGrid.DataSource = this.attractionsBindingSource;

                this.vehiclesBindingSource.DataSource = Data.DataSet;
                this.vehiclesBindingSource.DataMember = Data.DataSet.Vehicle.TableName;
                this.vehiclesGrid.DataSource = this.vehiclesBindingSource;

            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void ribbon_CommandClick(object sender, Janus.Windows.Ribbon.CommandEventArgs e)
        {
            try
            {
                switch (e.Command.Key)
                {
                    case "NewTour":
                        {
                            this.NewTour();
                            break;
                        }
                    case "NewCoordinator":
                        {
                            this.NewCoordinator();
                            break;
                        }
                    case "NewAttraction":
                        {
                            this.NewAttraction();
                            break;
                        }
                    case "NewAccommodation":
                        {
                            //this.NewAccommodation();
                            break;
                        }
                    case "NewVehicle":
                        {
                            this.NewVehicle();
                            break;
                        }
                    
                    case "EditData":
                    case "Edit":
                        {
                            if (!this.toursPanel.Closed && this.toursGrid.SelectedItems.Count > 0)
                            {
                                this.EditData(this.toursGrid.GetRow());
                            }
                            else if (!this.coordinatorsPanel.Closed && this.coordinatorsGrid.SelectedItems.Count > 0)
                            {
                                this.EditData(this.coordinatorsGrid.GetRow());
                            }
                            else if (!this.attractionsPanel.Closed && this.attractionsGrid.SelectedItems.Count > 0)
                            {
                                this.EditData(this.attractionsGrid.GetRow());
                            }
                            else if (!this.accommodationsPanel.Closed && this.accommodationsGrid.SelectedItems.Count > 0)
                            {
                                this.EditData(this.accommodationsGrid.GetRow());
                            }
                            else if (!this.vehiclesPanel.Closed && this.vehiclesGrid.SelectedItems.Count > 0)
                            {
                                this.EditData(this.vehiclesGrid.GetRow());
                            }
                            
                            break;
                        }
                    case "DeleteData":
                    case "Delete":
                        {
                            if (!this.toursPanel.Closed)
                            {
                                this.DeleteTour();
                            }
                            else if (!this.coordinatorsPanel.Closed)
                            {
                                this.DeleteCoordinator();
                            }
                            else if (!this.attractionsPanel.Closed)
                            {
                                this.attractionsGrid.Delete();
                            }
                            else if (!this.accommodationsPanel.Closed)
                            {
                                this.accommodationsGrid.Delete();
                            }
                            else if (!this.vehiclesPanel.Closed)
                            {
                                this.vehiclesGrid.Delete();
                            }

                            break;
                        }
                    case "RefreshData":
                        {
                            this.RefreshData();
                            break;
                        }
                    case "ClearFilters":
                        {
                            this.ClearFilters();
                            break;
                        }
                    case "EditColumns":
                        {
                            this.EditColumns();
                            break;
                        }
                    case "Settings":
                        {
                            //this.ShowSettingsForm();
                            break;
                        }
                    case "ExpandAll":
                        {
                            this.ExpandAll();
                            break;
                        }
                    case "CollapseAll":
                        {
                            this.CollapseAll();
                            break;
                        }
                    case "ExportToExcel":
                        {
                            this.ExportToExcel();
                            break;
                        }
                    case "GridReport":
                        {
                            this.ShowGridReport();
                            break;
                        }
                    case "TimelineReport":
                        {
                            //this.ShowTimelineReport();
                            break;
                        }
                    case "AboutApp":
                        {
                            this.ShowAboutForm();
                            break;
                        }
                    case "ResetGrids":
                        {
                            this.ResetGrids();
                            break;
                        }
                    case "ChangeDatabase":
                        {
                            this.ShowChangeDatabaseForm();
                            break;
                        }
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void EditData(GridEXRow row)  //Top function
        {
            try
            {
                if (row != null)
                {
                    if (row.RowType == RowType.Record)
                    {
                        switch (row.Table.Key)
                        {
                            case "Tours":
                                {
                                    DataRowView drv = row.DataRow as DataRowView;
                                    TourunDataSet.TourRow toursRow = drv.Row as TourunDataSet.TourRow;
                                    this.EditTour(toursRow.TourId);
                                    break;
                                }
                            case "Coordinators":
                                {
                                    DataRowView drv = row.DataRow as DataRowView;
                                    TourunDataSet.CoordinatorRow coordinatorRow = drv.Row as TourunDataSet.CoordinatorRow;
                                    this.EditCoordinator(coordinatorRow.CoordinatorId);
                                    break;
                                }
                            case "Attractions":
                                {
                                    DataRowView drv = row.DataRow as DataRowView;
                                    TourunDataSet.AttractionRow attractionRow = drv.Row as TourunDataSet.AttractionRow;
                                    this.EditAttraction(attractionRow.AttractionId);
                                    break;
                                }
                            case "Vehicles":
                                {
                                    DataRowView drv = row.DataRow as DataRowView;
                                    TourunDataSet.VehicleRow vehicleRow = drv.Row as TourunDataSet.VehicleRow;
                                    this.EditVehicle(vehicleRow.VehicleId);
                                    break;
                                }

                        }
                    }
                    this.UpdateCommands();
                }
            }
            catch (Exception exp)
            {
                Globals.Application.IsRefreshingData = false;
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage + exp.Message, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void UpdateCommands()
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;

                #region
                this.editDataCommand.Enabled = false;
                this.deleteDataCommand.Enabled = false;

                this.exportToExcelCommand.Visible = false;
                this.filtersRibbonGroup.Visible = false;
                this.gridReportCommand.Visible = false;

                if (this.toursPanel.Closed == false)
                {
                    //Για τα Tours
                    this.filtersRibbonGroup.Visible = true;
                    this.exportToExcelCommand.Visible = true;
                    this.gridReportCommand.Visible = true;

                    if (this.toursGrid.RootTable != null)
                    {
                        if (this.toursGrid.SelectedItems.Count == 1)
                        {
                            if (this.toursGrid.SelectedItems[0].Position >= 0)
                            {
                                if (this.toursGrid.GetRow(this.toursGrid.SelectedItems[0].Position).Table.Key == "Tours")
                                {
                                    if (this.toursGrid.GetRow(this.toursGrid.SelectedItems[0].Position).RowType == RowType.Record)
                                    {
                                        this.editDataCommand.Enabled = true;
                                        this.deleteDataCommand.Enabled = true;
                                    }
                                }
                            }
                        }
                    }
                }
                else if (this.attractionsPanel.Closed == false )
                {
                    //Για τα Attractions Grid
                    this.filtersRibbonGroup.Visible = false;
                    this.gridReportCommand.Visible = true;

                    if (this.attractionsGrid.SelectedItems.Count == 1)
                    {
                        if (this.attractionsGrid.SelectedItems[0].RowType == RowType.Record)
                        {
                            this.editDataCommand.Enabled = true;
                            this.deleteDataCommand.Enabled = true;
                        }
                    }
                }
                else if (this.coordinatorsPanel.Closed == false)
                {
                    //Για τα Coordinators
                    this.filtersRibbonGroup.Visible = true;
                    this.exportToExcelCommand.Visible = true;
                    this.gridReportCommand.Visible = true;

                    if (this.coordinatorsGrid.RootTable != null)
                    {
                        if (this.coordinatorsGrid.SelectedItems.Count == 1)
                        {
                            if (this.coordinatorsGrid.SelectedItems[0].Position >= 0)
                            {
                                if (this.coordinatorsGrid.GetRow(this.coordinatorsGrid.SelectedItems[0].Position).Table.Key == "Coordinators")
                                {
                                    if (this.coordinatorsGrid.GetRow(this.coordinatorsGrid.SelectedItems[0].Position).RowType == RowType.Record)
                                    {
                                        this.editDataCommand.Enabled = true;
                                        this.deleteDataCommand.Enabled = true;
                                    }
                                }
                            }
                        }
                    }
                }
                else if (this.accommodationsPanel.Closed == false)
                {
                    //Για τα Accommodations Grid
                    this.filtersRibbonGroup.Visible = false;
                    this.gridReportCommand.Visible = true;

                    if (this.accommodationsGrid.SelectedItems.Count == 1)
                    {
                        if (this.accommodationsGrid.SelectedItems[0].RowType == RowType.Record)
                        {
                            this.editDataCommand.Enabled = true;
                            this.deleteDataCommand.Enabled = true;
                        }
                    }
                }
                else if (this.vehiclesPanel.Closed == false)
                {
                    //Για τα Vehicles Grid
                    this.filtersRibbonGroup.Visible = false;
                    this.gridReportCommand.Visible = true;

                    if (this.vehiclesGrid.SelectedItems.Count == 1)
                    {
                        if (this.vehiclesGrid.SelectedItems[0].RowType == RowType.Record)
                        {
                            this.editDataCommand.Enabled = true;
                            this.deleteDataCommand.Enabled = true;
                        }
                    }
                }

                #endregion
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void UpdateStatusBar()  //Top function
        {
            try
            {
                this.infoCommand.Text = "";  //Καθαρίζει πρώτα το StatusBar

                if (this.toursPanel.Closed == false )
                {
                    this.infoCommand.Text = this.toursGrid.RecordCount.ToString() + " " + Properties.Resources.RecordsText.ToLower();
                }
                else if (this.coordinatorsPanel.Closed == false)
                {
                    this.infoCommand.Text = this.coordinatorsGrid.RecordCount.ToString() + " " + Properties.Resources.RecordsText.ToLower();
                }
                else if (this.attractionsPanel.Closed == false)
                {
                    this.infoCommand.Text = this.attractionsGrid.RecordCount.ToString() + " " + Properties.Resources.RecordsText.ToLower();
                }
                else if (this.accommodationsPanel.Closed == false)
                {
                    this.infoCommand.Text = this.accommodationsGrid.RecordCount.ToString() + " " + Properties.Resources.RecordsText.ToLower();
                }
                else if (this.vehiclesPanel.Closed == false)
                {
                    this.infoCommand.Text = this.vehiclesGrid.RecordCount.ToString() + " " + Properties.Resources.RecordsText.ToLower();
                }
               
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void RefreshData()  //Top function
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;

                Data.GetAllData();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.WaitCursor;
            }
        }

        private void ClearFilters()
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;
                if (this.toursPanel.Visible)
                {
                    this.toursGrid.RemoveFilters();
                }
                else if (this.attractionsPanel.Visible)
                {
                    this.attractionsGrid.RemoveFilters();
                }
                else if (this.coordinatorsPanel.Visible)
                {
                    this.coordinatorsGrid.RemoveFilters();
                }
                else if (this.accommodationsPanel.Visible)
                {
                    this.accommodationsGrid.RemoveFilters();
                }
                else if (this.vehiclesPanel.Visible)
                {
                    this.vehiclesGrid.RemoveFilters();
                }
             
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.WaitCursor;
            }
        }

        private void ExpandAll()
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;

                if (this.toursPanel.Visible)
                {
                    this.toursGrid.ExpandGroups();
                    this.toursGrid.ExpandRecords();
                }
                else if (this.attractionsPanel.Visible)
                {
                    this.attractionsGrid.ExpandGroups();
                    this.attractionsGrid.ExpandRecords();
                }
                else if (this.accommodationsPanel.Visible)
                {
                    this.accommodationsGrid.ExpandGroups();
                    this.accommodationsGrid.ExpandRecords();
                }
                else if (this.coordinatorsPanel.Visible)
                {
                    this.coordinatorsGrid.ExpandGroups();
                    this.coordinatorsGrid.ExpandRecords();
                }
                else if (this.vehiclesPanel.Visible)
                {
                    this.vehiclesGrid.ExpandGroups();
                    this.vehiclesGrid.ExpandRecords();
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void CollapseAll()
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;

                if (this.toursPanel.Visible)
                {
                    this.toursGrid.CollapseGroups();
                    this.toursGrid.CollapseRecords();
                }
                else if (this.coordinatorsPanel.Visible)
                {
                    this.coordinatorsGrid.CollapseGroups();
                    this.coordinatorsGrid.CollapseRecords();
                }
                else if (this.attractionsPanel.Visible)
                {
                    this.attractionsGrid.CollapseGroups();
                    this.attractionsGrid.CollapseRecords();
                }
                else if (this.accommodationsPanel.Visible)
                {
                    this.accommodationsGrid.CollapseGroups();
                    this.accommodationsGrid.CollapseRecords();
                }
                else if (this.vehiclesPanel.Visible)
                {
                    this.vehiclesGrid.CollapseGroups();
                    this.vehiclesGrid.CollapseRecords();
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void EditColumns()  //Top function
        {
            //try
            //{
            //    if (this.contactsPanel.Visible)
            //    {
            //        this.contactsGrid.AllowRemoveColumns = Janus.Windows.GridEX.InheritableBoolean.True;
            //        this.contactsGrid.ShowFieldChooser(this, Properties.Resources.FieldsText, this.contactsGrid.RootTable, true);
            //    }
            //    else if (this.veterinariansPanel.Visible)
            //    {
            //        this.veterinariansGrid.AllowRemoveColumns = Janus.Windows.GridEX.InheritableBoolean.True;
            //        this.veterinariansGrid.ShowFieldChooser(this, Properties.Resources.FieldsText, this.veterinariansGrid.RootTable, true);
            //    }
            //    else if (this.reservationsPanel.Visible)
            //    {
            //        this.reservationsGrid.AllowRemoveColumns = Janus.Windows.GridEX.InheritableBoolean.True;
            //        this.reservationsGrid.ShowFieldChooser(this, Properties.Resources.FieldsText, this.reservationsGrid.RootTable, true);
            //    }
            //    else if (this.disposalsPanel.Visible)
            //    {
            //        this.disposalsGrid.AllowRemoveColumns = Janus.Windows.GridEX.InheritableBoolean.True;
            //        this.disposalsGrid.ShowFieldChooser(this, Properties.Resources.FieldsText, this.disposalsGrid.RootTable, true);
            //    }
            //}
            //catch (Exception exp)
            //{
            //    MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
            //    ExceptionHandler.RecordException(exp);
            //}
        }

        private void ShowAboutForm()  //Top function
        {
            try
            {
                AboutForm aboutForm = new AboutForm();
                aboutForm.ShowDialog();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
            }
        }

        private void ShowChangeDatabaseForm()
        {
            try
            {
                DatabaseConnectionSettingsForm form = new DatabaseConnectionSettingsForm();
                if (form.ShowDialog() == DialogResult.OK)
                {

                }

            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
            }
        }

        private void SetControlsForRole(TourunDataSet.RoleRow roleRow)
        {
            ////Καταργούμε τελείως
            //this.incomeNavigationPanel.Closed = true;
            //this.expensesNavigationPanel.Closed = true;

            //if (rolesRow.Name == "User1")
            //{
            //    this.rentCarCodesNavigationPanel.Closed = true;
            //    this.commissionsNavigationPanel.Closed = true;
            //    this.vehicleMaintenanceNavigationPanel.Closed = true;
            //    this.vehiclesNavigationPanel.Closed = true;
            //    this.newVehicleCommand.Visible = false;
            //    this.salariesNavigationPanel.Closed = true;
            //    this.notesNavigationPanel.Closed = true;

            //    if (this.tourReservationsGrid.RootTable.Columns.Contains("TotalCost"))
            //    {
            //        this.tourReservationsGrid.RootTable.Columns["TotalCost"].Visible = false;
            //    }
            //    if (this.apartmentReservationsGrid.RootTable.Columns.Contains("TotalCost"))
            //    {
            //        this.apartmentReservationsGrid.RootTable.Columns["TotalCost"].Visible = false;
            //    }
            //    //Reports
            //    this.dailyVehicleReservationsReportGrpBox.Visible = false;
            //    this.economicReportsGroupBox.Visible = false;
            //}
            //else if (rolesRow.Name == "User2")
            //{
            //    this.toursNavigationPanel.Closed = true;
            //    this.apartmentsNavigationPanel.Closed = true;
            //    this.newApartmentReservationCommand.Visible = false;
            //    this.newRoomReservationCommand.Visible = false;
            //    this.accommodationCodesNavigationPanel.Closed = true;
            //    this.commissionsNavigationPanel.Closed = true;
            //    this.vehiclesNavigationPanel.Closed = false;
            //    this.salariesNavigationPanel.Closed = true;
            //    this.notesNavigationPanel.Closed = true;
            //    if (this.vehicleReservationsGrid.RootTable.Columns.Contains("RentalCost"))
            //    {
            //        this.vehicleReservationsGrid.RootTable.Columns["RentalCost"].Visible = false;
            //    }
            //    if (this.vehicleReservationsGrid.RootTable.Columns.Contains("TotalCost"))
            //    {
            //        this.vehicleReservationsGrid.RootTable.Columns["TotalCost"].Visible = false;
            //    }
            //    //Reports
            //    this.dailyVehicleReservationsReportGrpBox.Visible = false;
            //    this.economicReportsGroupBox.Visible = false;
            //}

            return;

        }

        private void ResetGrids()  //Top function
        {
            try
            {
                this.toursGrid.ResetComponentSettings();
                this.coordinatorsGrid.ResetComponentSettings();
                this.attractionsGrid.ResetComponentSettings();
                this.accommodationsGrid.ResetComponentSettings();
                this.vehiclesGrid.ResetComponentSettings();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
            }
        }

        private void ExportToExcel()  //Top function
        {
            try
            {
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
                saveFileDialog.Title = Properties.Resources.SaveExcelText;
                saveFileDialog.Filter = "Excel Files | *.xls";

                if (!this.toursPanel.Closed)
                {
                    saveFileDialog.FileName = this.toursGrid.RootTable.Caption;

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        using (FileStream fs = new FileStream(saveFileDialog.FileName, FileMode.Create, FileAccess.Write, FileShare.ReadWrite))
                        {
                            this.gridExporter.GridEX = this.toursGrid;
                            this.gridExporter.Export(fs);
                        }
                    }
                }
                else if (!this.attractionsPanel.Closed)
                {
                    saveFileDialog.FileName = this.attractionsGrid.RootTable.Caption;

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        using (FileStream fs = new FileStream(saveFileDialog.FileName, FileMode.Create, FileAccess.Write, FileShare.ReadWrite))
                        {
                            this.gridExporter.GridEX = this.attractionsGrid;
                            this.gridExporter.Export(fs);
                        }
                    }
                }
                else if (!this.coordinatorsPanel.Closed)
                {
                    saveFileDialog.FileName = this.coordinatorsGrid.RootTable.Caption;

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        using (FileStream fs = new FileStream(saveFileDialog.FileName, FileMode.Create, FileAccess.Write, FileShare.ReadWrite))
                        {
                            this.gridExporter.GridEX = this.coordinatorsGrid;
                            this.gridExporter.Export(fs);
                        }
                    }
                }
                else if (!this.accommodationsPanel.Closed)
                {
                    saveFileDialog.FileName = this.accommodationsGrid.RootTable.Caption;

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        using (FileStream fs = new FileStream(saveFileDialog.FileName, FileMode.Create, FileAccess.Write, FileShare.ReadWrite))
                        {
                            this.gridExporter.GridEX = this.accommodationsGrid;
                            this.gridExporter.Export(fs);
                        }
                    }
                }
                else if (!this.vehiclesPanel.Closed)
                {
                    saveFileDialog.FileName = this.vehiclesGrid.RootTable.Caption;

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        using (FileStream fs = new FileStream(saveFileDialog.FileName, FileMode.Create, FileAccess.Write, FileShare.ReadWrite))
                        {
                            this.gridExporter.GridEX = this.vehiclesGrid;
                            this.gridExporter.Export(fs);
                        }
                    }
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void ShowGridReport()  //Top function
        {
            try
            {
                //Αν ο εκτυπωτής είναι έγκυρος
                if (Properties.Settings.Default.DataGridReportsPageSettings.PrinterSettings.IsValid)
                {
                    if (!this.toursPanel.Closed)
                    {
                        this.gridEXPrintDocument.DocumentName = this.toursGrid.RootTable.Caption;
                        this.gridEXPrintDocument.GridEX = this.toursGrid;
                    }
                    else if (!this.coordinatorsPanel.Closed)
                    {
                        this.gridEXPrintDocument.DocumentName = this.coordinatorsGrid.RootTable.Caption;
                        this.gridEXPrintDocument.GridEX = this.coordinatorsGrid;
                    }
                    else if (!this.attractionsPanel.Closed)
                    {
                        this.gridEXPrintDocument.DocumentName = this.attractionsGrid.RootTable.Caption;
                        this.gridEXPrintDocument.GridEX = this.attractionsGrid;
                    }
                    else if (!this.accommodationsPanel.Closed)
                    {
                        this.gridEXPrintDocument.DocumentName = this.accommodationsGrid.RootTable.Caption;
                        this.gridEXPrintDocument.GridEX = this.accommodationsGrid;
                    }
                    else if (!this.vehiclesPanel.Closed)
                    {
                        this.gridEXPrintDocument.DocumentName = this.vehiclesGrid.RootTable.Caption;
                        this.gridEXPrintDocument.GridEX = this.vehiclesGrid;
                    }

                    Reporting.Reports.ShowDataGridReportForm(this.gridEXPrintDocument);
                }
                else
                {
                    MessageBox.Show(Properties.Resources.InvalidPrinterError, Globals.Application.AssemblyTitle);
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = System.Windows.Forms.Cursors.Default;
            }
        }

        private void PrepareDataGridReport()  //Non Top function
        {
            try
            {
                if (!this.toursPanel.Closed)
                {
                    this.gridEXPrintDocument.DocumentName = this.toursGrid.RootTable.Caption;
                    this.gridEXPrintDocument.GridEX = this.toursGrid;
                }
                else if (!this.coordinatorsPanel.Closed)
                {
                    this.gridEXPrintDocument.DocumentName = this.coordinatorsGrid.RootTable.Caption;
                    this.gridEXPrintDocument.GridEX = this.coordinatorsGrid;
                }
                else if (!this.attractionsPanel.Closed)
                {
                    this.gridEXPrintDocument.DocumentName = this.attractionsGrid.RootTable.Caption;
                    this.gridEXPrintDocument.GridEX = this.attractionsGrid;
                }
                else if (!this.accommodationsPanel.Closed)
                {
                    this.gridEXPrintDocument.DocumentName = this.accommodationsGrid.RootTable.Caption;
                    this.gridEXPrintDocument.GridEX = this.accommodationsGrid;
                }
                else if (!this.vehiclesPanel.Closed)
                {
                    this.gridEXPrintDocument.DocumentName = this.vehiclesGrid.RootTable.Caption;
                    this.gridEXPrintDocument.GridEX = this.vehiclesGrid;
                }

                this.gridEXPrintDocument.TranslateSystemColors = false;
                this.gridEXPrintDocument.DefaultPageSettings = Properties.Settings.Default.DataGridReportsPageSettings;
                this.gridEXPrintDocument.PrinterSettings = Properties.Settings.Default.DataGridReportsPageSettings.PrinterSettings;
            }
            catch (Exception exp)
            {
                ExceptionHandler.RecordException(exp);
                throw new Exception(Properties.Resources.GeneralExceptionMessage);
            }
        }

        private void grid_DeletingRecords(object sender, CancelEventArgs e)
        {
            if (MessageBox.Show(Properties.Resources.DeleteDataConfirmationMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
            {
                e.Cancel = true;
            }
        }

        private void grid_RowDoubleClick(object sender, RowActionEventArgs e)
        {
            GridEX grid = (GridEX)sender;
            ////Αν για τα Rooms και Vehicles δεν έχει δικαιώματα
            //if ((grid == this.toursGrid || grid == this.attractionsGrid || grid == this.accommodationsGrid) && Globals.Application.CurrentUserRole.Name != "Admin")
            //{

            //}
            //else
            {
                this.EditData(e.Row);
            }
        }

        private void grid_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                this.UpdateCommands();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void grid_CurrentLayoutChanging(object sender, CancelEventArgs e)
        {
            try
            {
                GridEX grid = (GridEX)sender;
                if (grid.CurrentLayout != null)
                {
                    grid.CurrentLayout.Update();

                }
                grid.Refetch();  //Ξαναδιαβάζει τα δεδομένα γιατί αλλιώς κάποια grids εμφανίζονται κενά χωρίς δεδομένα.
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void grid_CurrentLayoutChanged(object sender, EventArgs e)
        {
            try
            {
                //this.SetGridsFontSize();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void grid_RowCountChanged(object sender, EventArgs e)
        {
            this.UpdateCommands();
            this.UpdateStatusBar();
        }

        private void grid_KeyDown(object sender, KeyEventArgs e)
        {
            if (!e.Handled)
            {
                if (e.Control && e.KeyCode == Keys.A)
                {
                    GridEX grid = ((GridEX)sender);
                    foreach (GridEXRow gridExRow in grid.GetDataRows())
                    {
                        grid.SelectedItems.Add(gridExRow.Position);
                    }
                }
            }
        }

        private void grid_MouseDown(object sender, MouseEventArgs e)
        {
            this.UpdateCommands();
        }

        private void grid_RecordsDeleted(object sender, EventArgs e)
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;
                Data.SaveAllData();
            }
            catch (CaughtedException exp)
            {
                MessageBox.Show(exp.Message, Properties.Resources.GeneralExceptionMessage, MessageBoxButtons.OK);
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        #region  Tours
        private void NewTour()
        {
            try
            {
                TourForm form = new TourForm(null);
                form.ShowDialog();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void EditTour(Int64 tourId)
        {
            try
            {
                TourForm form = new TourForm(tourId);
                form.ShowDialog(this);
                form.Dispose();

                this.UpdateCommands();
                this.UpdateStatusBar();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void DeleteTour()
        {
            try
            {
                if (this.toursGrid.SelectedItems.Count > 0)
                {
                    DataRowView rowView = this.toursBindingSource.Current as DataRowView;
                    TourunDataSet.TourRow row = rowView.Row as TourunDataSet.TourRow;

                    if (MessageBox.Show(Properties.Resources.DeleteDataConfirmationMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) == DialogResult.Yes)
                    {
                        this.toursGrid.SelectedItems[0].GetRow().Delete();
                        Data.SaveAllData();

                        this.UpdateCommands();
                        this.UpdateStatusBar();
                    }
                }
            }
            catch (Exception exp)
            {
                Data.DataSet.RejectChanges();

                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }
        #endregion

        #region  Coordinators
        private void NewCoordinator()
        {
            try
            {
                CoordinatorForm form = new CoordinatorForm(null);
                form.ShowDialog();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void EditCoordinator(Int64 coordinatorId)
        {
            try
            {
                CoordinatorForm form = new CoordinatorForm(coordinatorId);
                form.ShowDialog(this);
                form.Dispose();

                this.UpdateCommands();
                this.UpdateStatusBar();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void DeleteCoordinator()
        {
            try
            {
                if (this.toursGrid.SelectedItems.Count > 0)
                {
                    DataRowView rowView = this.toursBindingSource.Current as DataRowView;
                    TourunDataSet.CoordinatorRow row = rowView.Row as TourunDataSet.CoordinatorRow;

                    if (MessageBox.Show(Properties.Resources.DeleteDataConfirmationMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) == DialogResult.Yes)
                    {
                        this.toursGrid.SelectedItems[0].GetRow().Delete();
                        Data.SaveAllData();

                        this.UpdateCommands();
                        this.UpdateStatusBar();
                    }
                }
            }
            catch (Exception exp)
            {
                Data.DataSet.RejectChanges();

                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }
        #endregion

        #region  Attractions
        private void NewAttraction()
        {
            try
            {
                AttractionForm form = new AttractionForm(null);
                form.AllowSave = true;
                if (form.ShowDialog() == DialogResult.OK)
                {
                    this.RefreshData();
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void EditAttraction(Int64 attractionId)
        {
            try
            {
                AttractionForm form = new AttractionForm(attractionId);
                form.AllowSave = true;
                if (form.ShowDialog(this) == DialogResult.OK)
                {
                    this.RefreshData();
                }
                form.Dispose();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void DeleteAttraction()
        {
            try
            {
                if (this.accommodationsGrid.SelectedItems.Count > 0)
                {
                    DataRowView rowView = this.accommodationsBindingSource.Current as DataRowView;
                    TourunDataSet.AttractionRow row = rowView.Row as TourunDataSet.AttractionRow;

                    if (MessageBox.Show(Properties.Resources.DeleteDataConfirmationMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) == DialogResult.Yes)
                    {
                        this.accommodationsGrid.SelectedItems[0].GetRow().Delete();
                        Data.SaveAllData();

                        this.UpdateCommands();
                        this.UpdateStatusBar();
                    }
                }
            }
            catch (Exception exp)
            {
                Data.DataSet.RejectChanges();

                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }
        #endregion

        #region  Vehicles
        private void NewVehicle()
        {
            try
            {
                VehicleForm form = new VehicleForm(null);
                form.AllowSave = true;
                if (form.ShowDialog() == DialogResult.OK)
                {
                    this.RefreshData();
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void EditVehicle(Int64 vehicleId)
        {
            try
            {
                VehicleForm form = new VehicleForm(vehicleId);
                form.AllowSave = true;
                if (form.ShowDialog(this) == DialogResult.OK)
                {
                    this.RefreshData();
                }
                form.Dispose();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void DeleteVehicle()
        {
            try
            {
                if (this.vehiclesGrid.SelectedItems.Count > 0)
                {
                    DataRowView rowView = this.vehiclesBindingSource.Current as DataRowView;
                    TourunDataSet.VehicleRow row = rowView.Row as TourunDataSet.VehicleRow;

                    if (MessageBox.Show(Properties.Resources.DeleteDataConfirmationMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) == DialogResult.Yes)
                    {
                        this.vehiclesGrid.SelectedItems[0].GetRow().Delete();
                        Data.SaveAllData();

                        this.UpdateCommands();
                        this.UpdateStatusBar();
                    }
                }
            }
            catch (Exception exp)
            {
                Data.DataSet.RejectChanges();

                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }
        #endregion

        
    }
}
