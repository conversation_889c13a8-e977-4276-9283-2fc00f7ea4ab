﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.Diagnostics;
using System.Reflection;


namespace Tourun.UI
{
    partial class AboutForm : Form
    {
        private InputLanguage inputLanguage;

        public AboutForm()
        {
            InitializeComponent();

            //  Initialize the AboutBox to display the product information from the assembly information.
            //  Change assembly information settings for your application through either:
            //  - Project->Properties->Application->Assembly Information
            //  - AssemblyInfo.cs
            this.labelProductTitle.Text = AssemblyTitle;
            this.labelVersion.Text = String.Format(Properties.Resources.VersionString + " {0}", Globals.Application.FullAssemblyVersion);
            this.labelCopyright.Text = AssemblyCopyright;
            this.labelCompanyName.Text = AssemblyCompany;
            this.emailLnkLbl.Text = "Email: " + Properties.Resources.Email;
            this.inputLanguage = InputLanguage.CurrentInputLanguage;
        }

        #region Assembly Attribute Accessors

        public string AssemblyTitle
        {
            get
            {
                // Get all Title attributes on this assembly
                object[] attributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyTitleAttribute), false);
                // If there is at least one Title attribute
                if (attributes.Length > 0)
                {
                    // Select the first one
                    AssemblyTitleAttribute titleAttribute = (AssemblyTitleAttribute)attributes[0];
                    // If it is not an empty string, return it
                    if (titleAttribute.Title != "")
                        return titleAttribute.Title;
                }
                // If there was no Title attribute, or if the Title attribute was the empty string, return the .exe name
                return System.IO.Path.GetFileNameWithoutExtension(Assembly.GetExecutingAssembly().CodeBase);
            }
        }

        public string AssemblyVersion
        {
            get
            {
                return Assembly.GetExecutingAssembly().GetName().Version.ToString();
            }
        }

        public string AssemblyDescription
        {
            get
            {
                // Get all Description attributes on this assembly
                object[] attributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyDescriptionAttribute), false);
                // If there aren't any Description attributes, return an empty string
                if (attributes.Length == 0)
                    return "";
                // If there is a Description attribute, return its value
                return ((AssemblyDescriptionAttribute)attributes[0]).Description;
            }
        }

        public string AssemblyProduct
        {
            get
            {
                // Get all Product attributes on this assembly
                object[] attributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyProductAttribute), false);
                // If there aren't any Product attributes, return an empty string
                if (attributes.Length == 0)
                    return "";
                // If there is a Product attribute, return its value
                return ((AssemblyProductAttribute)attributes[0]).Product;
            }
        }

        public string AssemblyCopyright
        {
            get
            {
                // Get all Copyright attributes on this assembly
                object[] attributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyCopyrightAttribute), false);
                // If there aren't any Copyright attributes, return an empty string
                if (attributes.Length == 0)
                    return "";
                // If there is a Copyright attribute, return its value
                return ((AssemblyCopyrightAttribute)attributes[0]).Copyright;
            }
        }

        public string AssemblyCompany
        {
            get
            {
                // Get all Company attributes on this assembly
                object[] attributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyCompanyAttribute), false);
                // If there aren't any Company attributes, return an empty string
                if (attributes.Length == 0)
                    return "";
                // If there is a Company attribute, return its value
                return ((AssemblyCompanyAttribute)attributes[0]).Company;
            }
        }
        #endregion

        private void emailLnkLbl_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            this.SendMail();
        }

        private void SendMail()  //Top function
        {
            try
            {
                Process.Start("mailto: " + Properties.Resources.Email);
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void AboutForm_Load(object sender, EventArgs e)  //Top function
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;


                ////Licenser.ProductName = Globals.Application.AssemblyProduct + Globals.Application.AssemblyVersion;
                ////Licenser.ProductKey = Globals.Application.AssemblyProduct + Globals.Application.AssemblyVersion;
                //Licenser.InitLicense(Globals.Application.AssemblyProduct, Globals.Application.AssemblyVersion, 30, 200, "333");  //Ξανακάνουμε initialize το lics
                ////Licenser.InitLicense(new DateTime(2010, 8, 31));  //Ξανακάνουμε initialize το lics
                //this.SetLicenseData();
                ////Ενεργοποιεί το πλήκτρο για Μεταφορά του license μόνο αν η εφαρμογή είναι registered/
                //this.transferBtn.Enabled = Licenser.Status == LicenseStatus.Registered;
                ////Απενεργοποιεί το πλήκτρο για Επέκταση του license μόνο αν η εφαρμογή είναι registered/
                //this.extendBtn.Enabled = !(Licenser.Status == LicenseStatus.Registered);

            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void SetLicenseData()  //Non top function
        {
            try
            {
                //switch (Licenser.Status)
                //{
                //    case LicenseStatus.Trial:  //Αν είναι τύπου trial για ημέρες
                //        {
                //            //Δεν εμφανίζουμε το μήνυμα στον χρήστη παρά μόνο στις τελευταίες 30 ημέρες.
                //            /*if (Licenser.CountLeft <= 10)*/
                //            {
                //                this.registeredMessageLbl.Text = "Η εφαρμογή δεν έχει ενεργοποιηθεί. Απομένουν " + Licenser.DaysLeft.ToString() + " ημέρες χρήσης της εφαρμογής.";
                //            }
                //            break;
                //        }
                //    case LicenseStatus.Registered:  //Αν είναι registered
                //        {
                //            this.registeredMessageLbl.Text = "Η εφαρμογή έχει ενεργοποιηθεί.";
                //            break;
                //        }
                //    case LicenseStatus.Expired: //Αν το license (trial) έχει λήξει
                //        {
                //            this.registeredMessageLbl.Text = "Η άδεια χρήσης της εφαρμογή έληξε. Παρακαλώ κάντε ενεργοποίηση.";
                //            break;
                //        }
                //}
                //this.registerBtn.Enabled = Licenser.Status != LicenseStatus.Registered;
            }
            catch (Exception exp)
            {
                ExceptionHandler.RecordException(exp);
                throw new Exception(Properties.Resources.GeneralExceptionMessage);
            }
        }

        private void registerBtn_Click(object sender, EventArgs e)
        {
            this.RegisterApplication();
        }

        private void RegisterApplication()  //Top function
        {
            try
            {
                //RegisterAppForm regForm = new RegisterAppForm();
                //regForm.ShowDialog();
                //regForm.Dispose();
                //this.SetLicenseData();
                ////Ενεργοποιεί το πλήκτρο για Μεταφορά του license μόνο αν η εφαρμογή είναι registered/
                //this.transferBtn.Enabled = Licenser.Status == LicenseStatus.Registered;
                ////this.extendBtn.Enabled = !(Licenser.Status == LicenseStatus.Registered);
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void transferBtn_Click(object sender, EventArgs e)
        {
            this.TransferLicense();
        }

        private void TransferLicense()  //Top function
        {
            try
            {
                //TransferLicenseForm transferLicenseForm = new TransferLicenseForm();
                //transferLicenseForm.ShowDialog();
                //transferLicenseForm.Dispose();
                //this.SetLicenseData();
                ////Ενεργοποιεί το πλήκτρο για Μεταφορά του license μόνο αν η εφαρμογή είναι registered/
                //this.transferBtn.Enabled = Licenser.Status == LicenseStatus.Registered;
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void AboutForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            InputLanguage.CurrentInputLanguage = this.inputLanguage;  //θέτει την αρχική γλώσσα σαν γλώσσα του προγράμματος
        }

        private void extendBtn_Click(object sender, EventArgs e)  //Top function
        {
            try
            {
                //ExtendLicenseForm form = new ExtendLicenseForm();
                //form.ShowDialog();
                //this.SetLicenseData();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void okButton_Click(object sender, EventArgs e)
        {

        }
    }
}