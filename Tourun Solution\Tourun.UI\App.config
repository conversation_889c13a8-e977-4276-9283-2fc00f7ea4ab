﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Janus.Windows.Common.JanusApplicationSettings.TourAccommodationsGrid" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
            <section name="Janus.Windows.Common.JanusApplicationSettings.TourAttractionsGrid" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
            <section name="Janus.Windows.Common.JanusApplicationSettings.VehiclesGrid" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
            <section name="Janus.Windows.Common.JanusApplicationSettings.CoordinatorsGrid" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
            <section name="Janus.Windows.Common.JanusApplicationSettings.TourVehiclesGrid" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
            <section name="Janus.Windows.Common.JanusApplicationSettings.AttractionsGrid" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
            <section name="Janus.Windows.Common.JanusApplicationSettings.AccommodationsGrid" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
            <section name="Janus.Windows.Common.JanusApplicationSettings.ToursGrid" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
            <section name="Janus.Windows.Common.JanusApplicationSettings.ContactsGrid" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <connectionStrings>
        <add name="Tourun.UI.Properties.Settings.TourunConnectionString"
            connectionString="Data Source=MAIN\SQLEXPRESS;Initial Catalog=Tourun;Integrated Security=True;Encrypt=False"
            providerName="System.Data.SqlClient" />
        <add name="Tourun.UI.Properties.Settings.TourunConnectionString1"
            connectionString="Data Source=MAIN\SQLEXPRESS;Initial Catalog=Tourun;Integrated Security=True;Encrypt=False"
            providerName="Microsoft.Data.SqlClient" />
    </connectionStrings>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8.1" />
    </startup>
    <userSettings>
        <Janus.Windows.Common.JanusApplicationSettings.TourAccommodationsGrid>
            <setting name="LayoutSettings" serializeAs="Xml">
                <value>
                    <base64Binary>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</base64Binary>
                </value>
            </setting>
        </Janus.Windows.Common.JanusApplicationSettings.TourAccommodationsGrid>
        <Janus.Windows.Common.JanusApplicationSettings.TourAttractionsGrid>
            <setting name="LayoutSettings" serializeAs="Xml">
                <value>
                    <base64Binary>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</base64Binary>
                </value>
            </setting>
        </Janus.Windows.Common.JanusApplicationSettings.TourAttractionsGrid>
        <Janus.Windows.Common.JanusApplicationSettings.VehiclesGrid>
            <setting name="LayoutSettings" serializeAs="Xml">
                <value>
                    <base64Binary>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</base64Binary>
                </value>
            </setting>
        </Janus.Windows.Common.JanusApplicationSettings.VehiclesGrid>
        <Janus.Windows.Common.JanusApplicationSettings.CoordinatorsGrid>
            <setting name="LayoutSettings" serializeAs="Xml">
                <value>
                    <base64Binary>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</base64Binary>
                </value>
            </setting>
        </Janus.Windows.Common.JanusApplicationSettings.CoordinatorsGrid>
        <Janus.Windows.Common.JanusApplicationSettings.TourVehiclesGrid>
            <setting name="LayoutSettings" serializeAs="Xml">
                <value>
                    <base64Binary>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</base64Binary>
                </value>
            </setting>
        </Janus.Windows.Common.JanusApplicationSettings.TourVehiclesGrid>
        <Janus.Windows.Common.JanusApplicationSettings.AttractionsGrid>
            <setting name="LayoutSettings" serializeAs="Xml">
                <value>
                    <base64Binary>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</base64Binary>
                </value>
            </setting>
        </Janus.Windows.Common.JanusApplicationSettings.AttractionsGrid>
        <Janus.Windows.Common.JanusApplicationSettings.AccommodationsGrid>
            <setting name="LayoutSettings" serializeAs="Xml">
                <value>
                    <base64Binary>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</base64Binary>
                </value>
            </setting>
        </Janus.Windows.Common.JanusApplicationSettings.AccommodationsGrid>
        <Janus.Windows.Common.JanusApplicationSettings.ToursGrid>
            <setting name="LayoutSettings" serializeAs="Xml">
                <value>
                    <base64Binary>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</base64Binary>
                </value>
            </setting>
        </Janus.Windows.Common.JanusApplicationSettings.ToursGrid>
        <Janus.Windows.Common.JanusApplicationSettings.ContactsGrid>
            <setting name="LayoutSettings" serializeAs="Xml">
                <value>
                    <base64Binary>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</base64Binary>
                </value>
            </setting>
        </Janus.Windows.Common.JanusApplicationSettings.ContactsGrid>
    </userSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Abstractions" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-7.7.1.0" newVersion="7.7.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.1" newVersion="8.0.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.2" newVersion="8.0.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.3" newVersion="8.0.0.3" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.5" newVersion="8.0.0.5" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Azure.Core" publicKeyToken="92742159e12e44c8" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.47.1.0" newVersion="1.47.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory.Data" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.1" newVersion="8.0.0.1" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>