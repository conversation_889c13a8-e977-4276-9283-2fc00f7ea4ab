﻿using System;
using System.Reflection;
using System.Windows.Forms;
using System.Threading;
using System.IO;

namespace Tourun.UI.Globals
{
    public static class Application
    {
        //private static SplashForm splashForm = null;
        private static Thread splashThread = null;
        private static bool isRefreshingData = false;
        private static int reducedDataMode = 1;  //Καθοριζει ποιο από τα δυο modes (στo AppSettingsForm) έχει επιλέξει ο χρήστης. 1=με βάση τη τρέχουσα ημερομηνία, 2=με βάση τις επιλεγμένες ημερομηνίες (στo AppSettingsForm)
        private static DateTime reducedDataStartDate;
        public static bool TaskEditing = false;  //Καθορίζει αν κάποιο Task στο Schedule είναι σε κατάσταση διόρθωσης δηλαδή editing.
        private static string userName = "";
        private static string password = "";
        private static TourunDataSet.UserRow usersRow = null;
        private static TourunDataSet.RoleRow rolesRow = null;

        public static string UserName
        {
            get
            {
                return userName;
            }
            set
            {
                userName = value;
            }
        }

        public static string Password
        {
            get
            {
                return password;
            }
            set
            {
                password = value;
            }
        }

        public static TourunDataSet.UserRow CurrentUser
        {
            get
            {
                return usersRow;
            }
            set
            {
                usersRow = value;
            }
        }

        public static TourunDataSet.RoleRow CurrentUserRole
        {
            get
            {
                return rolesRow;
            }
            set
            {
                rolesRow = value;
            }
        }

        public static bool IsRefreshingData
        {
            get
            {
                return isRefreshingData;
            }
            set
            {
                isRefreshingData = value;
            }
        }

        public static string AssemblyTitle
        {
            get
            {
                // Get all Title attributes on this assembly
                object[] attributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyTitleAttribute), false);
                // If there is at least one Title attribute
                if (attributes.Length > 0)
                {
                    // Select the first one
                    AssemblyTitleAttribute titleAttribute = (AssemblyTitleAttribute)attributes[0];
                    // If it is not an empty string, return it
                    if (titleAttribute.Title != "")
                        return titleAttribute.Title;
                }
                // If there was no Title attribute, or if the Title attribute was the empty string, return the .exe name
                return System.IO.Path.GetFileNameWithoutExtension(Assembly.GetExecutingAssembly().CodeBase);
            }
        }

        /// <summary>
        /// Returns only the first three parts of version. [Major].[Minor].[Build]
        /// </summary>
        public static string AssemblyVersion
        {
            get
            {
                Version version = Assembly.GetExecutingAssembly().GetName().Version;
                return version.Major.ToString() + "." + version.Minor.ToString() + "." + version.Build.ToString();
            }
        }

        public static string FullAssemblyVersion
        {
            get
            {
                return Assembly.GetExecutingAssembly().GetName().Version.ToString();
            }
        }

        public static string AssemblyDescription
        {
            get
            {
                // Get all Description attributes on this assembly
                object[] attributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyDescriptionAttribute), false);
                // If there aren't any Description attributes, return an empty string
                if (attributes.Length == 0)
                    return "";
                // If there is a Description attribute, return its value
                return ((AssemblyDescriptionAttribute)attributes[0]).Description;
            }
        }

        public static string AssemblyProduct
        {
            get
            {
                // Get all Product attributes on this assembly
                object[] attributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyProductAttribute), false);
                // If there aren't any Product attributes, return an empty string
                if (attributes.Length == 0)
                    return "";
                // If there is a Product attribute, return its value
                return ((AssemblyProductAttribute)attributes[0]).Product;
            }
        }

        public static string AssemblyCopyright
        {
            get
            {
                // Get all Copyright attributes on this assembly
                object[] attributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyCopyrightAttribute), false);
                // If there aren't any Copyright attributes, return an empty string
                if (attributes.Length == 0)
                    return "";
                // If there is a Copyright attribute, return its value
                return ((AssemblyCopyrightAttribute)attributes[0]).Copyright;
            }
        }

        public static string AssemblyCompany
        {
            get
            {
                // Get all Company attributes on this assembly
                object[] attributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyCompanyAttribute), false);
                // If there aren't any Company attributes, return an empty string
                if (attributes.Length == 0)
                    return "";
                // If there is a Company attribute, return its value
                return ((AssemblyCompanyAttribute)attributes[0]).Company;
            }
        }

        public static string DataPath
        {
            get
            {
                return System.Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData) + @"\" + AssemblyCompany + @"\" + AssemblyProduct + @"\" + AssemblyVersion + @"\";
            }
        }

        public static string ServerName
        {
            get
            {
                string text = File.ReadAllText(Globals.Application.DataPath + "ServerName.txt");
                return text;
            }
            set
            {
                File.WriteAllText(Globals.Application.DataPath + "ServerName.txt", value);
            }
        }

        public static string ServerPassword
        {
            get
            {
                string text = File.ReadAllText(Globals.Application.DataPath + "ServerPwd.txt");
                return text;
            }
            set
            {
                File.WriteAllText(Globals.Application.DataPath + "ServerPwd.txt", value);
            }
        }

        public static int ReducedDataMode
        {
            get
            {
                return reducedDataMode;
            }
            set
            {
                reducedDataMode = value;
            }
        }

        public static DateTime ReducedDataStartDate
        {
            get { return reducedDataStartDate; }
            set { reducedDataStartDate = value; }
        }

        private static DateTime reducedDataEndDate;

        public static DateTime ReducedDataEndDate
        {
            get { return reducedDataEndDate; }
            set { reducedDataEndDate = value; }
        }

        //        public static void ShowSplash()
        //        {
        //            if (splashForm == null)
        //            {
        //                splashThread = new Thread(new ThreadStart(ShowSplashThreadFunction));
        //                splashThread.IsBackground = true;
        //                splashThread.SetApartmentState(ApartmentState.STA);
        //#if DEBUG
        //                //Μην δείξεις το SplashForm
        //#else
        //                splashThread.Start();
        //#endif
        //            }
        //        }

        //public static void ShowSplashThreadFunction()
        //{
        //    splashForm = new SplashForm();
        //    splashForm.ShowDialog();
        //}

        public static void CloseSplash()
        {
            try
            {
                splashThread.Abort();
            }
            catch (Exception)
            {
            }
        }

        ///// <summary>
        ///// Checks the settings if the printer for the reports is set and if so checks if this printer is installed.
        ///// </summary>
        ///// <returns>A value indicating if the printer for the reports is valid.</returns>
        //public static bool IsReportsPrinterValid(string printer)
        //{
        //    //Ελέγχει αν ο εκτυπωτής για τα reports έχει ρυθμιστεί.
        //    if (printer == "")
        //    {
        //        MessageBox.Show(Properties.Resources.PrinterNotSetErrorMessage, Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Information);
        //        return false;
        //    }
        //    //Ελέγχει αν ο εκτυπωτής για τα reports είναι πράγματι εγκατεστημένος στον υπολογιστή.
        //    foreach (string installedPrinter in PrinterSettings.InstalledPrinters)
        //    {
        //        if (installedPrinter == printer)
        //        {
        //            return true;
        //        }
        //    }
        //    MessageBox.Show(Properties.Resources.InvalidPrinterErrorMessage, Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Information);
        //    return false;
        //}

        //public static void PreviewDocument(System.Drawing.Printing.PrintDocument document)  //Non top function
        //{
        //    try
        //    {
        //        if (document.PrinterSettings.IsValid)
        //        {
        //            printPreviewDialog = new PrintPreviewDialog();
        //            printPreviewDialog.Document = document;
        //            printPreviewDialog.ShowIcon = true;
        //            printPreviewDialog.MinimizeBox = true;
        //            printPreviewDialog.Icon = Properties.Resources.PreviewIcon;
        //            printPreviewDialog.Text = Properties.Resources.PreviewText;
        //            printPreviewDialog.WindowState = FormWindowState.Maximized;
        //            printPreviewDialog.ShowInTaskbar = true;
        //            printPreviewDialog.ShowDialog();
        //            printPreviewDialog.Activate();
        //        }
        //        else
        //        {
        //            MessageBox.Show(Properties.Resources.InvalidPrinterErrorMessage, Application.AssemblyTitle);
        //        }
        //    }
        //    catch (Exception exp)
        //    {
        //        ExceptionHandler.RecordException(exp);
        //        throw new Exception(Properties.Resources.GeneralExceptionMessage);
        //    }
        //}

        //private static void PrintPreviewKeyDown(object sender, KeyEventArgs e)
        //{
        //    if (e.KeyCode == Keys.Escape)
        //    {
        //        printPreviewDialog.Close();
        //    }
        //}

        //public static void PrintDocument(System.Drawing.Printing.PrintDocument document)  //Non top function
        //{
        //    try
        //    {
        //        if (document.PrinterSettings.IsValid)
        //        {
        //            document.Print();
        //        }
        //        else
        //        {
        //            MessageBox.Show(Properties.Resources.InvalidPrinterErrorMessage, Application.AssemblyTitle);
        //        }
        //    }
        //    catch (Exception exp)
        //    {
        //        throw exp;
        //    }
        //}

        //public static void PrintDocument(GridEXPrintDocument gridEXPrintDocument)  //Non top function
        //{
        //    try
        //    {
        //        if (gridEXPrintDocument.DefaultPageSettings.PrinterSettings.IsValid)
        //        {
        //            gridEXPrintDocument.Print();
        //        }
        //        else
        //        {
        //            MessageBox.Show(Properties.Resources.InvalidPrinterErrorMessage, Application.AssemblyTitle);
        //        }
        //    }
        //    catch (Exception exp)
        //    {
        //        throw exp;
        //    }
        //}

        public static void MakeControlReadOnly(Control control)
        {
            if (control.GetType() == typeof(TextBox))
            {
                ((TextBox)control).ReadOnly = true;
            }
            if (control.GetType() == typeof(Button))
            {
                ((Button)control).Enabled = false;
            }
            if (control.GetType() == typeof(CheckBox))
            {
                ((CheckBox)control).Enabled = false;
            }
            if (control.GetType() == typeof(NumericUpDown))
            {
                ((NumericUpDown)control).Enabled = false;
            }
            if (control.GetType() == typeof(RadioButton))
            {
                ((RadioButton)control).Enabled = true;
            }
            if (control.GetType() == typeof(Janus.Windows.CalendarCombo.CalendarCombo))
            {
                ((Janus.Windows.CalendarCombo.CalendarCombo)control).ReadOnly = true;
            }
            if (control.GetType() == typeof(Janus.Windows.EditControls.UIComboBox))
            {
                ((Janus.Windows.EditControls.UIComboBox)control).ReadOnly = true;
            }
            if (control.GetType() == typeof(Janus.Windows.GridEX.EditControls.NumericEditBox))
            {
                ((Janus.Windows.GridEX.EditControls.NumericEditBox)control).ReadOnly = true;
            }
            if (control.GetType() == typeof(Janus.Windows.GridEX.EditControls.IntegerUpDown))
            {
                ((Janus.Windows.GridEX.EditControls.IntegerUpDown)control).ReadOnly = true;
            }
            if (control.GetType() == typeof(Janus.Windows.GridEX.EditControls.MultiColumnCombo))
            {
                ((Janus.Windows.GridEX.EditControls.MultiColumnCombo)control).ReadOnly = true;
            }
            if (control.GetType() == typeof(Janus.Windows.GridEX.GridEX))
            {
                ((Janus.Windows.GridEX.GridEX)control).Enabled = false;
            }

            foreach (Control childControl in control.Controls)
            {
                MakeControlReadOnly(childControl);
            }
        }
    }
}
