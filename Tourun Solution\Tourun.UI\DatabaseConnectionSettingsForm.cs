﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.Sql;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace Tourun.UI
{
    public partial class DatabaseConnectionSettingsForm : Form
    {
        private bool databaseConnectionSettingsChanged = false;

        public bool DatabaseConnectionSettingsChanged
        {
            get
            {
                return this.databaseConnectionSettingsChanged;
            }
        }

        public DatabaseConnectionSettingsForm()
        {
            InitializeComponent();

            try
            {
                #region  Εμφανίζει τιμές στα controls
                //this.serverInstanceTxtBox.Text = Globals.Application.DataSource;
                this.serverNameCmbBox.Text = Globals.Application.ServerName;
                this.serverPwdTxtBox.Text = Globals.Application.ServerPassword;

                #endregion
            }
            catch (Exception exp)
            {
                ExceptionHandler.RecordException(exp);
                throw new Exception(Properties.Resources.GeneralExceptionMessage);
            }
        }

        private bool CheckInputData()  //Non top function
        {
            try
            {
                bool errorfound = false;

                #region  Κάνει τους ελέγχους και εμφανίζει τα μηνύματα
                //Αν ο χρήστης άφησε κενό το serverNameCmbBox
                if (this.serverNameCmbBox.Text == "")
                {
                    this.errorProvider.SetError(this.serverNameCmbBox, Properties.Resources.FieldRequiredMessage);
                    errorfound = true;
                }
                else
                {
                    this.errorProvider.SetError(this.serverNameCmbBox, "");
                }
                #endregion

                //Αφού έχει εξετάσει όλες τις περιπτώσεις λαθών βγάζει ενα μήνυμα και τελειώνει η συνάρτηση
                if (errorfound == true)  //Αν βρήκε λάθη
                {
                    MessageBox.Show(Properties.Resources.InputDataValidationErrorMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK);
                    return false;
                }
                else  //Αν δεν βρήκε λάθη τα οποία πρέπει να δείξει με το errorProvider.
                {
                    return true;
                }
            }
            catch (Exception exp)
            {
                ExceptionHandler.RecordException(exp);
                throw new Exception(Properties.Resources.GeneralExceptionMessage);
            }
        }

        private void okBtn_Click(object sender, EventArgs e)
        {
            try
            {
                //Αν ο χρήστης άφησε κενό το serverNameCmbBox
                if (this.CheckInputData() == true)
                {
                    Cursor.Current = Cursors.WaitCursor;

                    //Αν ο χρήστης άλλαξε τα connection settings.
                    if (this.serverNameCmbBox.Text != Globals.Application.ServerName || this.serverPwdTxtBox.Text != Globals.Application.ServerPassword)
                    {
                        //Αν τα νέα connection settings είναι σωστά.
                        if (Data.CheckConnectionValidity(this.serverNameCmbBox.Text, this.serverPwdTxtBox.Text))
                        {
                            Globals.Application.ServerName = this.serverNameCmbBox.Text;
                            //Globals.Application.InstanceName = this.serverInstanceTxtBox.Text;
                            Globals.Application.ServerPassword = this.serverPwdTxtBox.Text;

                            this.databaseConnectionSettingsChanged = true;
                            this.DialogResult = DialogResult.OK;
                        }
                        else
                        {
                            MessageBox.Show(Properties.Resources.DatabaseConnectivityErrorMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }
                    }
                   
                    this.Close();
                }
            }
            catch (DBConcurrencyException exp)
            {
                MessageBox.Show(Properties.Resources.DatabaseConcurrencyExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void findSqlServerInstancesBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;

                //Διαβάζει τα Sql Server instances που βρίσκονται στο δίκτυο.
                SqlDataSourceEnumerator instance = SqlDataSourceEnumerator.Instance;
                System.Data.DataTable table = instance.GetDataSources();

                //Δίνει στο sqlServerInstancesCmbBox τα παραπάνω δεδομένα
                this.serverNameCmbBox.Items.Clear();
                foreach (DataRow row in table.Rows)
                {
                    //Αν το όνομα του server δεν περιέχεται ήδη στο drop down
                    if (this.serverNameCmbBox.Items.Contains(row["ServerName"].ToString()) == false)
                    {
                        this.serverNameCmbBox.Items.Add(row["ServerName"].ToString(), row["ServerName"].ToString());
                    }
                }

                this.serverNameCmbBox.DroppedDown = true;
            }
            catch (Exception exp)
            {
                ExceptionHandler.RecordException(exp);
                throw new Exception(Properties.Resources.GeneralExceptionMessage);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }
    }
}
