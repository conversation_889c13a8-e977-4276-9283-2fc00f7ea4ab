﻿using Microsoft.Data.SqlClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Tourun.UI;
using Tourun.UI.Properties;
using static Tourun.UI.Data.Tour;
using static Tourun.UI.TourunDataSet;

namespace Tourun.UI
{
    public class Data
    {
        private static TourunDataSet ds;
        private static SqlConnection connection;
        private static Tourun.UI.TourunDataSetTableAdapters.TableAdapterManager taManager;
        private static Tourun.UI.TourunDataSetTableAdapters.TourTableAdapter toursTA;
        private static Tourun.UI.TourunDataSetTableAdapters.AccommodationTableAdapter accommodationsTA;
        private static Tourun.UI.TourunDataSetTableAdapters.CoordinatorTableAdapter coordinatorsTA;
        private static Tourun.UI.TourunDataSetTableAdapters.VehicleTableAdapter vehiclesTA;
        private static Tourun.UI.TourunDataSetTableAdapters.AttractionTableAdapter attractionsTA;
        private static Tourun.UI.TourunDataSetTableAdapters.RoleTableAdapter rolesTA;
        private static Tourun.UI.TourunDataSetTableAdapters.UserTableAdapter usersTA;
        private static Tourun.UI.TourunDataSetTableAdapters.TourTableAdapter tourTA;
        private static Tourun.UI.TourunDataSetTableAdapters.TourAccommodationTableAdapter tourAccommodationsTA;
        private static Tourun.UI.TourunDataSetTableAdapters.TourAttractionTableAdapter tourAttractionsTA;
        private static Tourun.UI.TourunDataSetTableAdapters.TourVehicleTableAdapter tourVehiclesTA;

        public static string DatabaseFilePath
        {
            get
            {
                string path;
                string executablePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);

                //Αν υπάρχει το τοπικό αρχείο στο Program Files
                if (File.Exists(executablePath + @"\DatabaseFilePath.txt"))
                {
                    path = File.ReadAllText(executablePath + @"\DatabaseFilePath.txt");
                }
                else  //αλλιώς διαβάζει από το Programs Data
                {
                    path = File.ReadAllText(Tourun.UI.Globals.Application.DataPath + "DatabaseFilePath.txt");
                    File.WriteAllText(executablePath + @"\DatabaseFilePath.txt", path);  //Αποθηκεύει το path στο Program Files
                }
                return path;
            }
            set
            {
                string executablePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);

                File.WriteAllText(executablePath + @"\DatabaseFilePath.txt", value);  //Αποθηκεύει στο Program Files αντί για το Programs Data.
                //File.WriteAllText(Globals.Application.DataPath + "DatabaseFilePath.txt", value);
                Connection = new SqlConnection(@"Server=" + Globals.Application.ServerName + @";Database=" + "Tourun" + @";User ID=sa;Password=" + Globals.Application.ServerPassword + ";Connect Timeout=30;Encrypt=True;TrustServerCertificate=True;");
            }
        }

        public static TourunDataSet DataSet
        {
            get
            {
                return ds;
            }
            set
            {
                ds = value;
            }
        }

        public static SqlConnection Connection
        {
            get
            {
                return connection;
            }
            set
            {
                connection = value;

                toursTA.Connection = connection;
                accommodationsTA.Connection = connection;
                coordinatorsTA.Connection = connection;
                toursTA.Connection = connection;
                attractionsTA.Connection = connection;
                rolesTA.Connection = connection;
                usersTA.Connection = connection;
                tourAttractionsTA.Connection = connection;
                tourVehiclesTA.Connection = connection;
                tourAccommodationsTA.Connection = connection;
            }
        }

        static Data()
        {
            Properties.Settings.Default.Reload();
            InitializeMembers();
        }

        private static void InitializeMembers()
        {
            ds = new TourunDataSet();

            #region  Θέτει την αρχική τιμή σε στήλες των DataTables
            //Rooms
            foreach (DataColumn column in ds.Tour.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
            }

            //Accommodation
            foreach (DataColumn column in ds.Accommodation.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
            }

            //Vehicle
            foreach (DataColumn column in ds.Vehicle.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
            }

            //Coordinator
            foreach (DataColumn column in ds.Coordinator.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
            }
            ds.Coordinator.UserIdColumn.AllowDBNull = true;

            //Attraction
            foreach (DataColumn column in ds.Attraction.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
                else if (column.DataType == typeof(double))
                {
                    column.DefaultValue = 0;
                }
            }

            //TourAccommodation
            foreach (DataColumn column in ds.TourAccommodation.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
                //else if (column.DataType == typeof(int))
                //{
                //    if (column.AutoIncrement == false && column.ColumnName.Contains("ID") == false)
                //    {
                //        column.DefaultValue = 0;
                //    }
                //}

            }

            //Role
            foreach (DataColumn column in ds.Role.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
            }

            //Users
            foreach (DataColumn column in ds.User.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
                else if (column.DataType == typeof(double))
                {
                    column.DefaultValue = 0;
                }
            }

            //TourAttraction
            foreach (DataColumn column in ds.TourAttraction.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
            }

            //TourVehicle
            foreach (DataColumn column in ds.TourVehicle.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
            }


            #endregion

            if (connection != null)
            {
                connection.Close();
                connection.Dispose();
                connection = null;
            }
            connection = new SqlConnection(@"Server=" + Globals.Application.ServerName + @";Database=" + "Tourun" + @";User ID=sa;Password=" + Globals.Application.ServerPassword + ";Connect Timeout=30;Encrypt=True;TrustServerCertificate=True;");

            taManager = new TourunDataSetTableAdapters.TableAdapterManager();

            toursTA = new Tourun.UI.TourunDataSetTableAdapters.TourTableAdapter();
            toursTA.Connection = connection;
            accommodationsTA = new Tourun.UI.TourunDataSetTableAdapters.AccommodationTableAdapter();
            accommodationsTA.Connection = connection;
            toursTA = new Tourun.UI.TourunDataSetTableAdapters.TourTableAdapter();
            toursTA.Connection = connection;
            coordinatorsTA = new Tourun.UI.TourunDataSetTableAdapters.CoordinatorTableAdapter();
            coordinatorsTA.Connection = connection;
            attractionsTA = new Tourun.UI.TourunDataSetTableAdapters.AttractionTableAdapter();
            attractionsTA.Connection = connection;
            rolesTA = new Tourun.UI.TourunDataSetTableAdapters.RoleTableAdapter();
            rolesTA.Connection = connection;
            usersTA = new Tourun.UI.TourunDataSetTableAdapters.UserTableAdapter();
            usersTA.Connection = connection;
            tourAttractionsTA = new Tourun.UI.TourunDataSetTableAdapters.TourAttractionTableAdapter();
            tourAttractionsTA.Connection = connection;
            tourVehiclesTA = new Tourun.UI.TourunDataSetTableAdapters.TourVehicleTableAdapter();
            tourVehiclesTA.Connection = connection;
            tourAccommodationsTA = new Tourun.UI.TourunDataSetTableAdapters.TourAccommodationTableAdapter();
            tourAccommodationsTA.Connection = connection;
            vehiclesTA = new TourunDataSetTableAdapters.VehicleTableAdapter();
            vehiclesTA.Connection = connection;

            taManager.Connection = connection;
            taManager.TourTableAdapter = toursTA;
            taManager.TourAccommodationTableAdapter = tourAccommodationsTA;
            taManager.AccommodationTableAdapter = accommodationsTA;
            taManager.VehicleTableAdapter = vehiclesTA;
            taManager.CoordinatorTableAdapter = coordinatorsTA;
            taManager.AttractionTableAdapter = attractionsTA;
            taManager.RoleTableAdapter = rolesTA;
            taManager.UserTableAdapter = usersTA;
            taManager.TourAttractionTableAdapter = tourAttractionsTA;
            taManager.TourVehicleTableAdapter = tourVehiclesTA;
        }

        public static bool CheckConnectionValidity(string serverName, string serverPassword)
        {
            try
            {
                SqlConnection connection = new SqlConnection(@"Server=" + serverName + @";Database=" + "Tourun" + @";User ID=sa;Password=" + serverPassword + ";Connect Timeout=15");

                connection.Open();
            }
            catch (SqlException)
            {
                //ExceptionHandler.RecordException(exp);
                return false;
            }
            catch (Exception exp)
            {
                ExceptionHandler.RecordException(exp);
                return false;
            }
            finally
            {
                connection.Close();
            }

            return true;
        }

        public static void GetAllData()
        {
            try
            {
                ds.Clear();
                connection.Open();

                rolesTA.Fill(ds.Role);
                usersTA.Fill(ds.User);
                accommodationsTA.Fill(ds.Accommodation);
                attractionsTA.Fill(ds.Attraction);
                coordinatorsTA.Fill(ds.Coordinator);
                vehiclesTA.Fill(ds.Vehicle);
                toursTA.Fill(ds.Tour);
                tourAccommodationsTA.Fill(ds.TourAccommodation);
                tourAttractionsTA.Fill(ds.TourAttraction);
                tourVehiclesTA.Fill(ds.TourVehicle);
            }
            finally
            {
                connection.Close();
            }
        }

        public static void SaveAllData()
        {
            try
            {
                connection.Open();

                taManager.UpdateAll(ds);

                ds.AcceptChanges();
            }
            catch (Exception exp)
            {
                ds.RejectChanges();
                throw exp;
            }
            finally
            {
                connection.Close();
            }
        }

        public class Administration
        {
            public static bool IsValidUser(string username, string password)
            {
                SqlConnection localConnection = null;
                int count = 0;

                try
                {
                    localConnection = new SqlConnection(connection.ConnectionString);
                    localConnection.Open();

                    SqlCommand command = new SqlCommand();
                    command.Connection = localConnection;
                    command.CommandText = @"SELECT COUNT(*) AS Result FROM [User] WHERE Username='" + username + "' AND Password='" + password + "'";
                    count = (int)command.ExecuteScalar();
                }
                catch (CaughtedException exp)
                {
                    throw exp;
                    //MessageBox.Show(exp.Message, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                catch (Exception exp)
                {
                    //MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                    ExceptionHandler.RecordException(exp);
                    throw exp;
                }
                finally
                {
                    if (localConnection != null)
                    {

                        localConnection.Close();
                    }
                }

                if (count > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        public class Tour
        {
            /// <summary>
            /// Saves only data of Tour.
            /// </summary>
            public static void SaveTours()
            {
                try
                {
                    connection.Open();

                    toursTA.Update(ds.Tour);

                    ds.AcceptChanges();
                }
                catch (Exception exp)
                {
                    ds.Tour.RejectChanges();
                    throw exp;
                }
                finally
                {
                    connection.Close();
                }
            }
          
        }

        public class Vehicles
        {
            /// <summary>
            /// Saves only data of Vehicles.
            /// </summary>
            public static void SaveVehicles()
            {
                try
                {
                    connection.Open();

                    vehiclesTA.Update(ds.Vehicle);

                    ds.AcceptChanges();
                }
                catch (Exception exp)
                {
                    ds.Vehicle.RejectChanges();
                    throw exp;
                }
                finally
                {
                    connection.Close();
                }
            }

            public static bool CheckVehicleExistence(string licensePlate, Guid vehicleId)
            {
                SqlDataReader reader;
                SqlCommand command;
                bool connectionMustClose = false;  //Indicates if we must close the connection after the execution of query.
                int result = 0;
                try
                {
                    if (connection.State == ConnectionState.Closed)
                    {
                        connection.Open();
                        connectionMustClose = true;  //We must close the connection because we found it closed.
                    }
                    command = new SqlCommand();
                    command.CommandText = @"SELECT Count(*) AS Result FROM Vehicle WHERE [Vehicle].[PlateNumber]='" + licensePlate + "' AND VehicleId<>'" + vehicleId.ToString() + "'";
                    command.Connection = connection;
                    reader = command.ExecuteReader();
                    if (reader == null)
                    {
                        throw new Exception("variable reader=null");
                    }
                    reader.Read();
                    result = reader.GetInt32(0);
                    reader.Close();
                }
                finally
                {
                    if (connectionMustClose)  //If we must close the connection
                    {
                        connection.Close();
                    }
                }

                if (result >= 1) //Αν βρέθηκε τουλάχιστον ένα 
                {
                    return true;
                }
                else  //Αν δεν βρεθηκε  
                {
                    return false;
                }
            }

            public static bool VehicleHasReservations(Guid vehicleId)
            {
                SqlDataReader reader;
                SqlCommand command;
                bool connectionMustClose = false;  //Indicates if we must close the connection after the execution of query.
                int result = 0;
                try
                {
                    if (connection.State == ConnectionState.Closed)
                    {
                        connection.Open();
                        connectionMustClose = true;  //We must close the connection because we found it closed.
                    }
                    command = new SqlCommand();
                    command.CommandText = @"SELECT Count(*) AS Result FROM Reservation WHERE [Reservation].[VehicleId]='" + vehicleId.ToString() + "' ;";
                    command.Connection = connection;
                    reader = command.ExecuteReader();
                    if (reader == null)
                    {
                        throw new Exception("variable reader=null");
                    }
                    reader.Read();
                    result = reader.GetInt32(0);
                    reader.Close();
                }
                finally
                {
                    if (connectionMustClose)  //If we must close the connection
                    {
                        connection.Close();
                    }
                }

                if (result >= 1) //Αν βρέθηκε τουλάχιστον ένα 
                {
                    return true;
                }
                else  //Αν δεν βρεθηκε  
                {
                    return false;
                }
            }
        }

        public class Coordinators
        {
            /// <summary>
            /// Saves only data of Coordinators.
            /// </summary>
            public static void SaveCoordinators()
            {
                try
                {
                    connection.Open();

                    coordinatorsTA.Update(ds.Coordinator);

                    ds.AcceptChanges();
                }
                catch (Exception exp)
                {
                    ds.Coordinator.RejectChanges();
                    throw exp;
                }
                finally
                {
                    connection.Close();
                }
            }

            public static bool CheckCoordinatorExistence(Int64 userId, Int64 coordinatorId)
            {
                SqlDataReader reader;
                SqlCommand command;
                bool connectionMustClose = false;  //Indicates if we must close the connection after the execution of query.
                int result = 0;
                try
                {
                    if (connection.State == ConnectionState.Closed)
                    {
                        connection.Open();
                        connectionMustClose = true;  //We must close the connection because we found it closed.
                    }
                    command = new SqlCommand();
                    command.CommandText = @"SELECT Count(*) AS Result FROM Coordinator WHERE [Coordinator].[UserId]=" + userId.ToString() + " AND CoordinatorId<>" + coordinatorId.ToString() ;
                    command.Connection = connection;
                    reader = command.ExecuteReader();
                    if (reader == null)
                    {
                        throw new Exception("variable reader=null");
                    }
                    reader.Read();
                    result = reader.GetInt32(0);
                    reader.Close();
                }
                finally
                {
                    if (connectionMustClose)  //If we must close the connection
                    {
                        connection.Close();
                    }
                }

                if (result >= 1) //Αν βρέθηκε τουλάχιστον ένα 
                {
                    return true;
                }
                else  //Αν δεν βρεθηκε  
                {
                    return false;
                }
            }

            public static bool CoordinatorHasTours(Guid coordinatorId)
            {
                SqlDataReader reader;
                SqlCommand command;
                bool connectionMustClose = false;  //Indicates if we must close the connection after the execution of query.
                int result = 0;
                try
                {
                    if (connection.State == ConnectionState.Closed)
                    {
                        connection.Open();
                        connectionMustClose = true;  //We must close the connection because we found it closed.
                    }
                    command = new SqlCommand();
                    command.CommandText = @"SELECT Count(*) AS Result FROM Tour WHERE [Tour].[CoordinatorId]='" + coordinatorId.ToString() + "' ;";
                    command.Connection = connection;
                    reader = command.ExecuteReader();
                    if (reader == null)
                    {
                        throw new Exception("variable reader=null");
                    }
                    reader.Read();
                    result = reader.GetInt32(0);
                    reader.Close();
                }
                finally
                {
                    if (connectionMustClose)  //If we must close the connection
                    {
                        connection.Close();
                    }
                }

                if (result >= 1) //Αν βρέθηκε τουλάχιστον ένα 
                {
                    return true;
                }
                else  //Αν δεν βρεθηκε  
                {
                    return false;
                }
            }
        }

        public class Attractions
        {
            /// <summary>
            /// Saves only data of Attractions.
            /// </summary>
            public static void SaveAttractions()
            {
                try
                {
                    connection.Open();

                    attractionsTA.Update(ds.Attraction);

                    ds.AcceptChanges();
                }
                catch (Exception exp)
                {
                    ds.Attraction.RejectChanges();
                    throw exp;
                }
                finally
                {
                    connection.Close();
                }
            }

            public static bool CheckAttractionExistence(string name, Int64 attractionId)
            {
                SqlDataReader reader;
                SqlCommand command;
                bool connectionMustClose = false;  //Indicates if we must close the connection after the execution of query.
                int result = 0;
                try
                {
                    if (connection.State == ConnectionState.Closed)
                    {
                        connection.Open();
                        connectionMustClose = true;  //We must close the connection because we found it closed.
                    }
                    command = new SqlCommand();
                    command.CommandText = @"SELECT Count(*) AS Result FROM Attraction WHERE [Attraction].[Name]='" + name.Replace("'", "''") + "' AND AttractionId<>" + attractionId.ToString() + "";
                    command.Connection = connection;
                    reader = command.ExecuteReader();
                    if (reader == null)
                    {
                        throw new Exception("variable reader=null");
                    }
                    reader.Read();
                    result = reader.GetInt32(0);
                    reader.Close();
                }
                finally
                {
                    if (connectionMustClose)  //If we must close the connection
                    {
                        connection.Close();
                    }
                }

                if (result >= 1) //Αν βρέθηκε τουλάχιστον ένα 
                {
                    return true;
                }
                else  //Αν δεν βρεθηκε  
                {
                    return false;
                }
            }

            public static bool AttractionHasTours(Guid attractionId)
            {
                SqlDataReader reader;
                SqlCommand command;
                bool connectionMustClose = false;  //Indicates if we must close the connection after the execution of query.
                int result = 0;
                try
                {
                    if (connection.State == ConnectionState.Closed)
                    {
                        connection.Open();
                        connectionMustClose = true;  //We must close the connection because we found it closed.
                    }
                    command = new SqlCommand();
                    command.CommandText = @"SELECT Count(*) AS Result FROM TourAttraction WHERE [TourAttraction].[AttractionId]='" + attractionId.ToString() + "' ;";
                    command.Connection = connection;
                    reader = command.ExecuteReader();
                    if (reader == null)
                    {
                        throw new Exception("variable reader=null");
                    }
                    reader.Read();
                    result = reader.GetInt32(0);
                    reader.Close();
                }
                finally
                {
                    if (connectionMustClose)  //If we must close the connection
                    {
                        connection.Close();
                    }
                }

                if (result >= 1) //Αν βρέθηκε τουλάχιστον ένα 
                {
                    return true;
                }
                else  //Αν δεν βρεθηκε  
                {
                    return false;
                }
            }
        }

        public class Users
        {
            public static UserDataTable GetAllUsers()
            {
                TourunDataSet ds = new TourunDataSet();
                ds.EnforceConstraints = false;
                usersTA.Fill(ds.User);

                return ds.User;
            }
        }

    }
}
