﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="Data.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="TourunConnectionString" Type="(Connection string)" Scope="Application">
      <DesignTimeValue Profile="(Default)">&lt;?xml version="1.0" encoding="utf-16"?&gt;
&lt;SerializableConnectionString xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;
  &lt;ConnectionString&gt;Data Source=MAIN\SQLEXPRESS;Initial Catalog=Tourun;Integrated Security=True;Encrypt=False&lt;/ConnectionString&gt;
  &lt;ProviderName&gt;Microsoft.Data.SqlClient&lt;/ProviderName&gt;
&lt;/SerializableConnectionString&gt;</DesignTimeValue>
      <Value Profile="(Default)">Data Source=MAIN\SQLEXPRESS;Initial Catalog=Tourun;Integrated Security=True;Encrypt=False</Value>
    </Setting>
  </Settings>
</SettingsFile>