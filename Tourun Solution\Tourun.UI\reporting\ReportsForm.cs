﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace Tourun.UI.Reporting
{
    public enum ReportsFormResult
    {
        Preview,
        Print,
        PageSetup,
        ExportToHtml,
        ExportToPdf,
        ExportToRtf
    }

    public enum Report
    {
        DailyReservations,
        IncomeAnalysis,
        None
    }

    public enum PrintPricesInVisitReport
    {
        No,
        YesWithoutFpa,
        YesOnlyTotalsWithFpa,
        YesAllPricesWithFpa
    }

    public enum PrintPricesInQuoteReport
    {
        No,
        YesWithoutFpa,
        YesOnlyTotalsWithFpa,
        YesAllPricesWithFpa
    }

    public partial class ReportsForm : Form
    {
        private ReportsFormResult result;
        private bool exportToFileVisible = true;

        public ReportsFormResult Result
        {
            get
            {
                return this.result;
            }
        }

        public bool ExportToFileVisible
        {
            set
            {
                this.exportToFileVisible = value;
                this.exportToFileBtn.Visible = value;
            }
            get
            {
                return this.exportToFileVisible;
            }
        }

        public DateTime DailyReservationsReportDate
        {
            get
            {
                return this.dailyReservationsDateDtPick.Value;
            }
        }

        public bool DailyReservationsShowDailyCost
        {
            get
            {
                return this.dailyReservationsShowDailyCostChkBox.Checked;
            }
        }

        public DateTime IncomeAnalysisReportStartDate
        {
            get
            {
                return this.incomeAnalysisFromDateDtPick.Value;
            }
        }

        public DateTime IncomeAnalysisReportEndDate
        {
            get
            {
                return this.incomeAnalysisToDateDtPick.Value;
            }
        }

        public bool IncomeAnalysisReportIncludeRoomReservations
        {
            get
            {
                return this.incomeAnalysisIncludeRoomReservationsChkBox.Checked;
            }
        }

        public bool IncomeAnalysisReportIncludeApartmentReservations
        {
            get
            {
                return this.incomeAnalysisIncludeApartmentReservationsChkBox.Checked;
            }
        }

        public bool IncomeAnalysisReportIncludeVehicleReservations
        {
            get
            {
                return this.incomeAnalysisIncludeVehicleReservationsChkBox.Checked;
            }
        }

        public bool IncomeAnalysisReportIncludeIncome
        {
            get
            {
                return this.incomeAnalysisIncludeIncomeChkBox.Checked;
            }
        }

        public Int64[] IncomeAnalysisReportIncomeTypes
        {
            get
            {
                List<Int64> ids = new List<long>();
                for (int i = 0; i < this.incomeAnalysisIncomeTypesChkListView.CheckedItems.Count; i++)
                {
                    ids.Add(Convert.ToInt64(this.incomeAnalysisIncomeTypesChkListView.CheckedItems[i].Tag));
                }
                return ids.ToArray();
            }
        }

        public Int64[] IncomeAnalysisReportRooms
        {
            get
            {
                List<Int64> ids = new List<long>();
                for (int i = 0; i < this.incomeAnalysisRoomsChkListView.CheckedItems.Count; i++)
                {
                    ids.Add(Convert.ToInt64(this.incomeAnalysisRoomsChkListView.CheckedItems[i].Tag));
                }
                return ids.ToArray();
            }
        }

        public Int64[] IncomeAnalysisReportApartments
        {
            get
            {
                List<Int64> ids = new List<long>();
                for (int i = 0; i < this.incomeAnalysisApartmentsChkListView.CheckedItems.Count; i++)
                {
                    ids.Add(Convert.ToInt64(this.incomeAnalysisApartmentsChkListView.CheckedItems[i].Tag));
                }
                return ids.ToArray();
            }
        }

        public Int64[] IncomeAnalysisReportVehicles
        {
            get
            {
                List<Int64> ids = new List<long>();
                for (int i = 0; i < this.incomeAnalysisVehiclesChkListView.CheckedItems.Count; i++)
                {
                    ids.Add(Convert.ToInt64(this.incomeAnalysisVehiclesChkListView.CheckedItems[i].Tag));
                }
                return ids.ToArray();
            }
        }

        public ReportsForm(Report reportName)
        {
            InitializeComponent();

            this.optionsTab.ShowTabs = false;

            #region  DataBindings
            //PrepareDailyRoomReservations report
            this.dailyReservationsDateDtPick.Value = DateTime.Today;
            this.dailyReservationsShowDailyCostChkBox.Visible= Globals.Application.CurrentUserRole.Name == "Admin";

            //IncomeAnalysis report
            this.incomeAnalysisToDateDtPick.DataBindings.Add("MinDate", this.incomeAnalysisFromDateDtPick, "Value");
            this.incomeAnalysisFromDateDtPick.Value = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            this.incomeAnalysisToDateDtPick.Value = this.incomeAnalysisFromDateDtPick.Value.AddMonths(1).AddDays(-1);
            this.incomeAnalysisIncomeTypesChkListView.DataBindings.Add("Enabled", this.incomeAnalysisIncludeIncomeChkBox, "Checked");
            this.incomeAnalysisRoomsChkListView.DataBindings.Add("Enabled", this.incomeAnalysisIncludeRoomReservationsChkBox, "Checked");
            this.incomeAnalysisApartmentsChkListView.DataBindings.Add("Enabled", this.incomeAnalysisIncludeApartmentReservationsChkBox, "Checked");

           
            #endregion

            switch (reportName)
            {
                case Report.DailyReservations:
                    {
                        this.optionsTab.SelectedTab = this.dailyReservationsTabPage;
                        break;
                    }
                case Report.IncomeAnalysis:
                    {
                        this.optionsTab.SelectedTab = this.incomeAnalysisTabPage;
                        break;
                    }
                case Report.None:
                default:
                    {
                        this.optionsTab.Visible = false;
                        break;
                    }
            }
        }

        private void previewBtn_Click(object sender, EventArgs e)  //Top function
        {
            try
            {
                this.result = ReportsFormResult.Preview;
                this.Close();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void printBtn_Click(object sender, EventArgs e)  //Top function
        {
            try
            {
                this.result = ReportsFormResult.Print;
                this.Close();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void pageSettingsBtn_Click(object sender, EventArgs e)  //Top function
        {
            try
            {
                this.result = ReportsFormResult.PageSetup;
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void exportPDFBtn_Click(object sender, EventArgs e)  //Top function
        {
            try
            {
                this.result = ReportsFormResult.PageSetup;
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void commandManager_CommandClick(object sender, Janus.Windows.UI.CommandBars.CommandEventArgs e)  //Top function
        {
            try
            {
                switch (e.Command.Key)
                {
                    case "ExportToHtml":
                        {
                            this.result = ReportsFormResult.ExportToHtml;
                            this.DialogResult = System.Windows.Forms.DialogResult.OK;
                            break;
                        }
                    case "ExportToPdf":
                        {
                            this.result = ReportsFormResult.ExportToPdf;
                            this.DialogResult = System.Windows.Forms.DialogResult.OK;
                            break;
                        }
                    case "ExportToRtf":
                        {
                            this.result = ReportsFormResult.ExportToRtf;
                            this.DialogResult = System.Windows.Forms.DialogResult.OK;
                            break;
                        }
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }


        private void ReportsForm_FormClosing(object sender, FormClosingEventArgs e)  //Top function
        {
            try
            {

            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void ReportsForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                this.Close();
            }
        }

        private void exportPdfBtn_Click_1(object sender, EventArgs e)
        {
            try
            {
                this.result = ReportsFormResult.ExportToPdf;
                this.Close();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void incomeAnalysisCheckAllRoomsBtn_Click(object sender, EventArgs e)
        {
            foreach(ListViewItem item in this.incomeAnalysisRoomsChkListView.Items)
            {
                item.Checked = true;
            }
        }

        private void incomeAnalysisUncheckAllRoomsBtn_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in this.incomeAnalysisRoomsChkListView.Items)
            {
                item.Checked = false;
            }
        }

        private void incomeAnalysisCheckAllApartmentsBtn_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in this.incomeAnalysisApartmentsChkListView.Items)
            {
                item.Checked = true;
            }
        }

        private void incomeAnalysisUncheckAllApartmentsBtn_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in this.incomeAnalysisApartmentsChkListView.Items)
            {
                item.Checked = false;
            }
        }

        private void incomeAnalysisCheckAllVehiclesBtn_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in this.incomeAnalysisVehiclesChkListView.Items)
            {
                item.Checked = true;
            }
        }

        private void incomeAnalysisUncheckAllVehiclesBtn_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in this.incomeAnalysisVehiclesChkListView.Items)
            {
                item.Checked = false;
            }
        }

        private void incomeAnalysisCheckAllIncomeTypesBtn_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in this.incomeAnalysisIncomeTypesChkListView.Items)
            {
                item.Checked = true;
            }
        }

        private void incomeAnalysisUncheckAllIncomeTypesBtn_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in this.incomeAnalysisIncomeTypesChkListView.Items)
            {
                item.Checked = false;
            }
        }
    }
}
