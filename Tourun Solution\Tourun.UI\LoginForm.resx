<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="passwordLabel.GenerateMember" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="label1.GenerateMember" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAd3SURBVFhH1ZZ5UJRXFsVtNhVNaaJJOQ4uERSEKG6Asm/qsMmicQeEDmGQxUgERMGwyCK7aVka
        sJsdGxqRZqdFFmURAohjCFIybmDhxBpnSdXMP8OZ+z6YmSKRqbHEP+ar+tXX/XXXveedc9/rnjfv/+GS
        F/D128sCYksSzEXpoVuyci/o80WRRkvfu/bqDIclTaJD0tGe9Mnnv5PiUVcS+moCkRVhCXGM5WtJkiX/
        vYkoS7Za2Jjr3FUuOABxtBFK4o0Jo8mCizsnRdEmCDmhDUnavknZFZvQ9yKiLMky7qLvFhTEmqI5b/+f
        JEkWvkWxxmuLYo02lcSbCGN8N0262qihtdj1H7J0W+M5FVGaYLpEGG7wk+CsHnor+X+vzbDX+3mD0ktm
        kec81iMtxAi38g+2zqkAaar1AUHINnSUe6NL6nX5TcVL402VM0L1nqSc2YFOidtkndDh13MmQpZhG0l2
        4369P9qK3UxmK1wYZ5JSEGeJGqEL6nP2Oc2ZgPpsx6ymq864X+eDplyXNbMVpqi+qhTYoSRxL+QiF/c5
        E9BccFTcIeFjsOa3qMtyWD1b4WuJZgFV6fYoiLOiOfh87gS0lpwQd5Z7YUDmhTqh/awOXEswOyUjAfmx
        Frgpcpo7AS1FbuI7Eg/0XvdEU7bDrAJK4k2DZBkOEEWboUloP3cCbuYfEbcWHUdXmSvqs2ePID/GNIxF
        kBNhDFn6b95dQEPOcdW2wsMGbcVHuptEB9BaeBAygbW0QWgT25bv5HO37Ihd33VX3b4q748acg8vop1y
        rlJgj8ywXahIs3w3Ac97Uz2H2y/9sbvUBbfE+1CZZgZpsjFu5tqgt+xzDFw/goHKoxy9Fe5oLzo0WSWw
        eiW9bIvUM9shSTD0fqdd8KLngvhZx1m8HEzDeF8SHjQGoqPYCf3UeKT1HHpIRI/kAO5e2//ve0msAYou
        WSPWTweZIZqTNQLzHyqSDDPvFLkYvrWYlwOJ4md3vsbj9tN42h6Asa5gTAwkEKnoKT+Ke1XuGG7yxVDD
        SXLBlRNRk26F3HBdFCc7ojxxF5rFzqhMNUNp3HbxWwsY6zovHusMwh/6o/Dqfgom+uMx3hOJFz3foCHL
        GiUxW1B8URfX4raiIsUUNRk2kOfacRE1ZFqj+ooFUv1XItlvFaSJBm8vYLQlQDwi98bjVn+MdYbgRxLx
        ekSMvz6txsANd7SIWCNL3Eg1hCR+B/K+0UZehA7EFzbievJOekZzQM+r0oxQe8XsTQJ45Arjzdd4d5j4
        SftXeNIeiEe3/EiIHwkJxqvvs9Bf6YauEmeOTpqLjiJH3Cncx72/d+MYhhu86JkT1zwzWAMJ/hp5G9Ys
        VqRO84lFxAfTLKb7QkKFUJihZLTFXzx6y5eaBuLHe3GY6IvjIhjviUB9phVnf2HUJrpvRkGkDr3+jKzW
        o88sOIdkl41x+dRKfHv6UwS5a5ZTcQ1Cm9hIrCNWEsumhTARyjMcGZH7iNkWG6rn41GzL8bvhnEx/PlR
        CQZlHmjPt6e896JaYIryBD3kk/255zcg59x6LoKKZAMSYcIJCfHY2EzFLQldYhWxdHrlrClzhq2e8Z9I
        Hree4hwYbvwSP9R7YajOE+z9xEASuiUHuRlgtrfl2XERMEEMthuG6PvdtD3r0s2REaQOD7vltVScHeHM
        cqUZjaZ8Z83/JWTqyVAdP/tBjTtGm30w1nEaL7rPkgsRFEEUatMtIArXIjQ5rp5fD1GYJhdJZcou9EqP
        cSsXBKpBGKqFUM+NTACzffm0CJY5a8gcYPazuWB3Jm7qGm70yRmoPIY+6SEwIb9v8aMtGIbXD69SBO7c
        quXZu7lG5Qn6KIrWpQi0uAikifqcEHn2Hs6FIPcNXVTyELGH0Cc0CTWCzQGDCVOddmJKwKCML3rY6EX5
        n+Sm+kGtJ0XBx/PuCLQXOHJbUJ5tjWaaA0ZjlhVu5uzhYvievssiYM8yg9eB7/Dxd1TyJOFKOBJsHtj/
        Sh1iLcGGke2QqRmID9jGkyTtzWbH7H2ZK0bkX+LZ7QA8vR2I513haBTu5oYuM/hTpH+9ChlnViMzaC2y
        QtTpPNBBW4EzxWROrmzmHIry3d5PZf2JE8R+Yjexg1Cfbr5gxuqZCG31Dz/arr0s8GKA/uDtQqfJexTH
        cOMXmPguio5hN27/3y5woFVak92GtB234mqYNonQoNNxG1rEtugtPwj2YxbuvYkJCCCOEyyGzcSvCGY7
        m4U3X8pKCgqKirwlyz9caOvuqCW9IbD96aHcH3KyujJlJ6q/NeEybhJasdOOe88On7u0EwarjnPN6QcJ
        TparG6iDy/SqWeb/vfHP5Sgo8HiKCrwFSkqKWqZ6ayK/cFnXeuqw2pMYn7V/S/ZbgcSTnyDFXw2pAatx
        5Yw6ajN205w4QHhh518+W788meqxvFcQv7R61uXP8gGJUeTx5i3g8XhL6LXa4kULdq1aschts8YH0eZb
        l5a6mC3rCXZb95LvotU4X0XJdDrjmafc2zb9X76voqzAm6+iqKBKPqkuVFZVUVacTyJnz3eWov8ENmc8
        /Bkg6VcAAAAASUVORK5CYII=
</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAQICAwEBAgsIDhEmCyAoTBAvO3ENGh9AAAAABgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAMGBwEECAoXHjxHdD13jdAseJX1LICi/y2Aof8+kLH/TJ69/Bw+TJ8AAAAqAAAACgAA
        AAEAAAAAAAAAAAAAAAAwYnWHUp+982e72v9ot9X/Oo+w/y6DpP8ne5z/NIeo/0OYuP87gp7nAAAAZgAA
        AEMAAAAfAAAACAAAAAEAAAAAYrTUzmG11f9rvdz/c73Z/0abvP9AlLX/OIyt/0KXt/9Fmrr/J22J5AAA
        AFkAAAA8AAAAJgAAABgAAAAFAAAAAGe62c5pvNz/ccHf/2m10v85j7D/OY+w/zqQsf9Tpsb/OpCw/yJm
        gtoAAAAZAAAABgAAAAIAAAAGAAAABQAAAABmudjPcsLg/4TM5/9yu9f/O5Cx/zOIqf8sgaP/P5Oz/zKH
        qP8rcIzaAAAAMAAAAB4AAAAPAAAABwAAAAMAAAAAb8Dfz3DB3/+DzOf/gsXf/0ugwf9DmLn/PZKz/0id
        vf88kbL/MIKizwQLDQMAAAAGAAAABwAAAAQAAAABAAAAAHvH5M98yeX/gsvm/2u31P9Knbz/TZ++/0qc
        u/9Cj63/OYqq/zGCos8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACCxuC8b7nV/0+evf9crcv/V6zM/1ir
        y/9Jmbj6O36Z/zSFpZBClLQ1AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAT46sA1ijv2hYo8D/RpW0VFWl
        xC1grssQPYWiQEOJpP9AjaowAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABepL9QY6zI/0CN
        rRAAAAAAAAAAAD6JpjlGjaj/SJe3MAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAU525TmGs
        yP8/iacQAAAAAAAAAAA+iqc4SI6q/06atzAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEeS
        rzVQl7P/XqfCNAAAAAAAAAAAOYCcXUmPqv9JlbEbAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAA8iKQEN3qV3lCat8c+iKUjPIelMiplft9Nk66+AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAE2YtDhfn7jqa6rD/0qOqf8+fpjUR5KwHgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAVpq1DlSYskkxe5g/O3+dBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAA8B+sQYADrEGAAKxBgACsQYAArEGAAKxBgACsQYAfrEGAH6xBgD+sQcY/rEHGP6xBxj+sQcB/
        rEHgf6xB8P+sQQ==
</value>
  </data>
</root>