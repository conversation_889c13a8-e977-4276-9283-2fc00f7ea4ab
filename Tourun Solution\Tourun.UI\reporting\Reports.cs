﻿using Janus.Windows.GridEX;

using System;
using System.Data;
using System.Drawing.Printing;
using System.Windows.Forms;

namespace Tourun.UI.Reporting
{
    public enum ExportFileType
    {
        Pdf,
        Html,
        Rtf
    }

    class Reports
    {
        private static SaveFileDialog saveFileDialog = new SaveFileDialog();
        private static C1.C1Report.C1Report report = new C1.C1Report.C1Report();

        /// <summary>
        /// Checks the settings if the printer for the reports is set and if so checks if this printer is installed.
        /// </summary>
        /// <returns>A value indicating if the printer for the reports is valid.</returns>
        public static bool IsReportsPrinterValid(string printer)
        {
            //Ελέγχει αν ο εκτυπωτής για τα reports έχει ρυθμιστεί.
            if (printer == "")
            {
                MessageBox.Show(Properties.Resources.PrinterNotSetErrorMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }
            //Ελέγχει αν ο εκτυπωτής για τα reports είναι πράγματι εγκατεστημένος στον υπολογιστή.
            foreach (string installedPrinter in PrinterSettings.InstalledPrinters)
            {
                if (installedPrinter == printer)
                {
                    return true;
                }
            }
            MessageBox.Show(Properties.Resources.InvalidPrinterErrorMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Information);
            return false;
        }

        public static void PreviewDocument(System.Drawing.Printing.PrintDocument document)  //Non top function
        {
            try
            {
                if (document.PrinterSettings.IsValid)
                {
                    PrintPreviewForm form = new PrintPreviewForm(document);
                    form.WindowState = Application.OpenForms[0].WindowState;
                    form.SetDesktopBounds(Application.OpenForms[0].DesktopBounds.X, Application.OpenForms[0].DesktopBounds.Y, Application.OpenForms[0].DesktopBounds.Width, Application.OpenForms[0].DesktopBounds.Height);
                    form.ShowDialog();
                }
                else
                {
                    MessageBox.Show(Properties.Resources.InvalidPrinterError, Globals.Application.AssemblyTitle);
                }
            }
            catch (Exception exp)
            {
                ExceptionHandler.RecordException(exp);
                throw new Exception(Properties.Resources.GeneralExceptionMessage);
            }
        }

        public static bool PrintDocument(System.Drawing.Printing.PrintDocument document)  //Non top function
        {
            try
            {
                if (document.PrinterSettings.IsValid)
                {
                    PrintDialog dlg = new PrintDialog();
                    dlg.PrinterSettings = document.PrinterSettings;
                    if (dlg.ShowDialog() == DialogResult.OK)
                    {
                        Cursor.Current = Cursors.WaitCursor;
                        document.PrinterSettings = dlg.PrinterSettings;
                        document.Print();
                        return true;
                    }

                }
                else
                {
                    MessageBox.Show(Properties.Resources.InvalidPrinterError, Globals.Application.AssemblyTitle);
                }

                return false;
            }
            catch (Exception exp)
            {
                ExceptionHandler.RecordException(exp);
                throw new Exception(Properties.Resources.GeneralExceptionMessage);
            }
        }

        public static bool PrintDocument(GridEXPrintDocument gridEXPrintDocument)  //Non top function
        {
            try
            {
                if (gridEXPrintDocument.DefaultPageSettings.PrinterSettings.IsValid)
                {
                    PrintDialog dlg = new PrintDialog();
                    dlg.PrinterSettings = gridEXPrintDocument.PrinterSettings;
                    if (dlg.ShowDialog() == DialogResult.OK)
                    {
                        Cursor.Current = Cursors.WaitCursor;
                        gridEXPrintDocument.PrinterSettings = dlg.PrinterSettings;
                        gridEXPrintDocument.Print();
                        return true;
                    }
                }
                else
                {
                    MessageBox.Show(Properties.Resources.InvalidPrinterError, Globals.Application.AssemblyTitle);
                }

                return false;
            }
            catch (Exception exp)
            {
                ExceptionHandler.RecordException(exp);
                throw new Exception(Properties.Resources.GeneralExceptionMessage);
            }
        }

        public static void ExportReportToFile(ExportFileType exportFileType)
        {
            try
            {
                if (exportFileType == ExportFileType.Html)
                {
                    //Αλλάζουμε τα properties του LogoField στον χώρο εκτύπωσης των στοιχείων του φροντιστηρίου γιατί αλλιώς το Logo δεν εμφανίζεται σωστά.
                    report.Fields["HotelDataSubreportField"].Subreport.Fields["LogoField"].CanShrink = false;
                    report.Fields["HotelDataSubreportField"].Subreport.Fields["LogoField"].PictureScale = C1.C1Report.PictureScaleEnum.Clip;

                    saveFileDialog.Filter = "Html files (*.html)|*.html";
                    saveFileDialog.DefaultExt = "html";
                    saveFileDialog.FileName = report.ReportName;
                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        report.RenderToFile(saveFileDialog.FileName, C1.C1Report.FileFormatEnum.HTML);
                    }
                }
                if (exportFileType == ExportFileType.Pdf)
                {
                    saveFileDialog.Filter = "PDF files (*.pdf)|*.pdf";
                    saveFileDialog.DefaultExt = "pdf";
                    saveFileDialog.FileName = report.ReportName;
                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        report.RenderToFile(saveFileDialog.FileName, C1.C1Report.FileFormatEnum.PDF);
                    }
                }
                if (exportFileType == ExportFileType.Rtf)
                {
                    saveFileDialog.Filter = "RTF files (*.rtf)|*.rtf";
                    saveFileDialog.DefaultExt = "rtf";
                    saveFileDialog.FileName = report.ReportName;
                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        report.RenderToFile(saveFileDialog.FileName, C1.C1Report.FileFormatEnum.RTF);
                    }
                }
            }
            catch (Exception exp)
            {
                ExceptionHandler.RecordException(exp);
                throw new Exception(Properties.Resources.GeneralExceptionMessage);
            }
        }

        public static void ReportPageSetup(PageSettings pageSettings)
        {
            //Αν ο εκτυπωτής δεν είναι έγκυρος
            if (!pageSettings.PrinterSettings.IsValid)
            {
                //Αν υπάρχουν εγκατεστημένοι υπολογιστές
                if (System.Drawing.Printing.PrinterSettings.InstalledPrinters.Count > 0)
                {
                    pageSettings.PrinterSettings = new PrinterSettings();
                    pageSettings.Margins = new Margins(50, 50, 50, 50);
                }
                else  //Αν δεν υπάρχουν εγκατεστημένοι υπολογιστές
                {
                    MessageBox.Show(Properties.Resources.NoInstalledPrintersMessage, Globals.Application.AssemblyTitle);
                    return;
                }
            }

            PrintDialog printDialog = new PrintDialog();
            printDialog.PrinterSettings = pageSettings.PrinterSettings;
            if (printDialog.ShowDialog() == DialogResult.OK)
            {
                pageSettings.PrinterSettings = printDialog.PrinterSettings;
            }
        }

        private static void InitPageAndPrinterSettings()
        {
            if (Properties.Settings.Default.DataGridReportsPageSettings == null)
            {
                PrinterSettings printerSettings = new PrinterSettings();
                Properties.Settings.Default.DataGridReportsPageSettings = new PageSettings(printerSettings);
                Properties.Settings.Default.DataGridReportsPageSettings.Margins = new Margins(50, 50, 50, 50);
            }
          
        }

        private static void PrepareCompanyDataSubreport()
        {
            //report.Fields["CompanyDataSubreportField"].Subreport.DataSource.Recordset = Data.DataSet.Settings;

            //#region  Ρυθμίζει την κεφαλίδα της εκτύπωσης
            //if (Properties.Settings.Default.PrintCompanyDataOnReports)
            //{
            //    //Εμφανίζει την είκονα-logo ή το κείμενο με τα στοιχεία ανάλογα με τις ρυθμίσεις.
            //    //Αν ο χρήστης έχει επιλέξει να εμφανίζεται η εικόνα-logo
            //    if (Properties.Settings.Default.PrintLogoOnReports)
            //    {
            //        report.Fields["CompanyDataSubreportField"].Subreport.Fields["LogoField"].Visible = true;
            //        report.Fields["CompanyDataSubreportField"].Subreport.Fields["CompanyDataField"].Visible = false;

            //        //Αν δεν υπάρχει εικόνα-logo
            //        if (Data.DataSet.Settings[0].IsLogoNull())
            //        {
            //            report.Fields["CompanyDataSubreportField"].Subreport.Fields["LogoField"].Visible = false;
            //        }
            //    }
            //    else  //Αν ο χρήστης έχει επιλέξει να εμφανίζεται το κείμενο με τα στοιχεία της επιχείρησης.
            //    {
            //        report.Fields["CompanyDataSubreportField"].Subreport.Fields["LogoField"].Visible = false;
            //        report.Fields["CompanyDataSubreportField"].Subreport.Fields["CompanyDataField"].Visible = true;
            //    }
            //}
            //else  //Αν ο χρήστης έχει επιλέξει να μην εμφανίζεται η κεφαλίδα.
            //{
            //    report.Fields["CompanyDataSubreportField"].Visible = false;
            //}
            //#endregion
        }


        #region  Grids
        private static void PrepareDataGridReport(GridEXPrintDocument printDocument)  //Non Top function
        {
            try
            {
                printDocument.DocumentName = printDocument.GridEX.RootTable.Caption;

                InitPageAndPrinterSettings();  //TODO: Πρέπει να φύγει από εδώ και να δω τι θα γίνει με τα PageSettings objects
                printDocument.TranslateSystemColors = false;
                printDocument.DefaultPageSettings = Properties.Settings.Default.DataGridReportsPageSettings;
                printDocument.PrinterSettings = Properties.Settings.Default.DataGridReportsPageSettings.PrinterSettings;

            }
            catch (Exception exp)
            {
                ExceptionHandler.RecordException(exp);
                throw new Exception(Properties.Resources.GeneralExceptionMessage);
            }
        }

        public static void PreviewDataGridReport(GridEXPrintDocument printDocument)  //Top function
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;

                //Κάνει το page setup
                PrepareDataGridReport(printDocument);
                PreviewDocument(printDocument);
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = System.Windows.Forms.Cursors.Default;
            }
        }

        public static void PrintDataGridReport(GridEXPrintDocument printDocument)  //Top function
        {
            try
            {
                PrepareDataGridReport(printDocument);

                PrintDocument(printDocument);
            }
            finally
            {
                Cursor.Current = System.Windows.Forms.Cursors.Default;
            }
        }

        public static void ShowDataGridReportForm(GridEXPrintDocument printDocument)
        {
            ReportsForm form = new ReportsForm(Report.None);
            form.ExportToFileVisible = false;
            if (form.ShowDialog() == DialogResult.OK)
            {
                if (form.Result == ReportsFormResult.Preview)
                {
                    PreviewDataGridReport(printDocument);
                }
                else if (form.Result == ReportsFormResult.Print)
                {
                    PrintDataGridReport(printDocument);
                }
                else if (form.Result == ReportsFormResult.ExportToPdf)
                {
                    PrintDataGridReport(printDocument);
                }
                else if (form.Result == ReportsFormResult.PageSetup)
                {
                    ReportPageSetup(Properties.Settings.Default.DataGridReportsPageSettings);
                }
            }
        }
        #endregion

       



    }
}
