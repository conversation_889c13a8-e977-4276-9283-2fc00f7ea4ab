﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Azure.Core" version="1.47.1" targetFramework="net481" />
  <package id="Azure.Identity" version="1.14.2" targetFramework="net481" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net481" />
  <package id="Microsoft.Bcl.Cryptography" version="8.0.0" targetFramework="net481" />
  <package id="Microsoft.Data.SqlClient" version="6.1.1" targetFramework="net481" />
  <package id="Microsoft.Data.SqlClient.SNI" version="6.0.2" targetFramework="net481" />
  <package id="Microsoft.Extensions.Caching.Abstractions" version="8.0.0" targetFramework="net481" />
  <package id="Microsoft.Extensions.Caching.Memory" version="8.0.1" targetFramework="net481" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.2" targetFramework="net481" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="8.0.3" targetFramework="net481" />
  <package id="Microsoft.Extensions.Options" version="8.0.2" targetFramework="net481" />
  <package id="Microsoft.Extensions.Primitives" version="8.0.0" targetFramework="net481" />
  <package id="Microsoft.Identity.Client" version="4.73.1" targetFramework="net481" />
  <package id="Microsoft.Identity.Client.Extensions.Msal" version="4.73.1" targetFramework="net481" />
  <package id="Microsoft.IdentityModel.Abstractions" version="7.7.1" targetFramework="net481" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="7.7.1" targetFramework="net481" />
  <package id="Microsoft.IdentityModel.Logging" version="7.7.1" targetFramework="net481" />
  <package id="Microsoft.IdentityModel.Protocols" version="7.7.1" targetFramework="net481" />
  <package id="Microsoft.IdentityModel.Protocols.OpenIdConnect" version="7.7.1" targetFramework="net481" />
  <package id="Microsoft.IdentityModel.Tokens" version="7.7.1" targetFramework="net481" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net481" />
  <package id="System.ClientModel" version="1.5.1" targetFramework="net481" />
  <package id="System.Data.Common" version="4.3.0" targetFramework="net481" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.1" targetFramework="net481" />
  <package id="System.IdentityModel.Tokens.Jwt" version="7.7.1" targetFramework="net481" />
  <package id="System.IO.FileSystem.AccessControl" version="5.0.0" targetFramework="net481" />
  <package id="System.Memory" version="4.5.5" targetFramework="net481" />
  <package id="System.Memory.Data" version="8.0.1" targetFramework="net481" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net481" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net481" />
  <package id="System.Security.AccessControl" version="5.0.0" targetFramework="net481" />
  <package id="System.Security.Cryptography.Pkcs" version="8.0.1" targetFramework="net481" />
  <package id="System.Security.Cryptography.ProtectedData" version="4.5.0" targetFramework="net481" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net481" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net481" />
  <package id="System.Text.Json" version="8.0.5" targetFramework="net481" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net481" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net481" />
</packages>