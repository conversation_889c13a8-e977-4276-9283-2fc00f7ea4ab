﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace Tourun.UI
{
    public partial class LoginForm : Form
    {
        private bool useForAlekos;

        public LoginForm(bool useForAlekos = false)
        {
            InitializeComponent();

            this.useForAlekos = useForAlekos;
            if (this.useForAlekos)
            {
                this.userNameTxtBox.Text = "";
            }
        }

        private void cancelBtn_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void okBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.useForAlekos)
                {
                    if (this.userNameTxtBox.Text== "045445" && this.passwordTextBox.Text == "045445")
                    {
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        MessageBox.Show(Properties.Resources.InvalidCredentialsMessage, Globals.Application.AssemblyTitle);
                        this.passwordTextBox.Focus();
                        this.passwordTextBox.SelectAll();
                    }
                }
                else
                {
                    if (Data.Administration.IsValidUser(this.userNameTxtBox.Text, this.passwordTextBox.Text) == true)
                    {
                        Globals.Application.UserName = this.userNameTxtBox.Text;
                        Globals.Application.Password = this.passwordTextBox.Text;

                        TourunDataSetTableAdapters.UserTableAdapter usersTA = new TourunDataSetTableAdapters.UserTableAdapter();
                        usersTA.Connection = Data.Connection;
                        TourunDataSet.UserDataTable usersDT = usersTA.GetDataByUsername(this.userNameTxtBox.Text);
                        Globals.Application.CurrentUser = usersDT[0];

                        TourunDataSetTableAdapters.RoleTableAdapter rolesTA = new TourunDataSetTableAdapters.RoleTableAdapter();
                        rolesTA.Connection = Data.Connection;
                        TourunDataSet.RoleDataTable rolesDT = rolesTA.GetDataByRoleId(usersDT[0].RoleId);
                        Globals.Application.CurrentUserRole = rolesDT[0];

                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        MessageBox.Show(Properties.Resources.InvalidCredentialsMessage, Globals.Application.AssemblyTitle);
                        this.passwordTextBox.Focus();
                        this.passwordTextBox.SelectAll();
                    }
                }
            }
            catch (CaughtedException exp)
            {
                MessageBox.Show(exp.Message, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage + exp.Message, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }
    }
}
