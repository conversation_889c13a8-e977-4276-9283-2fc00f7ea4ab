﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Tourun.UI
{
    public partial class TourForm : Form
    {
        private bool editMode = false;  //Καθορίζει αν γίνεται edit ή new
        private bool allowSave = true;  //Καθορίζει αν θα επιτρέπεται η αποθήκευση της περιηγήσης ή αν θα είναι μόνο για εμφάνιση στοιχείων

        public TourForm(Int64? tourId)
        {
            InitializeComponent();

            try
            {
                //Ρυθμίζει τα databindings
                this.toursBindingSource.DataSource = Data.DataSet;
                this.toursBindingSource.DataMember = Data.DataSet.Tour.TableName;

                this.coordinatorComboBox.DataSource = Data.DataSet.Coordinator;
                this.coordinatorComboBox.DisplayMember = "UserFullName";
                this.coordinatorComboBox.ValueMember = "CoordinatorId";

                // Initialize grid binding sources
                this.tourVehiclesBindingSource.DataSource = this.toursBindingSource;
                this.tourVehiclesBindingSource.DataMember = "FK_TourVehicle_Tour";

                this.tourAttractionsBindingSource.DataSource = this.toursBindingSource;
                this.tourAttractionsBindingSource.DataMember = "FK_TourAttraction_Tour";

                this.tourAccommodationsBindingSource.DataSource = this.toursBindingSource;
                this.tourAccommodationsBindingSource.DataMember = "FK_TourAccommodation_Tour";

                if (tourId.HasValue)
                {
                    int position = this.toursBindingSource.Find("TourId", tourId.Value);
                    this.toursBindingSource.Position = position;
                    this.editMode = true;
                }
                else
                {
                    this.toursBindingSource.AddNew();
                    DataRowView rowView = this.toursBindingSource.Current as DataRowView;
                    TourunDataSet.TourRow row = rowView.Row as TourunDataSet.TourRow;
                    row.ArrivalDate = DateTime.Today;
                    row.DepartureDate = DateTime.Today.AddDays(2);
                    row.CreatedAt = DateTime.Now;
                    row.UpdatedAt = DateTime.Now;

                    this.toursBindingSource.EndEdit();
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        public bool AllowSave
        {
            set
            {
                this.allowSave = value;

                this.saveAndCloseCmd.Visible = value ? Janus.Windows.UI.InheritableBoolean.True : Janus.Windows.UI.InheritableBoolean.False;
                this.saveAndNewCmd.Visible = value ? Janus.Windows.UI.InheritableBoolean.True : Janus.Windows.UI.InheritableBoolean.False;
                this.deleteCmd.Visible = value ? Janus.Windows.UI.InheritableBoolean.True : Janus.Windows.UI.InheritableBoolean.False;
                this.TopRebar1.Visible = value;
                this.okBtn.Visible = !value;
                this.cancelBtn.Visible = !value;
            }
            get
            {
                return this.allowSave;
            }
        }

        public void SetReadOnly()
        {
            this.toolbar.Enabled = false;

            //Για κάθε control
            foreach (Control control in this.Controls)
            {
                Globals.Application.MakeControlReadOnly(control);
            }
        }

        private void TourForm_Load(object sender, EventArgs e)
        {
            try
            {
                DataRowView rowView = this.toursBindingSource.Current as DataRowView;
                TourunDataSet.TourRow row = rowView.Row as TourunDataSet.TourRow;
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void TourForm_Shown(object sender, EventArgs e)
        {
            this.tourCodeTextBox.Focus();
        }

        private void commandManager_CommandClick(object sender, Janus.Windows.UI.CommandBars.CommandEventArgs e)  //Top function
        {
            Cursor.Current = Cursors.WaitCursor;
            try
            {
                switch (e.Command.Key)
                {
                    case "SaveAndClose":
                        {
                            this.SaveAndClose();
                            break;
                        }
                    case "SaveAndNew":
                        {
                            this.SaveAndNew();
                            break;
                        }
                    case "Delete":
                        {
                            this.Delete();
                            break;
                        }
                    case "Close":
                        {
                            this.Close();
                            break;
                        }
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void SaveAndClose()  //Top function
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;
                this.Validate();
                this.toursBindingSource.EndEdit();

                if (this.CheckInputData())
                {
                    if (this.allowSave)
                    {
                        Data.Tour.SaveTours();
                    }

                    this.DialogResult = System.Windows.Forms.DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);

                this.Close();  //Κλείνουμε τη φόρμα στη περίπτωση που εμφανίστηκε σφάλμα ώστε να μην μεταφερθεί σε άλλη περιήγηση
            }
            finally
            {
                Cursor.Current = Cursors.Default;
            }
        }

        private void SaveAndNew()  //Top function
        {
            try
            {
                Cursor.Current = Cursors.WaitCursor;
                this.Validate();
                this.toursBindingSource.EndEdit();

                if (this.CheckInputData())
                {
                    Data.Tour.SaveTours();
                    this.toursBindingSource.AddNew();
                    DataRowView rowView = this.toursBindingSource.Current as DataRowView;
                    TourunDataSet.TourRow row = rowView.Row as TourunDataSet.TourRow;
                    row.CreatedAt = DateTime.Now;
                    row.UpdatedAt = DateTime.Now;
                }

                this.tourCodeTextBox.Focus();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage + exp.InnerException.Message, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);

                this.Close();  //Κλείνουμε τη φόρμα στη περίπτωση που εμφανίστηκε σφάλμα ώστε να μην μεταφερθεί σε άλλη περιήγηση
            }
            finally
            {
                Cursor.Current = Cursors.WaitCursor;
            }
        }

        private void Delete()  //Top function
        {
            try
            {
                if (MessageBox.Show(Properties.Resources.DeleteDataConfirmationMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) == DialogResult.Yes)
                {
                    this.toursBindingSource.RemoveCurrent();
                    if (this.allowSave)
                    {
                        Data.SaveAllData();  //Αποθηκεύουμε όλα τα Data και οχι μόνο του Tour γιατί όταν γίνεται διαγραφή τότε υπάρχουν συσχετιζόμενες οντότητες
                    }

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);

                this.Close();  //Κλείνουμε τη φόρμα στη περίπτωση που εμφανίστηκε σφάλμα ώστε να μην μεταφερθεί σε άλλη περιήγηση
            }
        }

        private bool CheckInputData()  //Non top function
        {
            try
            {
                bool errorfound = false;

                #region  Κάνει τους ελέγχους και εμφανίζει τα μηνύματα
                //Ελέγχει το TourCode είναι συμπληρωμένο
                if (string.IsNullOrWhiteSpace(this.tourCodeTextBox.Text))
                {
                    this.errorProvider.SetError(this.tourCodeTextBox, Properties.Resources.FieldRequiredMessage);
                    errorfound = true;
                }
                else
                {
                    this.errorProvider.SetError(this.tourCodeTextBox, "");
                }

                //Ελέγχει το Coordinator είναι επιλεγμένο
                if (this.coordinatorComboBox.SelectedIndex < 0 || this.coordinatorComboBox.SelectedValue == null)
                {
                    this.errorProvider.SetError(this.coordinatorComboBox, Properties.Resources.FieldRequiredMessage);
                    errorfound = true;
                }
                else
                {
                    this.errorProvider.SetError(this.coordinatorComboBox, "");
                }

                //Ελέγχει οι ημερομηνίες είναι έγκυρες
                if (this.arrivalDatePicker.Value >= this.departureDatePicker.Value)
                {
                    this.errorProvider.SetError(this.departureDatePicker, "Η ημερομηνία αναχώρησης πρέπει να είναι μετά την ημερομηνία άφιξης.");
                    errorfound = true;
                }
                else
                {
                    this.errorProvider.SetError(this.departureDatePicker, "");
                }

                #endregion

                #region  Αν δεν έχουν βρεθεί άλλα λάθη πιο πριν τότε ελέγχει την μοναδικότητα
                if (errorfound == false)
                {
                    DataRowView rowView = this.toursBindingSource.Current as DataRowView;
                    TourunDataSet.TourRow row = rowView.Row as TourunDataSet.TourRow;

                    #region  Ελέγχει αν η Tour πρόκειται να καταχωρηθεί στο DataSet 2 φορές.
                    //Ελέγχει το TourCode της Tour υπάρχει ήδη στο DataSet.
                    if (Data.DataSet.Tour.Select("TourCode='" + row.TourCode.Replace("'", "''") + "'", "", DataViewRowState.CurrentRows).Length > 1)
                    {
                        MessageBox.Show("Υπάρχει ήδη περιήγηση με αυτό τον κωδικό.", Globals.Application.AssemblyTitle);
                        return false;
                    }
                    #endregion

                    #region  Εφόσον η Tour δεν υπάρχει στο DataSet ελέγχει στην βάση δεδομένων αν υπάρχει.
                    // For now, we'll skip the database check to keep it simple like the CoordinatorForm pattern
                    #endregion
                }
                #endregion

                //Αφού έχει εξετάσει όλες τις περιπτώσεις λαθών βγάζει ενα μήνυμα και τελειώνει η συνάρτηση
                if (errorfound == true)  //Αν βρήκε λάθη
                {
                    MessageBox.Show(Properties.Resources.InputDataValidationErrorMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK);
                    return false;
                }
                else  //Αν δεν βρήκε λάθη τα οποία πρέπει να δείξει με το errorProvider.
                {
                    return true;
                }
            }
            catch (Exception exp)
            {
                ExceptionHandler.RecordException(exp);
                throw new Exception(Properties.Resources.GeneralExceptionMessage);
            }
        }

        private void TourForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                if (this.allowSave == false)
                {
                    if (this.DialogResult != DialogResult.OK)
                    {
                        if (this.editMode)
                        {
                            this.toursBindingSource.CancelEdit();
                        }
                        else
                        {
                            this.toursBindingSource.RemoveCurrent();
                        }
                    }
                }
                else
                {
                    Data.DataSet.Tour.RejectChanges();
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void TourForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                this.Close();
            }
        }

        private void cancelBtn_Click(object sender, EventArgs e)
        {
            try
            {
                this.Close();
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }

        private void okBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.CheckInputData() == true)
                {
                    this.toursBindingSource.EndEdit();
                }
            }
            catch (Exception exp)
            {
                MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
                ExceptionHandler.RecordException(exp);
            }
        }
    }
}
