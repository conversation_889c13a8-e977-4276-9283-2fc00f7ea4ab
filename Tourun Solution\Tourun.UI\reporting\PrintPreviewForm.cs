using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Diagnostics;
using System.Windows.Forms;

namespace Tourun.UI.Reporting
{
	public partial class PrintPreviewForm : System.Windows.Forms.Form
	{
        private System.Drawing.Printing.PrintDocument printDocument = null;
		
        public PrintPreviewForm(System.Drawing.Printing.PrintDocument printDocument)
        {
            InitializeComponent();

            this.printDocument = printDocument;

            this.ribbon.DocumentName = Properties.Resources.PreviewText;
            this.ribbon.ApplicationName = printDocument.DocumentName;
           
            //this.SetLookAndFeel();
            this.printPreviewControl.Document = printDocument;
        }

        //private void SetLookAndFeel()
        //{
        //    try
        //    {
        //        if (Properties.Settings.Default.LookAndFeel == "Office2007Blue")
        //        {
        //            this.ribbon.Office2007ColorScheme = Janus.Windows.Ribbon.Office2007ColorScheme.Blue;
        //        }
        //        else if (Properties.Settings.Default.LookAndFeel == "Office2007Silver")
        //        {
        //            this.ribbon.Office2007ColorScheme = Janus.Windows.Ribbon.Office2007ColorScheme.Silver;
        //        }
        //        else if (Properties.Settings.Default.LookAndFeel == "Office2007Black")
        //        {
        //            this.ribbon.Office2007ColorScheme = Janus.Windows.Ribbon.Office2007ColorScheme.Black;
        //        }
        //        else if (Properties.Settings.Default.LookAndFeel == "Office2003")
        //        {
        //            this.ribbon.Office2007ColorScheme = Janus.Windows.Ribbon.Office2007ColorScheme.Blue;
        //        }
        //    }
        //    catch (Exception exp)
        //    {
        //        MessageBox.Show(Properties.Resources.GeneralExceptionMessage, Globals.Application.AssemblyTitle, MessageBoxButtons.OK);
        //        ExceptionHandler.RecordException(exp);
        //    }
        //    finally
        //    {
        //        Cursor.Current = Cursors.Default;
        //    }
        //}


		private void PrintPreviewControl_StartPageChanged(object sender, System.EventArgs e)
		{
            this.rcmdPreviousPage.Enabled = (this.printPreviewControl.StartPage > 0);
		}

        private void ribbon_CommandClick(object sender, Janus.Windows.Ribbon.CommandEventArgs e)
        {
            switch (e.Command.Key)
            {
                case "rcmdPreviousPage":
                    this.printPreviewControl.StartPage = this.printPreviewControl.StartPage - 1;
                    break;
                case "rcmdNextPage":
                    this.printPreviewControl.StartPage = this.printPreviewControl.StartPage + 1;
                    break;
                case "rcmdActualSize":
                    this.rcmdOnePage.Checked = false;
                    rcmdTwoPages.Checked = false;
                    this.printPreviewControl.AutoZoom = false;
                    this.printPreviewControl.Zoom = 1;
                    break;
                case "rcmdOnePage":
                    rcmdActualSize.Checked = false;
                    rcmdTwoPages.Checked = false;
                    this.printPreviewControl.AutoZoom = true;
                    this.printPreviewControl.Rows = 1;
                    this.printPreviewControl.Columns = 1;
                    break;
                case "rcmdTwoPages":
                    this.rcmdActualSize.Checked = false;
                    this.rcmdOnePage.Checked = false;
                    this.printPreviewControl.AutoZoom = true;
                    this.printPreviewControl.Rows = 1;
                    this.printPreviewControl.Columns = 2;
                    break;
                case "rcmdPageSetup":
                    this.pageSetupDialog.Document = this.printPreviewControl.Document;
                    if (this.pageSetupDialog.ShowDialog(this) == System.Windows.Forms.DialogResult.OK)
                    {
                        System.Drawing.Printing.PrintDocument doc = null;
                        doc = this.printPreviewControl.Document;
                        this.printPreviewControl.Document = doc;
                    }
                    break;
                case "rcmdPrint":
                    //this.printPreviewControl.Document.Print();
                    if (Reporting.Reports.PrintDocument(this.printDocument) == true)
                    {
                        this.Close();
                    }
                    break;
                case "rcmdClose":
                    this.Close();
                    break;
            }
        }

        private void PrintPreviewForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                this.Close();
            }
        }
	}

}