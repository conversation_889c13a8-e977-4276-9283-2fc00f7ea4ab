﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="TourunDataSet" targetNamespace="http://tempuri.org/TourunDataSet.xsd" xmlns:mstns="http://tempuri.org/TourunDataSet.xsd" xmlns="http://tempuri.org/TourunDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Settings" AppSettingsPropertyName="TourunConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="TourunConnectionString (Settings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Data.Properties.Settings.GlobalReference.Default.TourunConnectionString" Provider="Microsoft.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="AccommodationTableAdapter" GeneratorDataComponentClassName="AccommodationTableAdapter" Name="Accommodation" UserDataComponentName="AccommodationTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString (Settings)" DbObjectName="Tourun.dbo.Accommodation" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Accommodation] WHERE (([AccommodationId] = @Original_AccommodationId) AND ([Name] = @Original_Name) AND ((@IsNull_Type = 1 AND [Type] IS NULL) OR ([Type] = @Original_Type)) AND ((@IsNull_AddressLine1 = 1 AND [AddressLine1] IS NULL) OR ([AddressLine1] = @Original_AddressLine1)) AND ((@IsNull_AddressLine2 = 1 AND [AddressLine2] IS NULL) OR ([AddressLine2] = @Original_AddressLine2)) AND ((@IsNull_City = 1 AND [City] IS NULL) OR ([City] = @Original_City)) AND ((@IsNull_StateRegion = 1 AND [StateRegion] IS NULL) OR ([StateRegion] = @Original_StateRegion)) AND ((@IsNull_PostalCode = 1 AND [PostalCode] IS NULL) OR ([PostalCode] = @Original_PostalCode)) AND ((@IsNull_Country = 1 AND [Country] IS NULL) OR ([Country] = @Original_Country)) AND ((@IsNull_Phone = 1 AND [Phone] IS NULL) OR ([Phone] = @Original_Phone)) AND ((@IsNull_Email = 1 AND [Email] IS NULL) OR ([Email] = @Original_Email)) AND ((@IsNull_Website = 1 AND [Website] IS NULL) OR ([Website] = @Original_Website)) AND ((@IsNull_Rating = 1 AND [Rating] IS NULL) OR ([Rating] = @Original_Rating)) AND ([CreatedAt] = @Original_CreatedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_AccommodationId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Type" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Type" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AddressLine1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_AddressLine1" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AddressLine2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_AddressLine2" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_City" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_City" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_StateRegion" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_StateRegion" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_PostalCode" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_PostalCode" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Country" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Country" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Phone" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Phone" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Email" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Email" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Website" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Website" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Rating" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Rating" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Rating" Precision="3" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Rating" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Accommodation] ([AccommodationId], [Name], [Type], [AddressLine1], [AddressLine2], [City], [StateRegion], [PostalCode], [Country], [Phone], [Email], [Website], [Rating], [CreatedAt]) VALUES (@AccommodationId, @Name, @Type, @AddressLine1, @AddressLine2, @City, @StateRegion, @PostalCode, @Country, @Phone, @Email, @Website, @Rating, @CreatedAt);
SELECT AccommodationId, Name, Type, AddressLine1, AddressLine2, City, StateRegion, PostalCode, Country, Phone, Email, Website, Rating, CreatedAt FROM Accommodation WHERE (AccommodationId = @AccommodationId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@AccommodationId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Type" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@AddressLine1" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@AddressLine2" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@City" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@StateRegion" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@PostalCode" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Country" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Phone" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Website" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Rating" Precision="3" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Rating" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT AccommodationId, Name, Type, AddressLine1, AddressLine2, City, StateRegion, PostalCode, Country, Phone, Email, Website, Rating, CreatedAt FROM dbo.Accommodation</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Accommodation] SET [AccommodationId] = @AccommodationId, [Name] = @Name, [Type] = @Type, [AddressLine1] = @AddressLine1, [AddressLine2] = @AddressLine2, [City] = @City, [StateRegion] = @StateRegion, [PostalCode] = @PostalCode, [Country] = @Country, [Phone] = @Phone, [Email] = @Email, [Website] = @Website, [Rating] = @Rating, [CreatedAt] = @CreatedAt WHERE (([AccommodationId] = @Original_AccommodationId) AND ([Name] = @Original_Name) AND ((@IsNull_Type = 1 AND [Type] IS NULL) OR ([Type] = @Original_Type)) AND ((@IsNull_AddressLine1 = 1 AND [AddressLine1] IS NULL) OR ([AddressLine1] = @Original_AddressLine1)) AND ((@IsNull_AddressLine2 = 1 AND [AddressLine2] IS NULL) OR ([AddressLine2] = @Original_AddressLine2)) AND ((@IsNull_City = 1 AND [City] IS NULL) OR ([City] = @Original_City)) AND ((@IsNull_StateRegion = 1 AND [StateRegion] IS NULL) OR ([StateRegion] = @Original_StateRegion)) AND ((@IsNull_PostalCode = 1 AND [PostalCode] IS NULL) OR ([PostalCode] = @Original_PostalCode)) AND ((@IsNull_Country = 1 AND [Country] IS NULL) OR ([Country] = @Original_Country)) AND ((@IsNull_Phone = 1 AND [Phone] IS NULL) OR ([Phone] = @Original_Phone)) AND ((@IsNull_Email = 1 AND [Email] IS NULL) OR ([Email] = @Original_Email)) AND ((@IsNull_Website = 1 AND [Website] IS NULL) OR ([Website] = @Original_Website)) AND ((@IsNull_Rating = 1 AND [Rating] IS NULL) OR ([Rating] = @Original_Rating)) AND ([CreatedAt] = @Original_CreatedAt));
SELECT AccommodationId, Name, Type, AddressLine1, AddressLine2, City, StateRegion, PostalCode, Country, Phone, Email, Website, Rating, CreatedAt FROM Accommodation WHERE (AccommodationId = @AccommodationId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@AccommodationId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Type" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@AddressLine1" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@AddressLine2" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@City" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@StateRegion" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@PostalCode" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Country" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Phone" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Website" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Rating" Precision="3" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Rating" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_AccommodationId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Type" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Type" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AddressLine1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_AddressLine1" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AddressLine2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_AddressLine2" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_City" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_City" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_StateRegion" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_StateRegion" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_PostalCode" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_PostalCode" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Country" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Country" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Phone" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Phone" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Email" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Email" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Website" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Website" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Rating" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Rating" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Rating" Precision="3" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Rating" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="AccommodationId" DataSetColumn="AccommodationId" />
              <Mapping SourceColumn="Name" DataSetColumn="Name" />
              <Mapping SourceColumn="Type" DataSetColumn="Type" />
              <Mapping SourceColumn="AddressLine1" DataSetColumn="AddressLine1" />
              <Mapping SourceColumn="AddressLine2" DataSetColumn="AddressLine2" />
              <Mapping SourceColumn="City" DataSetColumn="City" />
              <Mapping SourceColumn="StateRegion" DataSetColumn="StateRegion" />
              <Mapping SourceColumn="PostalCode" DataSetColumn="PostalCode" />
              <Mapping SourceColumn="Country" DataSetColumn="Country" />
              <Mapping SourceColumn="Phone" DataSetColumn="Phone" />
              <Mapping SourceColumn="Email" DataSetColumn="Email" />
              <Mapping SourceColumn="Website" DataSetColumn="Website" />
              <Mapping SourceColumn="Rating" DataSetColumn="Rating" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="AttractionTableAdapter" GeneratorDataComponentClassName="AttractionTableAdapter" Name="Attraction" UserDataComponentName="AttractionTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString (Settings)" DbObjectName="Tourun.dbo.Attraction" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Attraction] WHERE (([AttractionId] = @Original_AttractionId) AND ([Name] = @Original_Name) AND ((@IsNull_Type = 1 AND [Type] IS NULL) OR ([Type] = @Original_Type)) AND ((@IsNull_Description = 1 AND [Description] IS NULL) OR ([Description] = @Original_Description)) AND ((@IsNull_AddressLine1 = 1 AND [AddressLine1] IS NULL) OR ([AddressLine1] = @Original_AddressLine1)) AND ((@IsNull_AddressLine2 = 1 AND [AddressLine2] IS NULL) OR ([AddressLine2] = @Original_AddressLine2)) AND ((@IsNull_City = 1 AND [City] IS NULL) OR ([City] = @Original_City)) AND ((@IsNull_StateRegion = 1 AND [StateRegion] IS NULL) OR ([StateRegion] = @Original_StateRegion)) AND ((@IsNull_PostalCode = 1 AND [PostalCode] IS NULL) OR ([PostalCode] = @Original_PostalCode)) AND ((@IsNull_Country = 1 AND [Country] IS NULL) OR ([Country] = @Original_Country)) AND ((@IsNull_Latitude = 1 AND [Latitude] IS NULL) OR ([Latitude] = @Original_Latitude)) AND ((@IsNull_Longitude = 1 AND [Longitude] IS NULL) OR ([Longitude] = @Original_Longitude)) AND ((@IsNull_Website = 1 AND [Website] IS NULL) OR ([Website] = @Original_Website)) AND ([CreatedAt] = @Original_CreatedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_AttractionId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Type" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Type" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Description" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Description" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AddressLine1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_AddressLine1" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AddressLine2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_AddressLine2" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_City" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_City" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_StateRegion" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_StateRegion" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_PostalCode" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_PostalCode" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Country" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Country" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Latitude" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Latitude" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Latitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Latitude" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Longitude" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Longitude" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Longitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Longitude" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Website" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Website" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Attraction] ([AttractionId], [Name], [Type], [Description], [AddressLine1], [AddressLine2], [City], [StateRegion], [PostalCode], [Country], [Latitude], [Longitude], [Website], [CreatedAt]) VALUES (@AttractionId, @Name, @Type, @Description, @AddressLine1, @AddressLine2, @City, @StateRegion, @PostalCode, @Country, @Latitude, @Longitude, @Website, @CreatedAt);
SELECT AttractionId, Name, Type, Description, AddressLine1, AddressLine2, City, StateRegion, PostalCode, Country, Latitude, Longitude, Website, CreatedAt FROM Attraction WHERE (AttractionId = @AttractionId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@AttractionId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Type" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Description" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@AddressLine1" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@AddressLine2" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@City" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@StateRegion" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@PostalCode" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Country" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Latitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Latitude" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Longitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Longitude" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Website" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT AttractionId, Name, Type, Description, AddressLine1, AddressLine2, City, StateRegion, PostalCode, Country, Latitude, Longitude, Website, CreatedAt FROM dbo.Attraction</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Attraction] SET [AttractionId] = @AttractionId, [Name] = @Name, [Type] = @Type, [Description] = @Description, [AddressLine1] = @AddressLine1, [AddressLine2] = @AddressLine2, [City] = @City, [StateRegion] = @StateRegion, [PostalCode] = @PostalCode, [Country] = @Country, [Latitude] = @Latitude, [Longitude] = @Longitude, [Website] = @Website, [CreatedAt] = @CreatedAt WHERE (([AttractionId] = @Original_AttractionId) AND ([Name] = @Original_Name) AND ((@IsNull_Type = 1 AND [Type] IS NULL) OR ([Type] = @Original_Type)) AND ((@IsNull_Description = 1 AND [Description] IS NULL) OR ([Description] = @Original_Description)) AND ((@IsNull_AddressLine1 = 1 AND [AddressLine1] IS NULL) OR ([AddressLine1] = @Original_AddressLine1)) AND ((@IsNull_AddressLine2 = 1 AND [AddressLine2] IS NULL) OR ([AddressLine2] = @Original_AddressLine2)) AND ((@IsNull_City = 1 AND [City] IS NULL) OR ([City] = @Original_City)) AND ((@IsNull_StateRegion = 1 AND [StateRegion] IS NULL) OR ([StateRegion] = @Original_StateRegion)) AND ((@IsNull_PostalCode = 1 AND [PostalCode] IS NULL) OR ([PostalCode] = @Original_PostalCode)) AND ((@IsNull_Country = 1 AND [Country] IS NULL) OR ([Country] = @Original_Country)) AND ((@IsNull_Latitude = 1 AND [Latitude] IS NULL) OR ([Latitude] = @Original_Latitude)) AND ((@IsNull_Longitude = 1 AND [Longitude] IS NULL) OR ([Longitude] = @Original_Longitude)) AND ((@IsNull_Website = 1 AND [Website] IS NULL) OR ([Website] = @Original_Website)) AND ([CreatedAt] = @Original_CreatedAt));
SELECT AttractionId, Name, Type, Description, AddressLine1, AddressLine2, City, StateRegion, PostalCode, Country, Latitude, Longitude, Website, CreatedAt FROM Attraction WHERE (AttractionId = @AttractionId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@AttractionId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Type" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Description" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@AddressLine1" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@AddressLine2" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@City" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@StateRegion" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@PostalCode" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Country" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Latitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Latitude" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Longitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Longitude" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Website" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_AttractionId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Type" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Type" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Description" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Description" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AddressLine1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_AddressLine1" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AddressLine2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_AddressLine2" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_City" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_City" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_StateRegion" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_StateRegion" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="StateRegion" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_PostalCode" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_PostalCode" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Country" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Country" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Country" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Latitude" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Latitude" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Latitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Latitude" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Longitude" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Longitude" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Longitude" Precision="9" ProviderType="Decimal" Scale="6" Size="0" SourceColumn="Longitude" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Website" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Website" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Website" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="AttractionId" DataSetColumn="AttractionId" />
              <Mapping SourceColumn="Name" DataSetColumn="Name" />
              <Mapping SourceColumn="Type" DataSetColumn="Type" />
              <Mapping SourceColumn="Description" DataSetColumn="Description" />
              <Mapping SourceColumn="AddressLine1" DataSetColumn="AddressLine1" />
              <Mapping SourceColumn="AddressLine2" DataSetColumn="AddressLine2" />
              <Mapping SourceColumn="City" DataSetColumn="City" />
              <Mapping SourceColumn="StateRegion" DataSetColumn="StateRegion" />
              <Mapping SourceColumn="PostalCode" DataSetColumn="PostalCode" />
              <Mapping SourceColumn="Country" DataSetColumn="Country" />
              <Mapping SourceColumn="Latitude" DataSetColumn="Latitude" />
              <Mapping SourceColumn="Longitude" DataSetColumn="Longitude" />
              <Mapping SourceColumn="Website" DataSetColumn="Website" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="VehicleTableAdapter" GeneratorDataComponentClassName="VehicleTableAdapter" Name="Vehicle" UserDataComponentName="VehicleTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString (Settings)" DbObjectName="Tourun.dbo.Vehicle" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Vehicle] WHERE (([VehicleId] = @Original_VehicleId) AND ([Type] = @Original_Type) AND ((@IsNull_Make = 1 AND [Make] IS NULL) OR ([Make] = @Original_Make)) AND ((@IsNull_Model = 1 AND [Model] IS NULL) OR ([Model] = @Original_Model)) AND ([PlateNumber] = @Original_PlateNumber) AND ((@IsNull_Capacity = 1 AND [Capacity] IS NULL) OR ([Capacity] = @Original_Capacity)) AND ((@IsNull_ProviderCompany = 1 AND [ProviderCompany] IS NULL) OR ([ProviderCompany] = @Original_ProviderCompany)) AND ((@IsNull_ContactPhone = 1 AND [ContactPhone] IS NULL) OR ([ContactPhone] = @Original_ContactPhone)) AND ([CreatedAt] = @Original_CreatedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_VehicleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Type" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Make" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Make" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Make" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Make" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Model" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Model" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Model" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Model" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_PlateNumber" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PlateNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Capacity" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Capacity" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ProviderCompany" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProviderCompany" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ProviderCompany" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ProviderCompany" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ContactPhone" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ContactPhone" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ContactPhone" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ContactPhone" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Vehicle] ([VehicleId], [Type], [Make], [Model], [PlateNumber], [Capacity], [ProviderCompany], [ContactPhone], [CreatedAt]) VALUES (@VehicleId, @Type, @Make, @Model, @PlateNumber, @Capacity, @ProviderCompany, @ContactPhone, @CreatedAt);
SELECT VehicleId, Type, Make, Model, PlateNumber, Capacity, ProviderCompany, ContactPhone, CreatedAt FROM Vehicle WHERE (VehicleId = @VehicleId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@VehicleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Type" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Make" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Make" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Model" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Model" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@PlateNumber" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PlateNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Capacity" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ProviderCompany" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ProviderCompany" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ContactPhone" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ContactPhone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT VehicleId, Type, Make, Model, PlateNumber, Capacity, ProviderCompany, ContactPhone, CreatedAt FROM dbo.Vehicle</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Vehicle] SET [VehicleId] = @VehicleId, [Type] = @Type, [Make] = @Make, [Model] = @Model, [PlateNumber] = @PlateNumber, [Capacity] = @Capacity, [ProviderCompany] = @ProviderCompany, [ContactPhone] = @ContactPhone, [CreatedAt] = @CreatedAt WHERE (([VehicleId] = @Original_VehicleId) AND ([Type] = @Original_Type) AND ((@IsNull_Make = 1 AND [Make] IS NULL) OR ([Make] = @Original_Make)) AND ((@IsNull_Model = 1 AND [Model] IS NULL) OR ([Model] = @Original_Model)) AND ([PlateNumber] = @Original_PlateNumber) AND ((@IsNull_Capacity = 1 AND [Capacity] IS NULL) OR ([Capacity] = @Original_Capacity)) AND ((@IsNull_ProviderCompany = 1 AND [ProviderCompany] IS NULL) OR ([ProviderCompany] = @Original_ProviderCompany)) AND ((@IsNull_ContactPhone = 1 AND [ContactPhone] IS NULL) OR ([ContactPhone] = @Original_ContactPhone)) AND ([CreatedAt] = @Original_CreatedAt));
SELECT VehicleId, Type, Make, Model, PlateNumber, Capacity, ProviderCompany, ContactPhone, CreatedAt FROM Vehicle WHERE (VehicleId = @VehicleId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@VehicleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Type" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Make" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Make" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Model" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Model" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@PlateNumber" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PlateNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Capacity" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ProviderCompany" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ProviderCompany" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ContactPhone" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ContactPhone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_VehicleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Type" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Type" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Make" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Make" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Make" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Make" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Model" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Model" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Model" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Model" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_PlateNumber" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PlateNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Capacity" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Capacity" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ProviderCompany" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProviderCompany" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ProviderCompany" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ProviderCompany" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ContactPhone" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ContactPhone" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_ContactPhone" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ContactPhone" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="VehicleId" DataSetColumn="VehicleId" />
              <Mapping SourceColumn="Type" DataSetColumn="Type" />
              <Mapping SourceColumn="Make" DataSetColumn="Make" />
              <Mapping SourceColumn="Model" DataSetColumn="Model" />
              <Mapping SourceColumn="PlateNumber" DataSetColumn="PlateNumber" />
              <Mapping SourceColumn="Capacity" DataSetColumn="Capacity" />
              <Mapping SourceColumn="ProviderCompany" DataSetColumn="ProviderCompany" />
              <Mapping SourceColumn="ContactPhone" DataSetColumn="ContactPhone" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CoordinatorTableAdapter" GeneratorDataComponentClassName="CoordinatorTableAdapter" Name="Coordinator" UserDataComponentName="CoordinatorTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString (Settings)" DbObjectName="Tourun.dbo.Coordinator" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Coordinator] WHERE (([CoordinatorId] = @Original_CoordinatorId) AND ([UserId] = @Original_UserId) AND ((@IsNull_HireDate = 1 AND [HireDate] IS NULL) OR ([HireDate] = @Original_HireDate)) AND ((@IsNull_Notes = 1 AND [Notes] IS NULL) OR ([Notes] = @Original_Notes)) AND ([CreatedAt] = @Original_CreatedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_CoordinatorId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_UserId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_HireDate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_HireDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Notes" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Coordinator] ([CoordinatorId], [UserId], [HireDate], [Notes], [CreatedAt]) VALUES (@CoordinatorId, @UserId, @HireDate, @Notes, @CreatedAt);
SELECT CoordinatorId, UserId, HireDate, Notes, CreatedAt FROM Coordinator WHERE (CoordinatorId = @CoordinatorId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@CoordinatorId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@UserId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@HireDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT CoordinatorId, UserId, HireDate, Notes, CreatedAt FROM dbo.Coordinator</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Coordinator] SET [CoordinatorId] = @CoordinatorId, [UserId] = @UserId, [HireDate] = @HireDate, [Notes] = @Notes, [CreatedAt] = @CreatedAt WHERE (([CoordinatorId] = @Original_CoordinatorId) AND ([UserId] = @Original_UserId) AND ((@IsNull_HireDate = 1 AND [HireDate] IS NULL) OR ([HireDate] = @Original_HireDate)) AND ((@IsNull_Notes = 1 AND [Notes] IS NULL) OR ([Notes] = @Original_Notes)) AND ([CreatedAt] = @Original_CreatedAt));
SELECT CoordinatorId, UserId, HireDate, Notes, CreatedAt FROM Coordinator WHERE (CoordinatorId = @CoordinatorId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@CoordinatorId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@UserId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@HireDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_CoordinatorId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_UserId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_HireDate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_HireDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="HireDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Notes" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="CoordinatorId" DataSetColumn="CoordinatorId" />
              <Mapping SourceColumn="UserId" DataSetColumn="UserId" />
              <Mapping SourceColumn="HireDate" DataSetColumn="HireDate" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="RoleTableAdapter" GeneratorDataComponentClassName="RoleTableAdapter" Name="Role" UserDataComponentName="RoleTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString (Settings)" DbObjectName="Tourun.dbo.Role" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Role] WHERE (([RoleId] = @Original_RoleId) AND ([Name] = @Original_Name) AND ((@IsNull_Description = 1 AND [Description] IS NULL) OR ([Description] = @Original_Description)) AND ([CreatedAt] = @Original_CreatedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_RoleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Description" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Description" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Role] ([RoleId], [Name], [Description], [CreatedAt]) VALUES (@RoleId, @Name, @Description, @CreatedAt);
SELECT RoleId, Name, Description, CreatedAt FROM Role WHERE (RoleId = @RoleId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@RoleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Description" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT RoleId, Name, Description, CreatedAt FROM dbo.Role</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Role] SET [RoleId] = @RoleId, [Name] = @Name, [Description] = @Description, [CreatedAt] = @CreatedAt WHERE (([RoleId] = @Original_RoleId) AND ([Name] = @Original_Name) AND ((@IsNull_Description = 1 AND [Description] IS NULL) OR ([Description] = @Original_Description)) AND ([CreatedAt] = @Original_CreatedAt));
SELECT RoleId, Name, Description, CreatedAt FROM Role WHERE (RoleId = @RoleId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@RoleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Description" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_RoleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Description" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Description" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="RoleId" DataSetColumn="RoleId" />
              <Mapping SourceColumn="Name" DataSetColumn="Name" />
              <Mapping SourceColumn="Description" DataSetColumn="Description" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="TourTableAdapter" GeneratorDataComponentClassName="TourTableAdapter" Name="Tour" UserDataComponentName="TourTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString (Settings)" DbObjectName="Tourun.dbo.Tour" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Tour] WHERE (([TourId] = @Original_TourId) AND ([Name] = @Original_Name) AND ((@IsNull_Description = 1 AND [Description] IS NULL) OR ([Description] = @Original_Description)) AND ([StartDate] = @Original_StartDate) AND ([EndDate] = @Original_EndDate) AND ((@IsNull_CoordinatorId = 1 AND [CoordinatorId] IS NULL) OR ([CoordinatorId] = @Original_CoordinatorId)) AND ((@IsNull_MaxParticipants = 1 AND [MaxParticipants] IS NULL) OR ([MaxParticipants] = @Original_MaxParticipants)) AND ((@IsNull_PriceCurrency = 1 AND [PriceCurrency] IS NULL) OR ([PriceCurrency] = @Original_PriceCurrency)) AND ((@IsNull_PriceAmount = 1 AND [PriceAmount] IS NULL) OR ([PriceAmount] = @Original_PriceAmount)) AND ([Status] = @Original_Status) AND ([CreatedAt] = @Original_CreatedAt) AND ([UpdatedAt] = @Original_UpdatedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Description" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Description" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_StartDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="StartDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_EndDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="EndDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_CoordinatorId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_CoordinatorId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_MaxParticipants" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MaxParticipants" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MaxParticipants" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MaxParticipants" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_PriceCurrency" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PriceCurrency" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="@Original_PriceCurrency" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="PriceCurrency" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_PriceAmount" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PriceAmount" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_PriceAmount" Precision="12" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PriceAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Status" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Tour] ([TourId], [Name], [Description], [StartDate], [EndDate], [CoordinatorId], [MaxParticipants], [PriceCurrency], [PriceAmount], [Status], [CreatedAt], [UpdatedAt]) VALUES (@TourId, @Name, @Description, @StartDate, @EndDate, @CoordinatorId, @MaxParticipants, @PriceCurrency, @PriceAmount, @Status, @CreatedAt, @UpdatedAt);
SELECT TourId, Name, Description, StartDate, EndDate, CoordinatorId, MaxParticipants, PriceCurrency, PriceAmount, Status, CreatedAt, UpdatedAt FROM Tour WHERE (TourId = @TourId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Description" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@StartDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="StartDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@EndDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="EndDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@CoordinatorId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MaxParticipants" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MaxParticipants" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="@PriceCurrency" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="PriceCurrency" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@PriceAmount" Precision="12" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PriceAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT TourId, Name, Description, StartDate, EndDate, CoordinatorId, MaxParticipants, PriceCurrency, PriceAmount, Status, CreatedAt, UpdatedAt FROM dbo.Tour</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Tour] SET [TourId] = @TourId, [Name] = @Name, [Description] = @Description, [StartDate] = @StartDate, [EndDate] = @EndDate, [CoordinatorId] = @CoordinatorId, [MaxParticipants] = @MaxParticipants, [PriceCurrency] = @PriceCurrency, [PriceAmount] = @PriceAmount, [Status] = @Status, [CreatedAt] = @CreatedAt, [UpdatedAt] = @UpdatedAt WHERE (([TourId] = @Original_TourId) AND ([Name] = @Original_Name) AND ((@IsNull_Description = 1 AND [Description] IS NULL) OR ([Description] = @Original_Description)) AND ([StartDate] = @Original_StartDate) AND ([EndDate] = @Original_EndDate) AND ((@IsNull_CoordinatorId = 1 AND [CoordinatorId] IS NULL) OR ([CoordinatorId] = @Original_CoordinatorId)) AND ((@IsNull_MaxParticipants = 1 AND [MaxParticipants] IS NULL) OR ([MaxParticipants] = @Original_MaxParticipants)) AND ((@IsNull_PriceCurrency = 1 AND [PriceCurrency] IS NULL) OR ([PriceCurrency] = @Original_PriceCurrency)) AND ((@IsNull_PriceAmount = 1 AND [PriceAmount] IS NULL) OR ([PriceAmount] = @Original_PriceAmount)) AND ([Status] = @Original_Status) AND ([CreatedAt] = @Original_CreatedAt) AND ([UpdatedAt] = @Original_UpdatedAt));
SELECT TourId, Name, Description, StartDate, EndDate, CoordinatorId, MaxParticipants, PriceCurrency, PriceAmount, Status, CreatedAt, UpdatedAt FROM Tour WHERE (TourId = @TourId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Description" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@StartDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="StartDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@EndDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="EndDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@CoordinatorId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MaxParticipants" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MaxParticipants" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="@PriceCurrency" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="PriceCurrency" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@PriceAmount" Precision="12" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PriceAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Description" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Description" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_StartDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="StartDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_EndDate" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="EndDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_CoordinatorId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_CoordinatorId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="CoordinatorId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_MaxParticipants" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MaxParticipants" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MaxParticipants" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MaxParticipants" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_PriceCurrency" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PriceCurrency" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="@Original_PriceCurrency" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="PriceCurrency" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_PriceAmount" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PriceAmount" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_PriceAmount" Precision="12" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PriceAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Status" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="TourId" DataSetColumn="TourId" />
              <Mapping SourceColumn="Name" DataSetColumn="Name" />
              <Mapping SourceColumn="Description" DataSetColumn="Description" />
              <Mapping SourceColumn="StartDate" DataSetColumn="StartDate" />
              <Mapping SourceColumn="EndDate" DataSetColumn="EndDate" />
              <Mapping SourceColumn="CoordinatorId" DataSetColumn="CoordinatorId" />
              <Mapping SourceColumn="MaxParticipants" DataSetColumn="MaxParticipants" />
              <Mapping SourceColumn="PriceCurrency" DataSetColumn="PriceCurrency" />
              <Mapping SourceColumn="PriceAmount" DataSetColumn="PriceAmount" />
              <Mapping SourceColumn="Status" DataSetColumn="Status" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
              <Mapping SourceColumn="UpdatedAt" DataSetColumn="UpdatedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="TourAccommodationTableAdapter" GeneratorDataComponentClassName="TourAccommodationTableAdapter" Name="TourAccommodation" UserDataComponentName="TourAccommodationTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString (Settings)" DbObjectName="Tourun.dbo.TourAccommodation" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[TourAccommodation] WHERE (([TourAccommodationId] = @Original_TourAccommodationId) AND ([TourId] = @Original_TourId) AND ([AccommodationId] = @Original_AccommodationId) AND ([CheckIn] = @Original_CheckIn) AND ([CheckOut] = @Original_CheckOut) AND ((@IsNull_Notes = 1 AND [Notes] IS NULL) OR ([Notes] = @Original_Notes)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourAccommodationId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourAccommodationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_AccommodationId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_CheckIn" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckIn" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_CheckOut" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckOut" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Notes" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[TourAccommodation] ([TourAccommodationId], [TourId], [AccommodationId], [CheckIn], [CheckOut], [Notes]) VALUES (@TourAccommodationId, @TourId, @AccommodationId, @CheckIn, @CheckOut, @Notes);
SELECT TourAccommodationId, TourId, AccommodationId, CheckIn, CheckOut, Notes FROM TourAccommodation WHERE (TourAccommodationId = @TourAccommodationId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourAccommodationId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourAccommodationId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@AccommodationId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@CheckIn" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckIn" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@CheckOut" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckOut" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT TourAccommodationId, TourId, AccommodationId, CheckIn, CheckOut, Notes FROM dbo.TourAccommodation</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[TourAccommodation] SET [TourAccommodationId] = @TourAccommodationId, [TourId] = @TourId, [AccommodationId] = @AccommodationId, [CheckIn] = @CheckIn, [CheckOut] = @CheckOut, [Notes] = @Notes WHERE (([TourAccommodationId] = @Original_TourAccommodationId) AND ([TourId] = @Original_TourId) AND ([AccommodationId] = @Original_AccommodationId) AND ([CheckIn] = @Original_CheckIn) AND ([CheckOut] = @Original_CheckOut) AND ((@IsNull_Notes = 1 AND [Notes] IS NULL) OR ([Notes] = @Original_Notes)));
SELECT TourAccommodationId, TourId, AccommodationId, CheckIn, CheckOut, Notes FROM TourAccommodation WHERE (TourAccommodationId = @TourAccommodationId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourAccommodationId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourAccommodationId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@AccommodationId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@CheckIn" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckIn" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@CheckOut" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckOut" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourAccommodationId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourAccommodationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_AccommodationId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AccommodationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_CheckIn" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckIn" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_CheckOut" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="CheckOut" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Notes" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="TourAccommodationId" DataSetColumn="TourAccommodationId" />
              <Mapping SourceColumn="TourId" DataSetColumn="TourId" />
              <Mapping SourceColumn="AccommodationId" DataSetColumn="AccommodationId" />
              <Mapping SourceColumn="CheckIn" DataSetColumn="CheckIn" />
              <Mapping SourceColumn="CheckOut" DataSetColumn="CheckOut" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="TourAttractionTableAdapter" GeneratorDataComponentClassName="TourAttractionTableAdapter" Name="TourAttraction" UserDataComponentName="TourAttractionTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString (Settings)" DbObjectName="Tourun.dbo.TourAttraction" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[TourAttraction] WHERE (([TourAttractionId] = @Original_TourAttractionId) AND ([TourId] = @Original_TourId) AND ([AttractionId] = @Original_AttractionId) AND ((@IsNull_VisitDateTime = 1 AND [VisitDateTime] IS NULL) OR ([VisitDateTime] = @Original_VisitDateTime)) AND ((@IsNull_SequenceNumber = 1 AND [SequenceNumber] IS NULL) OR ([SequenceNumber] = @Original_SequenceNumber)) AND ((@IsNull_Notes = 1 AND [Notes] IS NULL) OR ([Notes] = @Original_Notes)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourAttractionId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourAttractionId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_AttractionId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_VisitDateTime" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="VisitDateTime" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_VisitDateTime" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="VisitDateTime" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_SequenceNumber" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SequenceNumber" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_SequenceNumber" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="SequenceNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Notes" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[TourAttraction] ([TourAttractionId], [TourId], [AttractionId], [VisitDateTime], [SequenceNumber], [Notes]) VALUES (@TourAttractionId, @TourId, @AttractionId, @VisitDateTime, @SequenceNumber, @Notes);
SELECT TourAttractionId, TourId, AttractionId, VisitDateTime, SequenceNumber, Notes FROM TourAttraction WHERE (TourAttractionId = @TourAttractionId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourAttractionId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourAttractionId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@AttractionId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@VisitDateTime" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="VisitDateTime" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@SequenceNumber" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="SequenceNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT TourAttractionId, TourId, AttractionId, VisitDateTime, SequenceNumber, Notes FROM dbo.TourAttraction</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[TourAttraction] SET [TourAttractionId] = @TourAttractionId, [TourId] = @TourId, [AttractionId] = @AttractionId, [VisitDateTime] = @VisitDateTime, [SequenceNumber] = @SequenceNumber, [Notes] = @Notes WHERE (([TourAttractionId] = @Original_TourAttractionId) AND ([TourId] = @Original_TourId) AND ([AttractionId] = @Original_AttractionId) AND ((@IsNull_VisitDateTime = 1 AND [VisitDateTime] IS NULL) OR ([VisitDateTime] = @Original_VisitDateTime)) AND ((@IsNull_SequenceNumber = 1 AND [SequenceNumber] IS NULL) OR ([SequenceNumber] = @Original_SequenceNumber)) AND ((@IsNull_Notes = 1 AND [Notes] IS NULL) OR ([Notes] = @Original_Notes)));
SELECT TourAttractionId, TourId, AttractionId, VisitDateTime, SequenceNumber, Notes FROM TourAttraction WHERE (TourAttractionId = @TourAttractionId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourAttractionId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourAttractionId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@AttractionId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@VisitDateTime" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="VisitDateTime" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@SequenceNumber" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="SequenceNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourAttractionId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourAttractionId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_AttractionId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="AttractionId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_VisitDateTime" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="VisitDateTime" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_VisitDateTime" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="VisitDateTime" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_SequenceNumber" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SequenceNumber" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_SequenceNumber" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="SequenceNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Notes" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="TourAttractionId" DataSetColumn="TourAttractionId" />
              <Mapping SourceColumn="TourId" DataSetColumn="TourId" />
              <Mapping SourceColumn="AttractionId" DataSetColumn="AttractionId" />
              <Mapping SourceColumn="VisitDateTime" DataSetColumn="VisitDateTime" />
              <Mapping SourceColumn="SequenceNumber" DataSetColumn="SequenceNumber" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="TourVehicleTableAdapter" GeneratorDataComponentClassName="TourVehicleTableAdapter" Name="TourVehicle" UserDataComponentName="TourVehicleTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString (Settings)" DbObjectName="Tourun.dbo.TourVehicle" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[TourVehicle] WHERE (([TourVehicleId] = @Original_TourVehicleId) AND ([TourId] = @Original_TourId) AND ([VehicleId] = @Original_VehicleId) AND ((@IsNull_AssignedFrom = 1 AND [AssignedFrom] IS NULL) OR ([AssignedFrom] = @Original_AssignedFrom)) AND ((@IsNull_AssignedTo = 1 AND [AssignedTo] IS NULL) OR ([AssignedTo] = @Original_AssignedTo)) AND ((@IsNull_DriverName = 1 AND [DriverName] IS NULL) OR ([DriverName] = @Original_DriverName)) AND ((@IsNull_Notes = 1 AND [Notes] IS NULL) OR ([Notes] = @Original_Notes)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourVehicleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourVehicleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_VehicleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AssignedFrom" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AssignedFrom" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_AssignedFrom" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedFrom" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AssignedTo" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AssignedTo" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_AssignedTo" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedTo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_DriverName" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="DriverName" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_DriverName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="DriverName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Notes" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[TourVehicle] ([TourVehicleId], [TourId], [VehicleId], [AssignedFrom], [AssignedTo], [DriverName], [Notes]) VALUES (@TourVehicleId, @TourId, @VehicleId, @AssignedFrom, @AssignedTo, @DriverName, @Notes);
SELECT TourVehicleId, TourId, VehicleId, AssignedFrom, AssignedTo, DriverName, Notes FROM TourVehicle WHERE (TourVehicleId = @TourVehicleId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourVehicleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourVehicleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@VehicleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@AssignedFrom" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedFrom" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@AssignedTo" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedTo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@DriverName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="DriverName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT TourVehicleId, TourId, VehicleId, AssignedFrom, AssignedTo, DriverName, Notes FROM dbo.TourVehicle</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[TourVehicle] SET [TourVehicleId] = @TourVehicleId, [TourId] = @TourId, [VehicleId] = @VehicleId, [AssignedFrom] = @AssignedFrom, [AssignedTo] = @AssignedTo, [DriverName] = @DriverName, [Notes] = @Notes WHERE (([TourVehicleId] = @Original_TourVehicleId) AND ([TourId] = @Original_TourId) AND ([VehicleId] = @Original_VehicleId) AND ((@IsNull_AssignedFrom = 1 AND [AssignedFrom] IS NULL) OR ([AssignedFrom] = @Original_AssignedFrom)) AND ((@IsNull_AssignedTo = 1 AND [AssignedTo] IS NULL) OR ([AssignedTo] = @Original_AssignedTo)) AND ((@IsNull_DriverName = 1 AND [DriverName] IS NULL) OR ([DriverName] = @Original_DriverName)) AND ((@IsNull_Notes = 1 AND [Notes] IS NULL) OR ([Notes] = @Original_Notes)));
SELECT TourVehicleId, TourId, VehicleId, AssignedFrom, AssignedTo, DriverName, Notes FROM TourVehicle WHERE (TourVehicleId = @TourVehicleId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourVehicleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourVehicleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@VehicleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@AssignedFrom" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedFrom" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@AssignedTo" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedTo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@DriverName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="DriverName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourVehicleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourVehicleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_TourId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="TourId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_VehicleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="VehicleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AssignedFrom" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AssignedFrom" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_AssignedFrom" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedFrom" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_AssignedTo" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AssignedTo" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Date" Direction="Input" ParameterName="@Original_AssignedTo" Precision="0" ProviderType="Date" Scale="0" Size="0" SourceColumn="AssignedTo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_DriverName" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="DriverName" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_DriverName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="DriverName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Notes" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="TourVehicleId" DataSetColumn="TourVehicleId" />
              <Mapping SourceColumn="TourId" DataSetColumn="TourId" />
              <Mapping SourceColumn="VehicleId" DataSetColumn="VehicleId" />
              <Mapping SourceColumn="AssignedFrom" DataSetColumn="AssignedFrom" />
              <Mapping SourceColumn="AssignedTo" DataSetColumn="AssignedTo" />
              <Mapping SourceColumn="DriverName" DataSetColumn="DriverName" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="UserTableAdapter" GeneratorDataComponentClassName="UserTableAdapter" Name="User" UserDataComponentName="UserTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString (Settings)" DbObjectName="Tourun.dbo.[User]" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[User] WHERE (([UserId] = @Original_UserId) AND ([Username] = @Original_Username) AND ([Email] = @Original_Email) AND ([PasswordHash] = @Original_PasswordHash) AND ([FirstName] = @Original_FirstName) AND ([LastName] = @Original_LastName) AND ((@IsNull_Phone = 1 AND [Phone] IS NULL) OR ([Phone] = @Original_Phone)) AND ([IsActive] = @Original_IsActive) AND ([CreatedAt] = @Original_CreatedAt) AND ([UpdatedAt] = @Original_UpdatedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_UserId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Username" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Username" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Email" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_PasswordHash" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PasswordHash" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_FirstName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_LastName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Phone" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Phone" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_IsActive" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsActive" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[User] ([UserId], [Username], [Email], [PasswordHash], [FirstName], [LastName], [Phone], [IsActive], [CreatedAt], [UpdatedAt]) VALUES (@UserId, @Username, @Email, @PasswordHash, @FirstName, @LastName, @Phone, @IsActive, @CreatedAt, @UpdatedAt);
SELECT UserId, Username, Email, PasswordHash, FirstName, LastName, Phone, IsActive, CreatedAt, UpdatedAt FROM [User] WHERE (UserId = @UserId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@UserId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Username" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Username" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@PasswordHash" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PasswordHash" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@FirstName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@LastName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Phone" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@IsActive" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsActive" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT UserId, Username, Email, PasswordHash, FirstName, LastName, Phone, IsActive, CreatedAt, UpdatedAt FROM dbo.[User]</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[User] SET [UserId] = @UserId, [Username] = @Username, [Email] = @Email, [PasswordHash] = @PasswordHash, [FirstName] = @FirstName, [LastName] = @LastName, [Phone] = @Phone, [IsActive] = @IsActive, [CreatedAt] = @CreatedAt, [UpdatedAt] = @UpdatedAt WHERE (([UserId] = @Original_UserId) AND ([Username] = @Original_Username) AND ([Email] = @Original_Email) AND ([PasswordHash] = @Original_PasswordHash) AND ([FirstName] = @Original_FirstName) AND ([LastName] = @Original_LastName) AND ((@IsNull_Phone = 1 AND [Phone] IS NULL) OR ([Phone] = @Original_Phone)) AND ([IsActive] = @Original_IsActive) AND ([CreatedAt] = @Original_CreatedAt) AND ([UpdatedAt] = @Original_UpdatedAt));
SELECT UserId, Username, Email, PasswordHash, FirstName, LastName, Phone, IsActive, CreatedAt, UpdatedAt FROM [User] WHERE (UserId = @UserId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@UserId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Username" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Username" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@PasswordHash" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PasswordHash" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@FirstName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@LastName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Phone" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@IsActive" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsActive" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_UserId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Username" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Username" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Email" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_PasswordHash" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PasswordHash" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_FirstName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_LastName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Phone" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Phone" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Phone" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_IsActive" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsActive" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_CreatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="CreatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_UpdatedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="UpdatedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="UserId" DataSetColumn="UserId" />
              <Mapping SourceColumn="Username" DataSetColumn="Username" />
              <Mapping SourceColumn="Email" DataSetColumn="Email" />
              <Mapping SourceColumn="PasswordHash" DataSetColumn="PasswordHash" />
              <Mapping SourceColumn="FirstName" DataSetColumn="FirstName" />
              <Mapping SourceColumn="LastName" DataSetColumn="LastName" />
              <Mapping SourceColumn="Phone" DataSetColumn="Phone" />
              <Mapping SourceColumn="IsActive" DataSetColumn="IsActive" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
              <Mapping SourceColumn="UpdatedAt" DataSetColumn="UpdatedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="UserRoleTableAdapter" GeneratorDataComponentClassName="UserRoleTableAdapter" Name="UserRole" UserDataComponentName="UserRoleTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="TourunConnectionString (Settings)" DbObjectName="Tourun.dbo.UserRole" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[UserRole] WHERE (([UserRoleId] = @Original_UserRoleId) AND ([UserId] = @Original_UserId) AND ([RoleId] = @Original_RoleId) AND ([AssignedAt] = @Original_AssignedAt))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_UserRoleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserRoleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_UserId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_RoleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_AssignedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="AssignedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[UserRole] ([UserRoleId], [UserId], [RoleId], [AssignedAt]) VALUES (@UserRoleId, @UserId, @RoleId, @AssignedAt);
SELECT UserRoleId, UserId, RoleId, AssignedAt FROM UserRole WHERE (UserRoleId = @UserRoleId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@UserRoleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserRoleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@UserId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@RoleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@AssignedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="AssignedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT UserRoleId, UserId, RoleId, AssignedAt FROM dbo.UserRole</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[UserRole] SET [UserRoleId] = @UserRoleId, [UserId] = @UserId, [RoleId] = @RoleId, [AssignedAt] = @AssignedAt WHERE (([UserRoleId] = @Original_UserRoleId) AND ([UserId] = @Original_UserId) AND ([RoleId] = @Original_RoleId) AND ([AssignedAt] = @Original_AssignedAt));
SELECT UserRoleId, UserId, RoleId, AssignedAt FROM UserRole WHERE (UserRoleId = @UserRoleId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@UserRoleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserRoleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@UserId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@RoleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@AssignedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="AssignedAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_UserRoleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserRoleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_UserId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_RoleId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@Original_AssignedAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="AssignedAt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="UserRoleId" DataSetColumn="UserRoleId" />
              <Mapping SourceColumn="UserId" DataSetColumn="UserId" />
              <Mapping SourceColumn="RoleId" DataSetColumn="RoleId" />
              <Mapping SourceColumn="AssignedAt" DataSetColumn="AssignedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="TourunDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_UserDSName="TourunDataSet" msprop:Generator_DataSetName="TourunDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Accommodation" msprop:Generator_RowEvHandlerName="AccommodationRowChangeEventHandler" msprop:Generator_RowDeletedName="AccommodationRowDeleted" msprop:Generator_RowDeletingName="AccommodationRowDeleting" msprop:Generator_RowEvArgName="AccommodationRowChangeEvent" msprop:Generator_TablePropName="Accommodation" msprop:Generator_RowChangedName="AccommodationRowChanged" msprop:Generator_RowChangingName="AccommodationRowChanging" msprop:Generator_TableClassName="AccommodationDataTable" msprop:Generator_RowClassName="AccommodationRow" msprop:Generator_TableVarName="tableAccommodation" msprop:Generator_UserTableName="Accommodation">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AccommodationId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="AccommodationId" msprop:Generator_ColumnPropNameInTable="AccommodationIdColumn" msprop:Generator_ColumnVarNameInTable="columnAccommodationId" msprop:Generator_UserColumnName="AccommodationId" type="xs:string" />
              <xs:element name="Name" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_ColumnVarNameInTable="columnName" msprop:Generator_UserColumnName="Name">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Type" msprop:Generator_ColumnPropNameInRow="Type" msprop:Generator_ColumnPropNameInTable="TypeColumn" msprop:Generator_ColumnVarNameInTable="columnType" msprop:Generator_UserColumnName="Type" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AddressLine1" msprop:Generator_ColumnPropNameInRow="AddressLine1" msprop:Generator_ColumnPropNameInTable="AddressLine1Column" msprop:Generator_ColumnVarNameInTable="columnAddressLine1" msprop:Generator_UserColumnName="AddressLine1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AddressLine2" msprop:Generator_ColumnPropNameInRow="AddressLine2" msprop:Generator_ColumnPropNameInTable="AddressLine2Column" msprop:Generator_ColumnVarNameInTable="columnAddressLine2" msprop:Generator_UserColumnName="AddressLine2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="City" msprop:Generator_ColumnPropNameInRow="City" msprop:Generator_ColumnPropNameInTable="CityColumn" msprop:Generator_ColumnVarNameInTable="columnCity" msprop:Generator_UserColumnName="City" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="StateRegion" msprop:Generator_ColumnPropNameInRow="StateRegion" msprop:Generator_ColumnPropNameInTable="StateRegionColumn" msprop:Generator_ColumnVarNameInTable="columnStateRegion" msprop:Generator_UserColumnName="StateRegion" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PostalCode" msprop:Generator_ColumnPropNameInRow="PostalCode" msprop:Generator_ColumnPropNameInTable="PostalCodeColumn" msprop:Generator_ColumnVarNameInTable="columnPostalCode" msprop:Generator_UserColumnName="PostalCode" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Country" msprop:Generator_ColumnPropNameInRow="Country" msprop:Generator_ColumnPropNameInTable="CountryColumn" msprop:Generator_ColumnVarNameInTable="columnCountry" msprop:Generator_UserColumnName="Country" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Phone" msprop:Generator_ColumnPropNameInRow="Phone" msprop:Generator_ColumnPropNameInTable="PhoneColumn" msprop:Generator_ColumnVarNameInTable="columnPhone" msprop:Generator_UserColumnName="Phone" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Email" msprop:Generator_ColumnPropNameInRow="Email" msprop:Generator_ColumnPropNameInTable="EmailColumn" msprop:Generator_ColumnVarNameInTable="columnEmail" msprop:Generator_UserColumnName="Email" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Website" msprop:Generator_ColumnPropNameInRow="Website" msprop:Generator_ColumnPropNameInTable="WebsiteColumn" msprop:Generator_ColumnVarNameInTable="columnWebsite" msprop:Generator_UserColumnName="Website" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Rating" msprop:Generator_ColumnPropNameInRow="Rating" msprop:Generator_ColumnPropNameInTable="RatingColumn" msprop:Generator_ColumnVarNameInTable="columnRating" msprop:Generator_UserColumnName="Rating" type="xs:decimal" minOccurs="0" />
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" msprop:Generator_UserColumnName="CreatedAt" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Attraction" msprop:Generator_RowEvHandlerName="AttractionRowChangeEventHandler" msprop:Generator_RowDeletedName="AttractionRowDeleted" msprop:Generator_RowDeletingName="AttractionRowDeleting" msprop:Generator_RowEvArgName="AttractionRowChangeEvent" msprop:Generator_TablePropName="Attraction" msprop:Generator_RowChangedName="AttractionRowChanged" msprop:Generator_RowChangingName="AttractionRowChanging" msprop:Generator_TableClassName="AttractionDataTable" msprop:Generator_RowClassName="AttractionRow" msprop:Generator_TableVarName="tableAttraction" msprop:Generator_UserTableName="Attraction">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AttractionId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="AttractionId" msprop:Generator_ColumnPropNameInTable="AttractionIdColumn" msprop:Generator_ColumnVarNameInTable="columnAttractionId" msprop:Generator_UserColumnName="AttractionId" type="xs:string" />
              <xs:element name="Name" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_ColumnVarNameInTable="columnName" msprop:Generator_UserColumnName="Name">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Type" msprop:Generator_ColumnPropNameInRow="Type" msprop:Generator_ColumnPropNameInTable="TypeColumn" msprop:Generator_ColumnVarNameInTable="columnType" msprop:Generator_UserColumnName="Type" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Description" msprop:Generator_ColumnPropNameInRow="Description" msprop:Generator_ColumnPropNameInTable="DescriptionColumn" msprop:Generator_ColumnVarNameInTable="columnDescription" msprop:Generator_UserColumnName="Description" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AddressLine1" msprop:Generator_ColumnPropNameInRow="AddressLine1" msprop:Generator_ColumnPropNameInTable="AddressLine1Column" msprop:Generator_ColumnVarNameInTable="columnAddressLine1" msprop:Generator_UserColumnName="AddressLine1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AddressLine2" msprop:Generator_ColumnPropNameInRow="AddressLine2" msprop:Generator_ColumnPropNameInTable="AddressLine2Column" msprop:Generator_ColumnVarNameInTable="columnAddressLine2" msprop:Generator_UserColumnName="AddressLine2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="City" msprop:Generator_ColumnPropNameInRow="City" msprop:Generator_ColumnPropNameInTable="CityColumn" msprop:Generator_ColumnVarNameInTable="columnCity" msprop:Generator_UserColumnName="City" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="StateRegion" msprop:Generator_ColumnPropNameInRow="StateRegion" msprop:Generator_ColumnPropNameInTable="StateRegionColumn" msprop:Generator_ColumnVarNameInTable="columnStateRegion" msprop:Generator_UserColumnName="StateRegion" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PostalCode" msprop:Generator_ColumnPropNameInRow="PostalCode" msprop:Generator_ColumnPropNameInTable="PostalCodeColumn" msprop:Generator_ColumnVarNameInTable="columnPostalCode" msprop:Generator_UserColumnName="PostalCode" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Country" msprop:Generator_ColumnPropNameInRow="Country" msprop:Generator_ColumnPropNameInTable="CountryColumn" msprop:Generator_ColumnVarNameInTable="columnCountry" msprop:Generator_UserColumnName="Country" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Latitude" msprop:Generator_ColumnPropNameInRow="Latitude" msprop:Generator_ColumnPropNameInTable="LatitudeColumn" msprop:Generator_ColumnVarNameInTable="columnLatitude" msprop:Generator_UserColumnName="Latitude" type="xs:decimal" minOccurs="0" />
              <xs:element name="Longitude" msprop:Generator_ColumnPropNameInRow="Longitude" msprop:Generator_ColumnPropNameInTable="LongitudeColumn" msprop:Generator_ColumnVarNameInTable="columnLongitude" msprop:Generator_UserColumnName="Longitude" type="xs:decimal" minOccurs="0" />
              <xs:element name="Website" msprop:Generator_ColumnPropNameInRow="Website" msprop:Generator_ColumnPropNameInTable="WebsiteColumn" msprop:Generator_ColumnVarNameInTable="columnWebsite" msprop:Generator_UserColumnName="Website" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" msprop:Generator_UserColumnName="CreatedAt" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Vehicle" msprop:Generator_RowEvHandlerName="VehicleRowChangeEventHandler" msprop:Generator_RowDeletedName="VehicleRowDeleted" msprop:Generator_RowDeletingName="VehicleRowDeleting" msprop:Generator_RowEvArgName="VehicleRowChangeEvent" msprop:Generator_TablePropName="Vehicle" msprop:Generator_RowChangedName="VehicleRowChanged" msprop:Generator_RowChangingName="VehicleRowChanging" msprop:Generator_TableClassName="VehicleDataTable" msprop:Generator_RowClassName="VehicleRow" msprop:Generator_TableVarName="tableVehicle" msprop:Generator_UserTableName="Vehicle">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="VehicleId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="VehicleId" msprop:Generator_ColumnPropNameInTable="VehicleIdColumn" msprop:Generator_ColumnVarNameInTable="columnVehicleId" msprop:Generator_UserColumnName="VehicleId" type="xs:string" />
              <xs:element name="Type" msprop:Generator_ColumnPropNameInRow="Type" msprop:Generator_ColumnPropNameInTable="TypeColumn" msprop:Generator_ColumnVarNameInTable="columnType" msprop:Generator_UserColumnName="Type">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Make" msprop:Generator_ColumnPropNameInRow="Make" msprop:Generator_ColumnPropNameInTable="MakeColumn" msprop:Generator_ColumnVarNameInTable="columnMake" msprop:Generator_UserColumnName="Make" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Model" msprop:Generator_ColumnPropNameInRow="Model" msprop:Generator_ColumnPropNameInTable="ModelColumn" msprop:Generator_ColumnVarNameInTable="columnModel" msprop:Generator_UserColumnName="Model" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PlateNumber" msprop:Generator_ColumnPropNameInRow="PlateNumber" msprop:Generator_ColumnPropNameInTable="PlateNumberColumn" msprop:Generator_ColumnVarNameInTable="columnPlateNumber" msprop:Generator_UserColumnName="PlateNumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Capacity" msprop:Generator_ColumnPropNameInRow="Capacity" msprop:Generator_ColumnPropNameInTable="CapacityColumn" msprop:Generator_ColumnVarNameInTable="columnCapacity" msprop:Generator_UserColumnName="Capacity" type="xs:int" minOccurs="0" />
              <xs:element name="ProviderCompany" msprop:Generator_ColumnPropNameInRow="ProviderCompany" msprop:Generator_ColumnPropNameInTable="ProviderCompanyColumn" msprop:Generator_ColumnVarNameInTable="columnProviderCompany" msprop:Generator_UserColumnName="ProviderCompany" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContactPhone" msprop:Generator_ColumnPropNameInRow="ContactPhone" msprop:Generator_ColumnPropNameInTable="ContactPhoneColumn" msprop:Generator_ColumnVarNameInTable="columnContactPhone" msprop:Generator_UserColumnName="ContactPhone" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" msprop:Generator_UserColumnName="CreatedAt" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Coordinator" msprop:Generator_RowEvHandlerName="CoordinatorRowChangeEventHandler" msprop:Generator_RowDeletedName="CoordinatorRowDeleted" msprop:Generator_RowDeletingName="CoordinatorRowDeleting" msprop:Generator_RowEvArgName="CoordinatorRowChangeEvent" msprop:Generator_TablePropName="Coordinator" msprop:Generator_RowChangedName="CoordinatorRowChanged" msprop:Generator_RowChangingName="CoordinatorRowChanging" msprop:Generator_TableClassName="CoordinatorDataTable" msprop:Generator_RowClassName="CoordinatorRow" msprop:Generator_TableVarName="tableCoordinator" msprop:Generator_UserTableName="Coordinator">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CoordinatorId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="CoordinatorId" msprop:Generator_ColumnPropNameInTable="CoordinatorIdColumn" msprop:Generator_ColumnVarNameInTable="columnCoordinatorId" msprop:Generator_UserColumnName="CoordinatorId" type="xs:string" />
              <xs:element name="UserId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="UserId" msprop:Generator_ColumnPropNameInTable="UserIdColumn" msprop:Generator_ColumnVarNameInTable="columnUserId" msprop:Generator_UserColumnName="UserId" type="xs:string" />
              <xs:element name="HireDate" msprop:Generator_ColumnPropNameInRow="HireDate" msprop:Generator_ColumnPropNameInTable="HireDateColumn" msprop:Generator_ColumnVarNameInTable="columnHireDate" msprop:Generator_UserColumnName="HireDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Notes" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_ColumnVarNameInTable="columnNotes" msprop:Generator_UserColumnName="Notes" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" msprop:Generator_UserColumnName="CreatedAt" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Role" msprop:Generator_RowEvHandlerName="RoleRowChangeEventHandler" msprop:Generator_RowDeletedName="RoleRowDeleted" msprop:Generator_RowDeletingName="RoleRowDeleting" msprop:Generator_RowEvArgName="RoleRowChangeEvent" msprop:Generator_TablePropName="Role" msprop:Generator_RowChangedName="RoleRowChanged" msprop:Generator_RowChangingName="RoleRowChanging" msprop:Generator_TableClassName="RoleDataTable" msprop:Generator_RowClassName="RoleRow" msprop:Generator_TableVarName="tableRole" msprop:Generator_UserTableName="Role">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="RoleId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="RoleId" msprop:Generator_ColumnPropNameInTable="RoleIdColumn" msprop:Generator_ColumnVarNameInTable="columnRoleId" msprop:Generator_UserColumnName="RoleId" type="xs:string" />
              <xs:element name="Name" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_ColumnVarNameInTable="columnName" msprop:Generator_UserColumnName="Name">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Description" msprop:Generator_ColumnPropNameInRow="Description" msprop:Generator_ColumnPropNameInTable="DescriptionColumn" msprop:Generator_ColumnVarNameInTable="columnDescription" msprop:Generator_UserColumnName="Description" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="500" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" msprop:Generator_UserColumnName="CreatedAt" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Tour" msprop:Generator_RowEvHandlerName="TourRowChangeEventHandler" msprop:Generator_RowDeletedName="TourRowDeleted" msprop:Generator_RowDeletingName="TourRowDeleting" msprop:Generator_RowEvArgName="TourRowChangeEvent" msprop:Generator_TablePropName="Tour" msprop:Generator_RowChangedName="TourRowChanged" msprop:Generator_RowChangingName="TourRowChanging" msprop:Generator_TableClassName="TourDataTable" msprop:Generator_RowClassName="TourRow" msprop:Generator_TableVarName="tableTour" msprop:Generator_UserTableName="Tour">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TourId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="TourId" msprop:Generator_ColumnPropNameInTable="TourIdColumn" msprop:Generator_ColumnVarNameInTable="columnTourId" msprop:Generator_UserColumnName="TourId" type="xs:string" />
              <xs:element name="Name" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_ColumnVarNameInTable="columnName" msprop:Generator_UserColumnName="Name">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Description" msprop:Generator_ColumnPropNameInRow="Description" msprop:Generator_ColumnPropNameInTable="DescriptionColumn" msprop:Generator_ColumnVarNameInTable="columnDescription" msprop:Generator_UserColumnName="Description" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="StartDate" msprop:Generator_ColumnPropNameInRow="StartDate" msprop:Generator_ColumnPropNameInTable="StartDateColumn" msprop:Generator_ColumnVarNameInTable="columnStartDate" msprop:Generator_UserColumnName="StartDate" type="xs:dateTime" />
              <xs:element name="EndDate" msprop:Generator_ColumnPropNameInRow="EndDate" msprop:Generator_ColumnPropNameInTable="EndDateColumn" msprop:Generator_ColumnVarNameInTable="columnEndDate" msprop:Generator_UserColumnName="EndDate" type="xs:dateTime" />
              <xs:element name="CoordinatorId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="CoordinatorId" msprop:Generator_ColumnPropNameInTable="CoordinatorIdColumn" msprop:Generator_ColumnVarNameInTable="columnCoordinatorId" msprop:Generator_UserColumnName="CoordinatorId" type="xs:string" minOccurs="0" />
              <xs:element name="MaxParticipants" msprop:Generator_ColumnPropNameInRow="MaxParticipants" msprop:Generator_ColumnPropNameInTable="MaxParticipantsColumn" msprop:Generator_ColumnVarNameInTable="columnMaxParticipants" msprop:Generator_UserColumnName="MaxParticipants" type="xs:int" minOccurs="0" />
              <xs:element name="PriceCurrency" msprop:Generator_ColumnPropNameInRow="PriceCurrency" msprop:Generator_ColumnPropNameInTable="PriceCurrencyColumn" msprop:Generator_ColumnVarNameInTable="columnPriceCurrency" msprop:Generator_UserColumnName="PriceCurrency" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PriceAmount" msprop:Generator_ColumnPropNameInRow="PriceAmount" msprop:Generator_ColumnPropNameInTable="PriceAmountColumn" msprop:Generator_ColumnVarNameInTable="columnPriceAmount" msprop:Generator_UserColumnName="PriceAmount" type="xs:decimal" minOccurs="0" />
              <xs:element name="Status" msprop:Generator_ColumnPropNameInRow="Status" msprop:Generator_ColumnPropNameInTable="StatusColumn" msprop:Generator_ColumnVarNameInTable="columnStatus" msprop:Generator_UserColumnName="Status">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" msprop:Generator_UserColumnName="CreatedAt" type="xs:dateTime" />
              <xs:element name="UpdatedAt" msprop:Generator_ColumnPropNameInRow="UpdatedAt" msprop:Generator_ColumnPropNameInTable="UpdatedAtColumn" msprop:Generator_ColumnVarNameInTable="columnUpdatedAt" msprop:Generator_UserColumnName="UpdatedAt" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="TourAccommodation" msprop:Generator_RowEvHandlerName="TourAccommodationRowChangeEventHandler" msprop:Generator_RowDeletedName="TourAccommodationRowDeleted" msprop:Generator_RowDeletingName="TourAccommodationRowDeleting" msprop:Generator_RowEvArgName="TourAccommodationRowChangeEvent" msprop:Generator_TablePropName="TourAccommodation" msprop:Generator_RowChangedName="TourAccommodationRowChanged" msprop:Generator_RowChangingName="TourAccommodationRowChanging" msprop:Generator_TableClassName="TourAccommodationDataTable" msprop:Generator_RowClassName="TourAccommodationRow" msprop:Generator_TableVarName="tableTourAccommodation" msprop:Generator_UserTableName="TourAccommodation">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TourAccommodationId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="TourAccommodationId" msprop:Generator_ColumnPropNameInTable="TourAccommodationIdColumn" msprop:Generator_ColumnVarNameInTable="columnTourAccommodationId" msprop:Generator_UserColumnName="TourAccommodationId" type="xs:string" />
              <xs:element name="TourId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="TourId" msprop:Generator_ColumnPropNameInTable="TourIdColumn" msprop:Generator_ColumnVarNameInTable="columnTourId" msprop:Generator_UserColumnName="TourId" type="xs:string" />
              <xs:element name="AccommodationId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="AccommodationId" msprop:Generator_ColumnPropNameInTable="AccommodationIdColumn" msprop:Generator_ColumnVarNameInTable="columnAccommodationId" msprop:Generator_UserColumnName="AccommodationId" type="xs:string" />
              <xs:element name="CheckIn" msprop:Generator_ColumnPropNameInRow="CheckIn" msprop:Generator_ColumnPropNameInTable="CheckInColumn" msprop:Generator_ColumnVarNameInTable="columnCheckIn" msprop:Generator_UserColumnName="CheckIn" type="xs:dateTime" />
              <xs:element name="CheckOut" msprop:Generator_ColumnPropNameInRow="CheckOut" msprop:Generator_ColumnPropNameInTable="CheckOutColumn" msprop:Generator_ColumnVarNameInTable="columnCheckOut" msprop:Generator_UserColumnName="CheckOut" type="xs:dateTime" />
              <xs:element name="Notes" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_ColumnVarNameInTable="columnNotes" msprop:Generator_UserColumnName="Notes" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="TourAttraction" msprop:Generator_RowEvHandlerName="TourAttractionRowChangeEventHandler" msprop:Generator_RowDeletedName="TourAttractionRowDeleted" msprop:Generator_RowDeletingName="TourAttractionRowDeleting" msprop:Generator_RowEvArgName="TourAttractionRowChangeEvent" msprop:Generator_TablePropName="TourAttraction" msprop:Generator_RowChangedName="TourAttractionRowChanged" msprop:Generator_RowChangingName="TourAttractionRowChanging" msprop:Generator_TableClassName="TourAttractionDataTable" msprop:Generator_RowClassName="TourAttractionRow" msprop:Generator_TableVarName="tableTourAttraction" msprop:Generator_UserTableName="TourAttraction">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TourAttractionId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="TourAttractionId" msprop:Generator_ColumnPropNameInTable="TourAttractionIdColumn" msprop:Generator_ColumnVarNameInTable="columnTourAttractionId" msprop:Generator_UserColumnName="TourAttractionId" type="xs:string" />
              <xs:element name="TourId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="TourId" msprop:Generator_ColumnPropNameInTable="TourIdColumn" msprop:Generator_ColumnVarNameInTable="columnTourId" msprop:Generator_UserColumnName="TourId" type="xs:string" />
              <xs:element name="AttractionId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="AttractionId" msprop:Generator_ColumnPropNameInTable="AttractionIdColumn" msprop:Generator_ColumnVarNameInTable="columnAttractionId" msprop:Generator_UserColumnName="AttractionId" type="xs:string" />
              <xs:element name="VisitDateTime" msprop:Generator_ColumnPropNameInRow="VisitDateTime" msprop:Generator_ColumnPropNameInTable="VisitDateTimeColumn" msprop:Generator_ColumnVarNameInTable="columnVisitDateTime" msprop:Generator_UserColumnName="VisitDateTime" type="xs:dateTime" minOccurs="0" />
              <xs:element name="SequenceNumber" msprop:Generator_ColumnPropNameInRow="SequenceNumber" msprop:Generator_ColumnPropNameInTable="SequenceNumberColumn" msprop:Generator_ColumnVarNameInTable="columnSequenceNumber" msprop:Generator_UserColumnName="SequenceNumber" type="xs:short" minOccurs="0" />
              <xs:element name="Notes" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_ColumnVarNameInTable="columnNotes" msprop:Generator_UserColumnName="Notes" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="TourVehicle" msprop:Generator_RowEvHandlerName="TourVehicleRowChangeEventHandler" msprop:Generator_RowDeletedName="TourVehicleRowDeleted" msprop:Generator_RowDeletingName="TourVehicleRowDeleting" msprop:Generator_RowEvArgName="TourVehicleRowChangeEvent" msprop:Generator_TablePropName="TourVehicle" msprop:Generator_RowChangedName="TourVehicleRowChanged" msprop:Generator_RowChangingName="TourVehicleRowChanging" msprop:Generator_TableClassName="TourVehicleDataTable" msprop:Generator_RowClassName="TourVehicleRow" msprop:Generator_TableVarName="tableTourVehicle" msprop:Generator_UserTableName="TourVehicle">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TourVehicleId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="TourVehicleId" msprop:Generator_ColumnPropNameInTable="TourVehicleIdColumn" msprop:Generator_ColumnVarNameInTable="columnTourVehicleId" msprop:Generator_UserColumnName="TourVehicleId" type="xs:string" />
              <xs:element name="TourId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="TourId" msprop:Generator_ColumnPropNameInTable="TourIdColumn" msprop:Generator_ColumnVarNameInTable="columnTourId" msprop:Generator_UserColumnName="TourId" type="xs:string" />
              <xs:element name="VehicleId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="VehicleId" msprop:Generator_ColumnPropNameInTable="VehicleIdColumn" msprop:Generator_ColumnVarNameInTable="columnVehicleId" msprop:Generator_UserColumnName="VehicleId" type="xs:string" />
              <xs:element name="AssignedFrom" msprop:Generator_ColumnPropNameInRow="AssignedFrom" msprop:Generator_ColumnPropNameInTable="AssignedFromColumn" msprop:Generator_ColumnVarNameInTable="columnAssignedFrom" msprop:Generator_UserColumnName="AssignedFrom" type="xs:dateTime" minOccurs="0" />
              <xs:element name="AssignedTo" msprop:Generator_ColumnPropNameInRow="AssignedTo" msprop:Generator_ColumnPropNameInTable="AssignedToColumn" msprop:Generator_ColumnVarNameInTable="columnAssignedTo" msprop:Generator_UserColumnName="AssignedTo" type="xs:dateTime" minOccurs="0" />
              <xs:element name="DriverName" msprop:Generator_ColumnPropNameInRow="DriverName" msprop:Generator_ColumnPropNameInTable="DriverNameColumn" msprop:Generator_ColumnVarNameInTable="columnDriverName" msprop:Generator_UserColumnName="DriverName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Notes" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_ColumnVarNameInTable="columnNotes" msprop:Generator_UserColumnName="Notes" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="User" msprop:Generator_RowEvHandlerName="UserRowChangeEventHandler" msprop:Generator_RowDeletedName="UserRowDeleted" msprop:Generator_RowDeletingName="UserRowDeleting" msprop:Generator_RowEvArgName="UserRowChangeEvent" msprop:Generator_TablePropName="User" msprop:Generator_RowChangedName="UserRowChanged" msprop:Generator_RowChangingName="UserRowChanging" msprop:Generator_TableClassName="UserDataTable" msprop:Generator_RowClassName="UserRow" msprop:Generator_TableVarName="tableUser" msprop:Generator_UserTableName="User">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="UserId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="UserId" msprop:Generator_ColumnPropNameInTable="UserIdColumn" msprop:Generator_ColumnVarNameInTable="columnUserId" msprop:Generator_UserColumnName="UserId" type="xs:string" />
              <xs:element name="Username" msprop:Generator_ColumnPropNameInRow="Username" msprop:Generator_ColumnPropNameInTable="UsernameColumn" msprop:Generator_ColumnVarNameInTable="columnUsername" msprop:Generator_UserColumnName="Username">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Email" msprop:Generator_ColumnPropNameInRow="Email" msprop:Generator_ColumnPropNameInTable="EmailColumn" msprop:Generator_ColumnVarNameInTable="columnEmail" msprop:Generator_UserColumnName="Email">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PasswordHash" msprop:Generator_ColumnPropNameInRow="PasswordHash" msprop:Generator_ColumnPropNameInTable="PasswordHashColumn" msprop:Generator_ColumnVarNameInTable="columnPasswordHash" msprop:Generator_UserColumnName="PasswordHash">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FirstName" msprop:Generator_ColumnPropNameInRow="FirstName" msprop:Generator_ColumnPropNameInTable="FirstNameColumn" msprop:Generator_ColumnVarNameInTable="columnFirstName" msprop:Generator_UserColumnName="FirstName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LastName" msprop:Generator_ColumnPropNameInRow="LastName" msprop:Generator_ColumnPropNameInTable="LastNameColumn" msprop:Generator_ColumnVarNameInTable="columnLastName" msprop:Generator_UserColumnName="LastName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Phone" msprop:Generator_ColumnPropNameInRow="Phone" msprop:Generator_ColumnPropNameInTable="PhoneColumn" msprop:Generator_ColumnVarNameInTable="columnPhone" msprop:Generator_UserColumnName="Phone" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="IsActive" msprop:Generator_ColumnPropNameInRow="IsActive" msprop:Generator_ColumnPropNameInTable="IsActiveColumn" msprop:Generator_ColumnVarNameInTable="columnIsActive" msprop:Generator_UserColumnName="IsActive" type="xs:boolean" />
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" msprop:Generator_UserColumnName="CreatedAt" type="xs:dateTime" />
              <xs:element name="UpdatedAt" msprop:Generator_ColumnPropNameInRow="UpdatedAt" msprop:Generator_ColumnPropNameInTable="UpdatedAtColumn" msprop:Generator_ColumnVarNameInTable="columnUpdatedAt" msprop:Generator_UserColumnName="UpdatedAt" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="UserRole" msprop:Generator_RowEvHandlerName="UserRoleRowChangeEventHandler" msprop:Generator_RowDeletedName="UserRoleRowDeleted" msprop:Generator_RowDeletingName="UserRoleRowDeleting" msprop:Generator_RowEvArgName="UserRoleRowChangeEvent" msprop:Generator_TablePropName="UserRole" msprop:Generator_RowChangedName="UserRoleRowChanged" msprop:Generator_RowChangingName="UserRoleRowChanging" msprop:Generator_TableClassName="UserRoleDataTable" msprop:Generator_RowClassName="UserRoleRow" msprop:Generator_TableVarName="tableUserRole" msprop:Generator_UserTableName="UserRole">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="UserRoleId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="UserRoleId" msprop:Generator_ColumnPropNameInTable="UserRoleIdColumn" msprop:Generator_ColumnVarNameInTable="columnUserRoleId" msprop:Generator_UserColumnName="UserRoleId" type="xs:string" />
              <xs:element name="UserId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="UserId" msprop:Generator_ColumnPropNameInTable="UserIdColumn" msprop:Generator_ColumnVarNameInTable="columnUserId" msprop:Generator_UserColumnName="UserId" type="xs:string" />
              <xs:element name="RoleId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnPropNameInRow="RoleId" msprop:Generator_ColumnPropNameInTable="RoleIdColumn" msprop:Generator_ColumnVarNameInTable="columnRoleId" msprop:Generator_UserColumnName="RoleId" type="xs:string" />
              <xs:element name="AssignedAt" msprop:Generator_ColumnPropNameInRow="AssignedAt" msprop:Generator_ColumnPropNameInTable="AssignedAtColumn" msprop:Generator_ColumnVarNameInTable="columnAssignedAt" msprop:Generator_UserColumnName="AssignedAt" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Accommodation" />
      <xs:field xpath="mstns:AccommodationId" />
    </xs:unique>
    <xs:unique name="Attraction_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Attraction" />
      <xs:field xpath="mstns:AttractionId" />
    </xs:unique>
    <xs:unique name="Vehicle_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Vehicle" />
      <xs:field xpath="mstns:VehicleId" />
    </xs:unique>
    <xs:unique name="Coordinator_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Coordinator" />
      <xs:field xpath="mstns:CoordinatorId" />
    </xs:unique>
    <xs:unique name="Role_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Role" />
      <xs:field xpath="mstns:RoleId" />
    </xs:unique>
    <xs:unique name="Tour_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Tour" />
      <xs:field xpath="mstns:TourId" />
    </xs:unique>
    <xs:unique name="TourAccommodation_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:TourAccommodation" />
      <xs:field xpath="mstns:TourAccommodationId" />
    </xs:unique>
    <xs:unique name="TourAttraction_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:TourAttraction" />
      <xs:field xpath="mstns:TourAttractionId" />
    </xs:unique>
    <xs:unique name="TourVehicle_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:TourVehicle" />
      <xs:field xpath="mstns:TourVehicleId" />
    </xs:unique>
    <xs:unique name="User_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:User" />
      <xs:field xpath="mstns:UserId" />
    </xs:unique>
    <xs:unique name="UserRole_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:UserRole" />
      <xs:field xpath="mstns:UserRoleId" />
    </xs:unique>
    <xs:keyref name="FkUserRolesUser" refer="User_Constraint1" msprop:rel_Generator_UserParentTable="User" msprop:rel_Generator_UserChildTable="UserRole" msprop:rel_Generator_RelationVarName="relationFkUserRolesUser" msprop:rel_Generator_ChildPropName="GetUserRoleRows" msprop:rel_Generator_ParentPropName="UserRow" msprop:rel_Generator_UserRelationName="FkUserRolesUser" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:UserRole" />
      <xs:field xpath="mstns:UserId" />
    </xs:keyref>
    <xs:keyref name="FkUserRolesRole" refer="Role_Constraint1" msprop:rel_Generator_UserParentTable="Role" msprop:rel_Generator_UserChildTable="UserRole" msprop:rel_Generator_RelationVarName="relationFkUserRolesRole" msprop:rel_Generator_ChildPropName="GetUserRoleRows" msprop:rel_Generator_ParentPropName="RoleRow" msprop:rel_Generator_UserRelationName="FkUserRolesRole" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:UserRole" />
      <xs:field xpath="mstns:RoleId" />
    </xs:keyref>
    <xs:keyref name="FkTourVehicleTour" refer="Tour_Constraint1" msprop:rel_Generator_UserParentTable="Tour" msprop:rel_Generator_UserChildTable="TourVehicle" msprop:rel_Generator_RelationVarName="relationFkTourVehicleTour" msprop:rel_Generator_ChildPropName="GetTourVehicleRows" msprop:rel_Generator_ParentPropName="TourRow" msprop:rel_Generator_UserRelationName="FkTourVehicleTour" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:TourVehicle" />
      <xs:field xpath="mstns:TourId" />
    </xs:keyref>
    <xs:keyref name="FkTourVehicleVehicle" refer="Vehicle_Constraint1" msprop:rel_Generator_UserParentTable="Vehicle" msprop:rel_Generator_UserChildTable="TourVehicle" msprop:rel_Generator_RelationVarName="relationFkTourVehicleVehicle" msprop:rel_Generator_ChildPropName="GetTourVehicleRows" msprop:rel_Generator_ParentPropName="VehicleRow" msprop:rel_Generator_UserRelationName="FkTourVehicleVehicle" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:TourVehicle" />
      <xs:field xpath="mstns:VehicleId" />
    </xs:keyref>
    <xs:keyref name="FkTourAttractionTour" refer="Tour_Constraint1" msprop:rel_Generator_UserParentTable="Tour" msprop:rel_Generator_UserChildTable="TourAttraction" msprop:rel_Generator_RelationVarName="relationFkTourAttractionTour" msprop:rel_Generator_ChildPropName="GetTourAttractionRows" msprop:rel_Generator_ParentPropName="TourRow" msprop:rel_Generator_UserRelationName="FkTourAttractionTour" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:TourAttraction" />
      <xs:field xpath="mstns:TourId" />
    </xs:keyref>
    <xs:keyref name="FkTourAttractionAttraction" refer="Attraction_Constraint1" msprop:rel_Generator_UserParentTable="Attraction" msprop:rel_Generator_UserChildTable="TourAttraction" msprop:rel_Generator_RelationVarName="relationFkTourAttractionAttraction" msprop:rel_Generator_ChildPropName="GetTourAttractionRows" msprop:rel_Generator_ParentPropName="AttractionRow" msprop:rel_Generator_UserRelationName="FkTourAttractionAttraction" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:TourAttraction" />
      <xs:field xpath="mstns:AttractionId" />
    </xs:keyref>
    <xs:keyref name="FkTourAccommodationAccommodation" refer="Constraint1" msprop:rel_Generator_UserParentTable="Accommodation" msprop:rel_Generator_UserChildTable="TourAccommodation" msprop:rel_Generator_RelationVarName="relationFkTourAccommodationAccommodation" msprop:rel_Generator_ChildPropName="GetTourAccommodationRows" msprop:rel_Generator_ParentPropName="AccommodationRow" msprop:rel_Generator_UserRelationName="FkTourAccommodationAccommodation" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:TourAccommodation" />
      <xs:field xpath="mstns:AccommodationId" />
    </xs:keyref>
    <xs:keyref name="FkTourAccommodationTour" refer="Tour_Constraint1" msprop:rel_Generator_UserParentTable="Tour" msprop:rel_Generator_UserChildTable="TourAccommodation" msprop:rel_Generator_RelationVarName="relationFkTourAccommodationTour" msprop:rel_Generator_ChildPropName="GetTourAccommodationRows" msprop:rel_Generator_ParentPropName="TourRow" msprop:rel_Generator_UserRelationName="FkTourAccommodationTour" msdata:UpdateRule="None">
      <xs:selector xpath=".//mstns:TourAccommodation" />
      <xs:field xpath="mstns:TourId" />
    </xs:keyref>
    <xs:keyref name="FkTourCoordinator" refer="Coordinator_Constraint1" msprop:rel_Generator_UserParentTable="Coordinator" msprop:rel_Generator_UserChildTable="Tour" msprop:rel_Generator_RelationVarName="relationFkTourCoordinator" msprop:rel_Generator_ChildPropName="GetTourRows" msprop:rel_Generator_ParentPropName="CoordinatorRow" msprop:rel_Generator_UserRelationName="FkTourCoordinator" msdata:UpdateRule="None" msdata:DeleteRule="SetNull">
      <xs:selector xpath=".//mstns:Tour" />
      <xs:field xpath="mstns:CoordinatorId" />
    </xs:keyref>
    <xs:keyref name="FkCoordinatorUser" refer="User_Constraint1" msprop:rel_Generator_UserParentTable="User" msprop:rel_Generator_UserChildTable="Coordinator" msprop:rel_Generator_RelationVarName="relationFkCoordinatorUser" msprop:rel_Generator_UserRelationName="FkCoordinatorUser" msprop:rel_Generator_ChildPropName="GetCoordinatorRows" msprop:rel_Generator_ParentPropName="UserRow" msdata:UpdateRule="None" msdata:DeleteRule="SetNull">
      <xs:selector xpath=".//mstns:Coordinator" />
      <xs:field xpath="mstns:UserId" />
    </xs:keyref>
  </xs:element>
</xs:schema>