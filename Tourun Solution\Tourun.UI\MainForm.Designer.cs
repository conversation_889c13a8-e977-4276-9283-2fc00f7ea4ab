﻿namespace Tourun.UI
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            Janus.Windows.GridEX.GridEXLayout vehiclesGrid_Layout_0 = new Janus.Windows.GridEX.GridEXLayout();
            Janus.Windows.GridEX.GridEXLayout toursGrid_Layout_0 = new Janus.Windows.GridEX.GridEXLayout();
            Janus.Windows.GridEX.GridEXLayout accommodationsGrid_Layout_0 = new Janus.Windows.GridEX.GridEXLayout();
            Janus.Windows.GridEX.GridEXLayout attractionsGrid_Layout_0 = new Janus.Windows.GridEX.GridEXLayout();
            Janus.Windows.GridEX.GridEXLayout coordinatorsGrid_Layout_0 = new Janus.Windows.GridEX.GridEXLayout();
            this.vehiclesBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.tourunDataSet = new Tourun.UI.TourunDataSet();
            this.toursBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.accommodationsBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.attractionsBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.coordinatorsBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.ribbon = new Janus.Windows.Ribbon.Ribbon();
            this.generaRibbonTab = new Janus.Windows.Ribbon.RibbonTab();
            this.ribbonGroup1 = new Janus.Windows.Ribbon.RibbonGroup();
            this.newTourCommand = new Janus.Windows.Ribbon.ButtonCommand();
            this.newAcommodationCommand = new Janus.Windows.Ribbon.ButtonCommand();
            this.newAttractionCommand = new Janus.Windows.Ribbon.ButtonCommand();
            this.newCoordinatorCommand = new Janus.Windows.Ribbon.ButtonCommand();
            this.newVehicleCommand = new Janus.Windows.Ribbon.ButtonCommand();
            this.separatorCommand1 = new Janus.Windows.Ribbon.SeparatorCommand();
            this.editDataCommand = new Janus.Windows.Ribbon.ButtonCommand();
            this.deleteDataCommand = new Janus.Windows.Ribbon.ButtonCommand();
            this.ribbonGroup2 = new Janus.Windows.Ribbon.RibbonGroup();
            this.refreshDataCommand = new Janus.Windows.Ribbon.ButtonCommand();
            this.separatorCommand2 = new Janus.Windows.Ribbon.SeparatorCommand();
            this.exportToExcelCommand = new Janus.Windows.Ribbon.ButtonCommand();
            this.gridReportCommand = new Janus.Windows.Ribbon.ButtonCommand();
            this.filtersRibbonGroup = new Janus.Windows.Ribbon.RibbonGroup();
            this.clearFiltersCommand = new Janus.Windows.Ribbon.ButtonCommand();
            this.ribbonStatusBar1 = new Janus.Windows.Ribbon.RibbonStatusBar();
            this.infoCommand = new Janus.Windows.Ribbon.StatusBarPanel();
            this.panelManager = new Janus.Windows.UI.Dock.UIPanelManager(this.components);
            this.navigationPanel = new Janus.Windows.UI.Dock.UIPanelGroup();
            this.toursNavigationPanel = new Janus.Windows.UI.Dock.UIPanel();
            this.toursNavigationPanelContainer = new Janus.Windows.UI.Dock.UIPanelInnerContainer();
            this.accommodationsNavigationPanel = new Janus.Windows.UI.Dock.UIPanel();
            this.accommodationsNavigationPanelContainer = new Janus.Windows.UI.Dock.UIPanelInnerContainer();
            this.attractionsNavigationPanel = new Janus.Windows.UI.Dock.UIPanel();
            this.attractionsNavigationPanelContainer = new Janus.Windows.UI.Dock.UIPanelInnerContainer();
            this.coordinatorsNavigationPanel = new Janus.Windows.UI.Dock.UIPanel();
            this.coordinatorsNavigationPanelContainer = new Janus.Windows.UI.Dock.UIPanelInnerContainer();
            this.vehiclesNavigationPanel = new Janus.Windows.UI.Dock.UIPanel();
            this.vehiclesNavigationPanelContainer = new Janus.Windows.UI.Dock.UIPanelInnerContainer();
            this.vehiclesPanel = new Janus.Windows.UI.Dock.UIPanel();
            this.vehiclesPanelContainer = new Janus.Windows.UI.Dock.UIPanelInnerContainer();
            this.vehiclesGrid = new Janus.Windows.GridEX.GridEX();
            this.toursPanel = new Janus.Windows.UI.Dock.UIPanel();
            this.toursPanelContainer = new Janus.Windows.UI.Dock.UIPanelInnerContainer();
            this.toursGrid = new Janus.Windows.GridEX.GridEX();
            this.accommodationsPanel = new Janus.Windows.UI.Dock.UIPanel();
            this.accommodationsPanelContainer = new Janus.Windows.UI.Dock.UIPanelInnerContainer();
            this.accommodationsGrid = new Janus.Windows.GridEX.GridEX();
            this.attractionsPanel = new Janus.Windows.UI.Dock.UIPanel();
            this.attractionsPanelContainer = new Janus.Windows.UI.Dock.UIPanelInnerContainer();
            this.attractionsGrid = new Janus.Windows.GridEX.GridEX();
            this.coordinatorsPanel = new Janus.Windows.UI.Dock.UIPanel();
            this.coordinatorsPanelContainer = new Janus.Windows.UI.Dock.UIPanelInnerContainer();
            this.coordinatorsGrid = new Janus.Windows.GridEX.GridEX();
            this.gridExporter = new Janus.Windows.GridEX.Export.GridEXExporter(this.components);
            this.gridEXPrintDocument = new Janus.Windows.GridEX.GridEXPrintDocument();
            ((System.ComponentModel.ISupportInitialize)(this.vehiclesBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tourunDataSet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.toursBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.accommodationsBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.attractionsBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.coordinatorsBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelManager)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.navigationPanel)).BeginInit();
            this.navigationPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.toursNavigationPanel)).BeginInit();
            this.toursNavigationPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.accommodationsNavigationPanel)).BeginInit();
            this.accommodationsNavigationPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.attractionsNavigationPanel)).BeginInit();
            this.attractionsNavigationPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.coordinatorsNavigationPanel)).BeginInit();
            this.coordinatorsNavigationPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.vehiclesNavigationPanel)).BeginInit();
            this.vehiclesNavigationPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.vehiclesPanel)).BeginInit();
            this.vehiclesPanel.SuspendLayout();
            this.vehiclesPanelContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.vehiclesGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.toursPanel)).BeginInit();
            this.toursPanel.SuspendLayout();
            this.toursPanelContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.toursGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.accommodationsPanel)).BeginInit();
            this.accommodationsPanel.SuspendLayout();
            this.accommodationsPanelContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.accommodationsGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.attractionsPanel)).BeginInit();
            this.attractionsPanel.SuspendLayout();
            this.attractionsPanelContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.attractionsGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.coordinatorsPanel)).BeginInit();
            this.coordinatorsPanel.SuspendLayout();
            this.coordinatorsPanelContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.coordinatorsGrid)).BeginInit();
            this.SuspendLayout();
            // 
            // vehiclesBindingSource
            // 
            this.vehiclesBindingSource.DataMember = "Vehicle";
            this.vehiclesBindingSource.DataSource = this.tourunDataSet;
            // 
            // tourunDataSet
            // 
            this.tourunDataSet.DataSetName = "TourunDataSet";
            this.tourunDataSet.SchemaSerializationMode = System.Data.SchemaSerializationMode.IncludeSchema;
            // 
            // toursBindingSource
            // 
            this.toursBindingSource.DataMember = "Tour";
            this.toursBindingSource.DataSource = this.tourunDataSet;
            // 
            // accommodationsBindingSource
            // 
            this.accommodationsBindingSource.DataMember = "Accommodation";
            this.accommodationsBindingSource.DataSource = this.tourunDataSet;
            // 
            // attractionsBindingSource
            // 
            this.attractionsBindingSource.DataMember = "Attraction";
            this.attractionsBindingSource.DataSource = this.tourunDataSet;
            // 
            // coordinatorsBindingSource
            // 
            this.coordinatorsBindingSource.DataMember = "Coordinator";
            this.coordinatorsBindingSource.DataSource = this.tourunDataSet;
            // 
            // ribbon
            // 
            this.ribbon.BackstageMenuData = "<?xml version=\"1.0\" encoding=\"utf-8\"?><BackstageMenu><ImageKey /><Key /><Text>Fil" +
    "e</Text></BackstageMenu>";
            this.ribbon.ControlBoxDoubleClickAction = Janus.Windows.Ribbon.ControlBoxDoubleClickAction.None;
            this.ribbon.EnableGlassEffect = false;
            // 
            // 
            // 
            this.ribbon.HelpButton.Image = ((System.Drawing.Image)(resources.GetObject("ribbon.HelpButton.Image")));
            this.ribbon.HelpButton.Key = "HelpButton";
            this.ribbon.Location = new System.Drawing.Point(0, 0);
            this.ribbon.MinimizeOnDoubleClick = false;
            this.ribbon.Name = "ribbon";
            this.ribbon.ShowCustomizeButton = false;
            this.ribbon.ShowQuickCustomizeMenu = false;
            this.ribbon.Size = new System.Drawing.Size(1770, 156);
            // 
            // 
            // 
            this.ribbon.SuperTipComponent.AutoPopDelay = 2000;
            this.ribbon.SuperTipComponent.Font = new System.Drawing.Font("Microsoft Sans Serif", 7.8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(161)));
            this.ribbon.SuperTipComponent.ImageList = null;
            this.ribbon.TabIndex = 0;
            this.ribbon.Tabs.AddRange(new Janus.Windows.Ribbon.RibbonTab[] {
            this.generaRibbonTab});
            this.ribbon.Text = "";
            this.ribbon.VisualStyle = Janus.Windows.Ribbon.VisualStyle.Office2010;
            this.ribbon.CommandClick += new Janus.Windows.Ribbon.CommandEventHandler(this.ribbon_CommandClick);
            // 
            // generaRibbonTab
            // 
            this.generaRibbonTab.Groups.AddRange(new Janus.Windows.Ribbon.RibbonGroup[] {
            this.ribbonGroup1,
            this.ribbonGroup2,
            this.filtersRibbonGroup});
            this.generaRibbonTab.Key = "General";
            this.generaRibbonTab.Name = "generaRibbonTab";
            this.generaRibbonTab.Text = "Γενικά";
            // 
            // ribbonGroup1
            // 
            this.ribbonGroup1.Commands.AddRange(new Janus.Windows.Ribbon.CommandBase[] {
            this.newTourCommand,
            this.newAcommodationCommand,
            this.newAttractionCommand,
            this.newCoordinatorCommand,
            this.newVehicleCommand,
            this.separatorCommand1,
            this.editDataCommand,
            this.deleteDataCommand});
            this.ribbonGroup1.DialogButtonSuperTipSettings.ImageListProvider = this.ribbonGroup1;
            this.ribbonGroup1.Key = "ribbonGroup1";
            this.ribbonGroup1.Name = "ribbonGroup1";
            this.ribbonGroup1.Text = "Δημιουργία - Επεξεργασία";
            // 
            // newTourCommand
            // 
            this.newTourCommand.Image = ((System.Drawing.Image)(resources.GetObject("newTourCommand.Image")));
            this.newTourCommand.Key = "NewTour";
            this.newTourCommand.Name = "newTourCommand";
            this.newTourCommand.Text = "Tour";
            // 
            // newAcommodationCommand
            // 
            this.newAcommodationCommand.Image = ((System.Drawing.Image)(resources.GetObject("newAcommodationCommand.Image")));
            this.newAcommodationCommand.Key = "NewAcommodation";
            this.newAcommodationCommand.Name = "newAcommodationCommand";
            this.newAcommodationCommand.Text = "Διαμονή";
            // 
            // newAttractionCommand
            // 
            this.newAttractionCommand.Image = ((System.Drawing.Image)(resources.GetObject("newAttractionCommand.Image")));
            this.newAttractionCommand.Key = "NewAttraction";
            this.newAttractionCommand.Name = "newAttractionCommand";
            this.newAttractionCommand.Text = "Αξιοθέατα";
            // 
            // newCoordinatorCommand
            // 
            this.newCoordinatorCommand.Image = ((System.Drawing.Image)(resources.GetObject("newCoordinatorCommand.Image")));
            this.newCoordinatorCommand.Key = "NewCoordinator";
            this.newCoordinatorCommand.Name = "newCoordinatorCommand";
            this.newCoordinatorCommand.Text = "Συντονιστής";
            // 
            // newVehicleCommand
            // 
            this.newVehicleCommand.Image = ((System.Drawing.Image)(resources.GetObject("newVehicleCommand.Image")));
            this.newVehicleCommand.Key = "NewVehicle";
            this.newVehicleCommand.Name = "newVehicleCommand";
            this.newVehicleCommand.Text = "Όχημα";
            // 
            // separatorCommand1
            // 
            this.separatorCommand1.Key = "separatorCommand1";
            this.separatorCommand1.Name = "separatorCommand1";
            // 
            // editDataCommand
            // 
            this.editDataCommand.Key = "EditData";
            this.editDataCommand.Name = "editDataCommand";
            this.editDataCommand.SizeStyle = Janus.Windows.Ribbon.CommandSizeStyle.Small;
            this.editDataCommand.Text = "Επεξεργασία";
            // 
            // deleteDataCommand
            // 
            this.deleteDataCommand.Key = "DeleteData";
            this.deleteDataCommand.Name = "deleteDataCommand";
            this.deleteDataCommand.SizeStyle = Janus.Windows.Ribbon.CommandSizeStyle.Small;
            this.deleteDataCommand.Text = "Διαγραφή";
            // 
            // ribbonGroup2
            // 
            this.ribbonGroup2.Commands.AddRange(new Janus.Windows.Ribbon.CommandBase[] {
            this.refreshDataCommand,
            this.separatorCommand2,
            this.exportToExcelCommand,
            this.gridReportCommand});
            this.ribbonGroup2.DialogButtonSuperTipSettings.ImageListProvider = this.ribbonGroup2;
            this.ribbonGroup2.Key = "ribbonGroup2";
            this.ribbonGroup2.Name = "ribbonGroup2";
            this.ribbonGroup2.Text = "Δεδομένα";
            // 
            // refreshDataCommand
            // 
            this.refreshDataCommand.Image = ((System.Drawing.Image)(resources.GetObject("refreshDataCommand.Image")));
            this.refreshDataCommand.Key = "RefreshData";
            this.refreshDataCommand.Name = "refreshDataCommand";
            this.refreshDataCommand.Text = "Ανανέωση Δεδομένων";
            // 
            // separatorCommand2
            // 
            this.separatorCommand2.Key = "separatorCommand2";
            this.separatorCommand2.Name = "separatorCommand2";
            // 
            // exportToExcelCommand
            // 
            this.exportToExcelCommand.Image = ((System.Drawing.Image)(resources.GetObject("exportToExcelCommand.Image")));
            this.exportToExcelCommand.Key = "ExportToExcel";
            this.exportToExcelCommand.Name = "exportToExcelCommand";
            this.exportToExcelCommand.SizeStyle = Janus.Windows.Ribbon.CommandSizeStyle.Small;
            this.exportToExcelCommand.Text = "Εξαγωγή σε Excel";
            // 
            // gridReportCommand
            // 
            this.gridReportCommand.Image = ((System.Drawing.Image)(resources.GetObject("gridReportCommand.Image")));
            this.gridReportCommand.Key = "GridReport";
            this.gridReportCommand.Name = "gridReportCommand";
            this.gridReportCommand.SizeStyle = Janus.Windows.Ribbon.CommandSizeStyle.Small;
            this.gridReportCommand.Text = "Εκτύπωση Λίστας";
            // 
            // filtersRibbonGroup
            // 
            this.filtersRibbonGroup.Commands.AddRange(new Janus.Windows.Ribbon.CommandBase[] {
            this.clearFiltersCommand});
            this.filtersRibbonGroup.DialogButtonSuperTipSettings.ImageListProvider = this.filtersRibbonGroup;
            this.filtersRibbonGroup.Key = "Filters";
            this.filtersRibbonGroup.Name = "filtersRibbonGroup";
            this.filtersRibbonGroup.Text = "Φίλτρα";
            // 
            // clearFiltersCommand
            // 
            this.clearFiltersCommand.Image = ((System.Drawing.Image)(resources.GetObject("clearFiltersCommand.Image")));
            this.clearFiltersCommand.Key = "ClearFilters";
            this.clearFiltersCommand.Name = "clearFiltersCommand";
            this.clearFiltersCommand.Text = "Καθάρισμα Φίλτρων";
            // 
            // ribbonStatusBar1
            // 
            this.ribbonStatusBar1.LeftPanelCommands.AddRange(new Janus.Windows.Ribbon.CommandBase[] {
            this.infoCommand});
            this.ribbonStatusBar1.Location = new System.Drawing.Point(0, 1052);
            this.ribbonStatusBar1.Name = "ribbonStatusBar1";
            this.ribbonStatusBar1.Size = new System.Drawing.Size(1770, 23);
            // 
            // 
            // 
            this.ribbonStatusBar1.SuperTipComponent.AutoPopDelay = 2000;
            this.ribbonStatusBar1.SuperTipComponent.ImageList = null;
            this.ribbonStatusBar1.TabIndex = 1;
            this.ribbonStatusBar1.Text = "ribbonStatusBar1";
            // 
            // infoCommand
            // 
            this.infoCommand.AutoSize = System.Windows.Forms.StatusBarPanelAutoSize.Spring;
            this.infoCommand.Key = "statusBarPanel1";
            this.infoCommand.Name = "infoCommand";
            this.infoCommand.SizeStyle = Janus.Windows.Ribbon.CommandSizeStyle.Small;
            // 
            // panelManager
            // 
            this.panelManager.AllowPanelDrag = false;
            this.panelManager.ContainerControl = this;
            this.panelManager.DefaultPanelSettings.AutoHideButtonVisible = false;
            this.panelManager.DefaultPanelSettings.CaptionDisplayMode = Janus.Windows.UI.Dock.PanelCaptionDisplayMode.Text;
            this.panelManager.DefaultPanelSettings.CaptionDoubleClickAction = Janus.Windows.UI.Dock.CaptionDoubleClickAction.None;
            this.panelManager.DefaultPanelSettings.CaptionHeight = 24;
            this.panelManager.DefaultPanelSettings.CloseButtonVisible = false;
            this.panelManager.VisualStyle = Janus.Windows.UI.Dock.PanelVisualStyle.Office2010;
            this.panelManager.GroupSelectedPanelChanged += new Janus.Windows.UI.Dock.GroupSelectedPanelChangedEventHandler(this.panelManager_GroupSelectedPanelChanged);
            this.navigationPanel.Id = new System.Guid("cc217452-294b-4f71-a547-bc50e02ab927");
            this.navigationPanel.StaticGroup = true;
            this.toursNavigationPanel.Id = new System.Guid("090607bf-1d90-4d43-98b1-1b31f5bd21dd");
            this.navigationPanel.Panels.Add(this.toursNavigationPanel);
            this.accommodationsNavigationPanel.Id = new System.Guid("46165705-4a28-4224-a1ca-ce75663d51b6");
            this.navigationPanel.Panels.Add(this.accommodationsNavigationPanel);
            this.attractionsNavigationPanel.Id = new System.Guid("138fdf84-f326-43ec-96fe-87ef3e2c1737");
            this.navigationPanel.Panels.Add(this.attractionsNavigationPanel);
            this.coordinatorsNavigationPanel.Id = new System.Guid("dad69589-2714-4f3e-b8de-bc7bd257ac67");
            this.navigationPanel.Panels.Add(this.coordinatorsNavigationPanel);
            this.vehiclesNavigationPanel.Id = new System.Guid("e5f7b8c9-3456-4321-a987-123456789abc");
            this.navigationPanel.Panels.Add(this.vehiclesNavigationPanel);
            this.panelManager.Panels.Add(this.navigationPanel);
            this.vehiclesPanel.Id = new System.Guid("f6a8c9d0-4567-5432-b098-234567890def");
            this.panelManager.Panels.Add(this.vehiclesPanel);
            this.toursPanel.Id = new System.Guid("f1b08f00-641a-42a5-829f-cabd5ebb7036");
            this.panelManager.Panels.Add(this.toursPanel);
            this.accommodationsPanel.Id = new System.Guid("8d06e3f9-3576-4285-ba7e-3f30bb63f3a5");
            this.panelManager.Panels.Add(this.accommodationsPanel);
            this.attractionsPanel.Id = new System.Guid("812b43fe-4445-4a2b-95c1-3f0ed68db197");
            this.panelManager.Panels.Add(this.attractionsPanel);
            this.coordinatorsPanel.Id = new System.Guid("b11b221a-7303-434b-8fc8-0977163d7a06");
            this.panelManager.Panels.Add(this.coordinatorsPanel);
            // 
            // Design Time Panel Info:
            // 
            this.panelManager.BeginPanelInfo();
            this.panelManager.AddDockPanelInfo(new System.Guid("cc217452-294b-4f71-a547-bc50e02ab927"), Janus.Windows.UI.Dock.PanelGroupStyle.OutlookNavigator, Janus.Windows.UI.Dock.PanelDockStyle.Left, true, new System.Drawing.Size(213, 890), true);
            this.panelManager.AddDockPanelInfo(new System.Guid("090607bf-1d90-4d43-98b1-1b31f5bd21dd"), new System.Guid("cc217452-294b-4f71-a547-bc50e02ab927"), -1, true);
            this.panelManager.AddDockPanelInfo(new System.Guid("46165705-4a28-4224-a1ca-ce75663d51b6"), new System.Guid("cc217452-294b-4f71-a547-bc50e02ab927"), -1, true);
            this.panelManager.AddDockPanelInfo(new System.Guid("138fdf84-f326-43ec-96fe-87ef3e2c1737"), new System.Guid("cc217452-294b-4f71-a547-bc50e02ab927"), -1, true);
            this.panelManager.AddDockPanelInfo(new System.Guid("dad69589-2714-4f3e-b8de-bc7bd257ac67"), new System.Guid("cc217452-294b-4f71-a547-bc50e02ab927"), -1, true);
            this.panelManager.AddDockPanelInfo(new System.Guid("e5f7b8c9-3456-4321-a987-123456789abc"), new System.Guid("cc217452-294b-4f71-a547-bc50e02ab927"), -1, true);
            this.panelManager.AddDockPanelInfo(new System.Guid("b11b221a-7303-434b-8fc8-0977163d7a06"), Janus.Windows.UI.Dock.PanelDockStyle.Fill, new System.Drawing.Size(1551, 878), true);
            this.panelManager.AddDockPanelInfo(new System.Guid("812b43fe-4445-4a2b-95c1-3f0ed68db197"), Janus.Windows.UI.Dock.PanelDockStyle.Fill, new System.Drawing.Size(1551, 878), true);
            this.panelManager.AddDockPanelInfo(new System.Guid("8d06e3f9-3576-4285-ba7e-3f30bb63f3a5"), Janus.Windows.UI.Dock.PanelDockStyle.Fill, new System.Drawing.Size(1551, 882), true);
            this.panelManager.AddDockPanelInfo(new System.Guid("f1b08f00-641a-42a5-829f-cabd5ebb7036"), Janus.Windows.UI.Dock.PanelDockStyle.Fill, new System.Drawing.Size(1551, 890), true);
            this.panelManager.AddDockPanelInfo(new System.Guid("f6a8c9d0-4567-5432-b098-234567890def"), Janus.Windows.UI.Dock.PanelDockStyle.Fill, new System.Drawing.Size(1551, 890), true);
            this.panelManager.AddFloatingPanelInfo(new System.Guid("cc217452-294b-4f71-a547-bc50e02ab927"), Janus.Windows.UI.Dock.PanelGroupStyle.OutlookNavigator, true, new System.Drawing.Point(-1, -1), new System.Drawing.Size(-1, -1), false);
            this.panelManager.AddFloatingPanelInfo(new System.Guid("f1b08f00-641a-42a5-829f-cabd5ebb7036"), new System.Drawing.Point(-1, -1), new System.Drawing.Size(-1, -1), false);
            this.panelManager.AddFloatingPanelInfo(new System.Guid("090607bf-1d90-4d43-98b1-1b31f5bd21dd"), new System.Drawing.Point(-1, -1), new System.Drawing.Size(-1, -1), false);
            this.panelManager.AddFloatingPanelInfo(new System.Guid("46165705-4a28-4224-a1ca-ce75663d51b6"), new System.Drawing.Point(-1, -1), new System.Drawing.Size(-1, -1), false);
            this.panelManager.AddFloatingPanelInfo(new System.Guid("138fdf84-f326-43ec-96fe-87ef3e2c1737"), new System.Drawing.Point(-1, -1), new System.Drawing.Size(-1, -1), false);
            this.panelManager.AddFloatingPanelInfo(new System.Guid("dad69589-2714-4f3e-b8de-bc7bd257ac67"), new System.Drawing.Point(-1, -1), new System.Drawing.Size(-1, -1), false);
            this.panelManager.AddFloatingPanelInfo(new System.Guid("b11b221a-7303-434b-8fc8-0977163d7a06"), new System.Drawing.Point(-1, -1), new System.Drawing.Size(-1, -1), false);
            this.panelManager.AddFloatingPanelInfo(new System.Guid("812b43fe-4445-4a2b-95c1-3f0ed68db197"), new System.Drawing.Point(-1, -1), new System.Drawing.Size(-1, -1), false);
            this.panelManager.AddFloatingPanelInfo(new System.Guid("8d06e3f9-3576-4285-ba7e-3f30bb63f3a5"), new System.Drawing.Point(-1, -1), new System.Drawing.Size(-1, -1), false);
            this.panelManager.AddFloatingPanelInfo(new System.Guid("f6a8c9d0-4567-5432-b098-234567890def"), new System.Drawing.Point(-1, -1), new System.Drawing.Size(-1, -1), false);
            this.panelManager.AddFloatingPanelInfo(new System.Guid("e5f7b8c9-3456-4321-a987-123456789abc"), new System.Drawing.Point(-1, -1), new System.Drawing.Size(-1, -1), false);
            this.panelManager.EndPanelInfo();
            // 
            // navigationPanel
            // 
            this.navigationPanel.GroupStyle = Janus.Windows.UI.Dock.PanelGroupStyle.OutlookNavigator;
            this.navigationPanel.Location = new System.Drawing.Point(3, 159);
            this.navigationPanel.Name = "navigationPanel";
            this.navigationPanel.SelectedPanel = this.accommodationsNavigationPanel;
            this.navigationPanel.ShowOutlookNavigatorConfigureMenu = false;
            this.navigationPanel.Size = new System.Drawing.Size(213, 890);
            this.navigationPanel.TabIndex = 4;
            this.navigationPanel.Text = "Navigation";
            // 
            // toursNavigationPanel
            // 
            this.toursNavigationPanel.Image = ((System.Drawing.Image)(resources.GetObject("toursNavigationPanel.Image")));
            this.toursNavigationPanel.InnerContainer = this.toursNavigationPanelContainer;
            this.toursNavigationPanel.Location = new System.Drawing.Point(0, 0);
            this.toursNavigationPanel.Name = "toursNavigationPanel";
            this.toursNavigationPanel.Size = new System.Drawing.Size(209, 690);
            this.toursNavigationPanel.TabIndex = 4;
            this.toursNavigationPanel.Text = "Tours";
            // 
            // toursNavigationPanelContainer
            // 
            this.toursNavigationPanelContainer.Location = new System.Drawing.Point(1, 25);
            this.toursNavigationPanelContainer.Name = "toursNavigationPanelContainer";
            this.toursNavigationPanelContainer.Size = new System.Drawing.Size(207, 665);
            this.toursNavigationPanelContainer.TabIndex = 0;
            // 
            // accommodationsNavigationPanel
            // 
            this.accommodationsNavigationPanel.Image = ((System.Drawing.Image)(resources.GetObject("accommodationsNavigationPanel.Image")));
            this.accommodationsNavigationPanel.InnerContainer = this.accommodationsNavigationPanelContainer;
            this.accommodationsNavigationPanel.Location = new System.Drawing.Point(0, 0);
            this.accommodationsNavigationPanel.Name = "accommodationsNavigationPanel";
            this.accommodationsNavigationPanel.Size = new System.Drawing.Size(209, 690);
            this.accommodationsNavigationPanel.TabIndex = 4;
            this.accommodationsNavigationPanel.Text = "Διαμονή";
            // 
            // accommodationsNavigationPanelContainer
            // 
            this.accommodationsNavigationPanelContainer.Location = new System.Drawing.Point(1, 25);
            this.accommodationsNavigationPanelContainer.Name = "accommodationsNavigationPanelContainer";
            this.accommodationsNavigationPanelContainer.Size = new System.Drawing.Size(207, 665);
            this.accommodationsNavigationPanelContainer.TabIndex = 0;
            // 
            // attractionsNavigationPanel
            // 
            this.attractionsNavigationPanel.Image = ((System.Drawing.Image)(resources.GetObject("attractionsNavigationPanel.Image")));
            this.attractionsNavigationPanel.InnerContainer = this.attractionsNavigationPanelContainer;
            this.attractionsNavigationPanel.Location = new System.Drawing.Point(0, 0);
            this.attractionsNavigationPanel.Name = "attractionsNavigationPanel";
            this.attractionsNavigationPanel.Size = new System.Drawing.Size(209, 690);
            this.attractionsNavigationPanel.TabIndex = 4;
            this.attractionsNavigationPanel.Text = "Αξιοθέατα";
            // 
            // attractionsNavigationPanelContainer
            // 
            this.attractionsNavigationPanelContainer.Location = new System.Drawing.Point(1, 25);
            this.attractionsNavigationPanelContainer.Name = "attractionsNavigationPanelContainer";
            this.attractionsNavigationPanelContainer.Size = new System.Drawing.Size(207, 665);
            this.attractionsNavigationPanelContainer.TabIndex = 0;
            // 
            // coordinatorsNavigationPanel
            // 
            this.coordinatorsNavigationPanel.Image = ((System.Drawing.Image)(resources.GetObject("coordinatorsNavigationPanel.Image")));
            this.coordinatorsNavigationPanel.InnerContainer = this.coordinatorsNavigationPanelContainer;
            this.coordinatorsNavigationPanel.Location = new System.Drawing.Point(0, 0);
            this.coordinatorsNavigationPanel.Name = "coordinatorsNavigationPanel";
            this.coordinatorsNavigationPanel.Size = new System.Drawing.Size(209, 690);
            this.coordinatorsNavigationPanel.TabIndex = 4;
            this.coordinatorsNavigationPanel.Text = "Συντονιστές";
            // 
            // coordinatorsNavigationPanelContainer
            // 
            this.coordinatorsNavigationPanelContainer.Location = new System.Drawing.Point(1, 25);
            this.coordinatorsNavigationPanelContainer.Name = "coordinatorsNavigationPanelContainer";
            this.coordinatorsNavigationPanelContainer.Size = new System.Drawing.Size(207, 665);
            this.coordinatorsNavigationPanelContainer.TabIndex = 0;
            // 
            // vehiclesNavigationPanel
            // 
            this.vehiclesNavigationPanel.Image = ((System.Drawing.Image)(resources.GetObject("vehiclesNavigationPanel.Image")));
            this.vehiclesNavigationPanel.InnerContainer = this.vehiclesNavigationPanelContainer;
            this.vehiclesNavigationPanel.Location = new System.Drawing.Point(0, 0);
            this.vehiclesNavigationPanel.Name = "vehiclesNavigationPanel";
            this.vehiclesNavigationPanel.Size = new System.Drawing.Size(209, 690);
            this.vehiclesNavigationPanel.TabIndex = 4;
            this.vehiclesNavigationPanel.Text = "Οχήματα";
            // 
            // vehiclesNavigationPanelContainer
            // 
            this.vehiclesNavigationPanelContainer.Location = new System.Drawing.Point(1, 25);
            this.vehiclesNavigationPanelContainer.Name = "vehiclesNavigationPanelContainer";
            this.vehiclesNavigationPanelContainer.Size = new System.Drawing.Size(207, 665);
            this.vehiclesNavigationPanelContainer.TabIndex = 0;
            // 
            // vehiclesPanel
            // 
            this.vehiclesPanel.Closed = true;
            this.vehiclesPanel.InnerContainer = this.vehiclesPanelContainer;
            this.vehiclesPanel.Location = new System.Drawing.Point(216, 159);
            this.vehiclesPanel.Name = "vehiclesPanel";
            this.vehiclesPanel.Size = new System.Drawing.Size(1551, 890);
            this.vehiclesPanel.TabIndex = 4;
            this.vehiclesPanel.Text = "Οχήματα";
            // 
            // vehiclesPanelContainer
            // 
            this.vehiclesPanelContainer.Controls.Add(this.vehiclesGrid);
            this.vehiclesPanelContainer.Location = new System.Drawing.Point(1, 24);
            this.vehiclesPanelContainer.Name = "vehiclesPanelContainer";
            this.vehiclesPanelContainer.Size = new System.Drawing.Size(1549, 865);
            this.vehiclesPanelContainer.TabIndex = 0;
            // 
            // vehiclesGrid
            // 
            this.vehiclesGrid.AllowDelete = Janus.Windows.GridEX.InheritableBoolean.True;
            this.vehiclesGrid.AllowEdit = Janus.Windows.GridEX.InheritableBoolean.False;
            this.vehiclesGrid.BackColor = System.Drawing.Color.White;
            this.vehiclesGrid.BorderStyle = Janus.Windows.GridEX.BorderStyle.None;
            this.vehiclesGrid.DataSource = this.vehiclesBindingSource;
            this.vehiclesGrid.DefaultFilterRowComparison = Janus.Windows.GridEX.FilterConditionOperator.Contains;
            this.vehiclesGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.vehiclesGrid.FilterMode = Janus.Windows.GridEX.FilterMode.Automatic;
            this.vehiclesGrid.FilterRowUpdateMode = Janus.Windows.GridEX.FilterRowUpdateMode.WhenValueChanges;
            this.vehiclesGrid.FocusCellFormatStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.vehiclesGrid.FocusCellFormatStyle.BackColorGradient = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.vehiclesGrid.FocusStyle = Janus.Windows.GridEX.FocusStyle.None;
            this.vehiclesGrid.GridLineStyle = Janus.Windows.GridEX.GridLineStyle.Solid;
            this.vehiclesGrid.GroupTotalRowFormatStyle.FontBold = Janus.Windows.GridEX.TriState.True;
            this.vehiclesGrid.HideSelection = Janus.Windows.GridEX.HideSelection.Highlight;
            vehiclesGrid_Layout_0.DataSource = this.vehiclesBindingSource;
            vehiclesGrid_Layout_0.IsCurrentLayout = true;
            vehiclesGrid_Layout_0.Key = "Simple";
            vehiclesGrid_Layout_0.LayoutString = resources.GetString("vehiclesGrid_Layout_0.LayoutString");
            this.vehiclesGrid.Layouts.AddRange(new Janus.Windows.GridEX.GridEXLayout[] {
            vehiclesGrid_Layout_0});
            this.vehiclesGrid.Location = new System.Drawing.Point(0, 0);
            this.vehiclesGrid.Margin = new System.Windows.Forms.Padding(4);
            this.vehiclesGrid.Name = "vehiclesGrid";
            this.vehiclesGrid.OfficeColorScheme = Janus.Windows.GridEX.OfficeColorScheme.Blue;
            this.vehiclesGrid.RowHeaders = Janus.Windows.GridEX.InheritableBoolean.True;
            this.vehiclesGrid.SaveSettings = true;
            this.vehiclesGrid.SelectionMode = Janus.Windows.GridEX.SelectionMode.MultipleSelectionSameTable;
            this.vehiclesGrid.SettingsKey = "ToursGrid";
            this.vehiclesGrid.Size = new System.Drawing.Size(1549, 865);
            this.vehiclesGrid.TabIndex = 7;
            this.vehiclesGrid.TotalRowFormatStyle.FontBold = Janus.Windows.GridEX.TriState.True;
            this.vehiclesGrid.VisualStyle = Janus.Windows.GridEX.VisualStyle.Office2010;
            // 
            // toursPanel
            // 
            this.toursPanel.InnerContainer = this.toursPanelContainer;
            this.toursPanel.Location = new System.Drawing.Point(216, 159);
            this.toursPanel.Name = "toursPanel";
            this.toursPanel.Size = new System.Drawing.Size(1551, 890);
            this.toursPanel.TabIndex = 4;
            this.toursPanel.Text = "Tours";
            // 
            // toursPanelContainer
            // 
            this.toursPanelContainer.Controls.Add(this.toursGrid);
            this.toursPanelContainer.Location = new System.Drawing.Point(1, 24);
            this.toursPanelContainer.Name = "toursPanelContainer";
            this.toursPanelContainer.Size = new System.Drawing.Size(1549, 865);
            this.toursPanelContainer.TabIndex = 0;
            // 
            // toursGrid
            // 
            this.toursGrid.AllowDelete = Janus.Windows.GridEX.InheritableBoolean.True;
            this.toursGrid.AllowEdit = Janus.Windows.GridEX.InheritableBoolean.False;
            this.toursGrid.BackColor = System.Drawing.Color.White;
            this.toursGrid.BorderStyle = Janus.Windows.GridEX.BorderStyle.None;
            this.toursGrid.DataSource = this.toursBindingSource;
            this.toursGrid.DefaultFilterRowComparison = Janus.Windows.GridEX.FilterConditionOperator.Contains;
            this.toursGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.toursGrid.FilterMode = Janus.Windows.GridEX.FilterMode.Automatic;
            this.toursGrid.FilterRowUpdateMode = Janus.Windows.GridEX.FilterRowUpdateMode.WhenValueChanges;
            this.toursGrid.FocusCellFormatStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.toursGrid.FocusCellFormatStyle.BackColorGradient = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.toursGrid.FocusStyle = Janus.Windows.GridEX.FocusStyle.None;
            this.toursGrid.GridLineStyle = Janus.Windows.GridEX.GridLineStyle.Solid;
            this.toursGrid.GroupTotalRowFormatStyle.FontBold = Janus.Windows.GridEX.TriState.True;
            this.toursGrid.HideSelection = Janus.Windows.GridEX.HideSelection.Highlight;
            toursGrid_Layout_0.DataSource = this.toursBindingSource;
            toursGrid_Layout_0.IsCurrentLayout = true;
            toursGrid_Layout_0.Key = "Simple";
            toursGrid_Layout_0.LayoutString = resources.GetString("toursGrid_Layout_0.LayoutString");
            this.toursGrid.Layouts.AddRange(new Janus.Windows.GridEX.GridEXLayout[] {
            toursGrid_Layout_0});
            this.toursGrid.Location = new System.Drawing.Point(0, 0);
            this.toursGrid.Margin = new System.Windows.Forms.Padding(4);
            this.toursGrid.Name = "toursGrid";
            this.toursGrid.OfficeColorScheme = Janus.Windows.GridEX.OfficeColorScheme.Blue;
            this.toursGrid.RowHeaders = Janus.Windows.GridEX.InheritableBoolean.True;
            this.toursGrid.SaveSettings = true;
            this.toursGrid.SelectionMode = Janus.Windows.GridEX.SelectionMode.MultipleSelectionSameTable;
            this.toursGrid.SettingsKey = "ToursGrid";
            this.toursGrid.Size = new System.Drawing.Size(1549, 865);
            this.toursGrid.TabIndex = 6;
            this.toursGrid.TotalRowFormatStyle.FontBold = Janus.Windows.GridEX.TriState.True;
            this.toursGrid.VisualStyle = Janus.Windows.GridEX.VisualStyle.Office2010;
            this.toursGrid.RowDoubleClick += new Janus.Windows.GridEX.RowActionEventHandler(this.grid_RowDoubleClick);
            this.toursGrid.RowCountChanged += new System.EventHandler(this.grid_RowCountChanged);
            this.toursGrid.DeletingRecords += new System.ComponentModel.CancelEventHandler(this.grid_DeletingRecords);
            this.toursGrid.RecordsDeleted += new System.EventHandler(this.grid_RecordsDeleted);
            this.toursGrid.SelectionChanged += new System.EventHandler(this.grid_SelectionChanged);
            this.toursGrid.CurrentLayoutChanged += new System.EventHandler(this.grid_CurrentLayoutChanged);
            this.toursGrid.CurrentLayoutChanging += new System.ComponentModel.CancelEventHandler(this.grid_CurrentLayoutChanging);
            this.toursGrid.KeyDown += new System.Windows.Forms.KeyEventHandler(this.grid_KeyDown);
            this.toursGrid.MouseDown += new System.Windows.Forms.MouseEventHandler(this.grid_MouseDown);
            // 
            // accommodationsPanel
            // 
            this.accommodationsPanel.Closed = true;
            this.accommodationsPanel.InnerContainer = this.accommodationsPanelContainer;
            this.accommodationsPanel.Location = new System.Drawing.Point(216, 159);
            this.accommodationsPanel.Name = "accommodationsPanel";
            this.accommodationsPanel.Size = new System.Drawing.Size(1551, 882);
            this.accommodationsPanel.TabIndex = 4;
            this.accommodationsPanel.Text = "Διαμονή";
            // 
            // accommodationsPanelContainer
            // 
            this.accommodationsPanelContainer.Controls.Add(this.accommodationsGrid);
            this.accommodationsPanelContainer.Location = new System.Drawing.Point(0, 0);
            this.accommodationsPanelContainer.Name = "accommodationsPanelContainer";
            this.accommodationsPanelContainer.Size = new System.Drawing.Size(1551, 882);
            this.accommodationsPanelContainer.TabIndex = 0;
            // 
            // accommodationsGrid
            // 
            this.accommodationsGrid.AllowDelete = Janus.Windows.GridEX.InheritableBoolean.True;
            this.accommodationsGrid.AllowEdit = Janus.Windows.GridEX.InheritableBoolean.False;
            this.accommodationsGrid.BackColor = System.Drawing.Color.White;
            this.accommodationsGrid.BorderStyle = Janus.Windows.GridEX.BorderStyle.None;
            this.accommodationsGrid.DataSource = this.accommodationsBindingSource;
            this.accommodationsGrid.DefaultFilterRowComparison = Janus.Windows.GridEX.FilterConditionOperator.Contains;
            this.accommodationsGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.accommodationsGrid.FilterMode = Janus.Windows.GridEX.FilterMode.Automatic;
            this.accommodationsGrid.FilterRowUpdateMode = Janus.Windows.GridEX.FilterRowUpdateMode.WhenValueChanges;
            this.accommodationsGrid.FocusCellFormatStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.accommodationsGrid.FocusCellFormatStyle.BackColorGradient = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.accommodationsGrid.FocusStyle = Janus.Windows.GridEX.FocusStyle.None;
            this.accommodationsGrid.GridLineStyle = Janus.Windows.GridEX.GridLineStyle.Solid;
            this.accommodationsGrid.GroupTotalRowFormatStyle.FontBold = Janus.Windows.GridEX.TriState.True;
            this.accommodationsGrid.HideSelection = Janus.Windows.GridEX.HideSelection.Highlight;
            accommodationsGrid_Layout_0.DataSource = this.accommodationsBindingSource;
            accommodationsGrid_Layout_0.IsCurrentLayout = true;
            accommodationsGrid_Layout_0.Key = "Simple";
            accommodationsGrid_Layout_0.LayoutString = resources.GetString("accommodationsGrid_Layout_0.LayoutString");
            this.accommodationsGrid.Layouts.AddRange(new Janus.Windows.GridEX.GridEXLayout[] {
            accommodationsGrid_Layout_0});
            this.accommodationsGrid.Location = new System.Drawing.Point(0, 0);
            this.accommodationsGrid.Margin = new System.Windows.Forms.Padding(4);
            this.accommodationsGrid.Name = "accommodationsGrid";
            this.accommodationsGrid.OfficeColorScheme = Janus.Windows.GridEX.OfficeColorScheme.Blue;
            this.accommodationsGrid.RowHeaders = Janus.Windows.GridEX.InheritableBoolean.True;
            this.accommodationsGrid.SaveSettings = true;
            this.accommodationsGrid.SelectionMode = Janus.Windows.GridEX.SelectionMode.MultipleSelectionSameTable;
            this.accommodationsGrid.SettingsKey = "AccommodationsGrid";
            this.accommodationsGrid.Size = new System.Drawing.Size(1551, 882);
            this.accommodationsGrid.TabIndex = 4;
            this.accommodationsGrid.TotalRowFormatStyle.FontBold = Janus.Windows.GridEX.TriState.True;
            this.accommodationsGrid.VisualStyle = Janus.Windows.GridEX.VisualStyle.Office2010;
            this.accommodationsGrid.RowDoubleClick += new Janus.Windows.GridEX.RowActionEventHandler(this.grid_RowDoubleClick);
            this.accommodationsGrid.RowCountChanged += new System.EventHandler(this.grid_RowCountChanged);
            this.accommodationsGrid.DeletingRecords += new System.ComponentModel.CancelEventHandler(this.grid_DeletingRecords);
            this.accommodationsGrid.RecordsDeleted += new System.EventHandler(this.grid_RecordsDeleted);
            this.accommodationsGrid.SelectionChanged += new System.EventHandler(this.grid_SelectionChanged);
            this.accommodationsGrid.CurrentLayoutChanged += new System.EventHandler(this.grid_CurrentLayoutChanged);
            this.accommodationsGrid.CurrentLayoutChanging += new System.ComponentModel.CancelEventHandler(this.grid_CurrentLayoutChanging);
            this.accommodationsGrid.KeyDown += new System.Windows.Forms.KeyEventHandler(this.grid_KeyDown);
            this.accommodationsGrid.MouseDown += new System.Windows.Forms.MouseEventHandler(this.grid_MouseDown);
            // 
            // attractionsPanel
            // 
            this.attractionsPanel.Closed = true;
            this.attractionsPanel.InnerContainer = this.attractionsPanelContainer;
            this.attractionsPanel.Location = new System.Drawing.Point(216, 159);
            this.attractionsPanel.Name = "attractionsPanel";
            this.attractionsPanel.Size = new System.Drawing.Size(1551, 878);
            this.attractionsPanel.TabIndex = 4;
            this.attractionsPanel.Text = "Αξιοθέατα";
            // 
            // attractionsPanelContainer
            // 
            this.attractionsPanelContainer.Controls.Add(this.attractionsGrid);
            this.attractionsPanelContainer.Location = new System.Drawing.Point(0, 0);
            this.attractionsPanelContainer.Name = "attractionsPanelContainer";
            this.attractionsPanelContainer.Size = new System.Drawing.Size(1551, 878);
            this.attractionsPanelContainer.TabIndex = 0;
            // 
            // attractionsGrid
            // 
            this.attractionsGrid.AllowDelete = Janus.Windows.GridEX.InheritableBoolean.True;
            this.attractionsGrid.AllowEdit = Janus.Windows.GridEX.InheritableBoolean.False;
            this.attractionsGrid.BackColor = System.Drawing.Color.White;
            this.attractionsGrid.BorderStyle = Janus.Windows.GridEX.BorderStyle.None;
            this.attractionsGrid.DataSource = this.attractionsBindingSource;
            this.attractionsGrid.DefaultFilterRowComparison = Janus.Windows.GridEX.FilterConditionOperator.Contains;
            this.attractionsGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.attractionsGrid.FilterMode = Janus.Windows.GridEX.FilterMode.Automatic;
            this.attractionsGrid.FilterRowUpdateMode = Janus.Windows.GridEX.FilterRowUpdateMode.WhenValueChanges;
            this.attractionsGrid.FocusCellFormatStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.attractionsGrid.FocusCellFormatStyle.BackColorGradient = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.attractionsGrid.FocusStyle = Janus.Windows.GridEX.FocusStyle.None;
            this.attractionsGrid.GridLineStyle = Janus.Windows.GridEX.GridLineStyle.Solid;
            this.attractionsGrid.GroupTotalRowFormatStyle.FontBold = Janus.Windows.GridEX.TriState.True;
            this.attractionsGrid.HideSelection = Janus.Windows.GridEX.HideSelection.Highlight;
            attractionsGrid_Layout_0.DataSource = this.attractionsBindingSource;
            attractionsGrid_Layout_0.IsCurrentLayout = true;
            attractionsGrid_Layout_0.Key = "Simple";
            attractionsGrid_Layout_0.LayoutString = resources.GetString("attractionsGrid_Layout_0.LayoutString");
            this.attractionsGrid.Layouts.AddRange(new Janus.Windows.GridEX.GridEXLayout[] {
            attractionsGrid_Layout_0});
            this.attractionsGrid.Location = new System.Drawing.Point(0, 0);
            this.attractionsGrid.Margin = new System.Windows.Forms.Padding(4);
            this.attractionsGrid.Name = "attractionsGrid";
            this.attractionsGrid.OfficeColorScheme = Janus.Windows.GridEX.OfficeColorScheme.Blue;
            this.attractionsGrid.RowHeaders = Janus.Windows.GridEX.InheritableBoolean.True;
            this.attractionsGrid.SelectionMode = Janus.Windows.GridEX.SelectionMode.MultipleSelectionSameTable;
            this.attractionsGrid.SettingsKey = "AttractionsGrid";
            this.attractionsGrid.Size = new System.Drawing.Size(1551, 878);
            this.attractionsGrid.TabIndex = 5;
            this.attractionsGrid.TotalRowFormatStyle.FontBold = Janus.Windows.GridEX.TriState.True;
            this.attractionsGrid.VisualStyle = Janus.Windows.GridEX.VisualStyle.Office2010;
            this.attractionsGrid.RowDoubleClick += new Janus.Windows.GridEX.RowActionEventHandler(this.grid_RowDoubleClick);
            this.attractionsGrid.RowCountChanged += new System.EventHandler(this.grid_RowCountChanged);
            this.attractionsGrid.DeletingRecords += new System.ComponentModel.CancelEventHandler(this.grid_DeletingRecords);
            this.attractionsGrid.RecordsDeleted += new System.EventHandler(this.grid_RecordsDeleted);
            this.attractionsGrid.SelectionChanged += new System.EventHandler(this.grid_SelectionChanged);
            this.attractionsGrid.CurrentLayoutChanged += new System.EventHandler(this.grid_CurrentLayoutChanged);
            this.attractionsGrid.CurrentLayoutChanging += new System.ComponentModel.CancelEventHandler(this.grid_CurrentLayoutChanging);
            this.attractionsGrid.KeyDown += new System.Windows.Forms.KeyEventHandler(this.grid_KeyDown);
            this.attractionsGrid.MouseDown += new System.Windows.Forms.MouseEventHandler(this.grid_MouseDown);
            // 
            // coordinatorsPanel
            // 
            this.coordinatorsPanel.Closed = true;
            this.coordinatorsPanel.InnerContainer = this.coordinatorsPanelContainer;
            this.coordinatorsPanel.Location = new System.Drawing.Point(216, 159);
            this.coordinatorsPanel.Name = "coordinatorsPanel";
            this.coordinatorsPanel.Size = new System.Drawing.Size(1551, 878);
            this.coordinatorsPanel.TabIndex = 4;
            this.coordinatorsPanel.Text = "Συντονιστές";
            // 
            // coordinatorsPanelContainer
            // 
            this.coordinatorsPanelContainer.Controls.Add(this.coordinatorsGrid);
            this.coordinatorsPanelContainer.Location = new System.Drawing.Point(0, 0);
            this.coordinatorsPanelContainer.Name = "coordinatorsPanelContainer";
            this.coordinatorsPanelContainer.Size = new System.Drawing.Size(1551, 878);
            this.coordinatorsPanelContainer.TabIndex = 0;
            // 
            // coordinatorsGrid
            // 
            this.coordinatorsGrid.AllowDelete = Janus.Windows.GridEX.InheritableBoolean.True;
            this.coordinatorsGrid.AllowEdit = Janus.Windows.GridEX.InheritableBoolean.False;
            this.coordinatorsGrid.BackColor = System.Drawing.Color.White;
            this.coordinatorsGrid.BorderStyle = Janus.Windows.GridEX.BorderStyle.None;
            this.coordinatorsGrid.DataSource = this.coordinatorsBindingSource;
            this.coordinatorsGrid.DefaultFilterRowComparison = Janus.Windows.GridEX.FilterConditionOperator.Contains;
            this.coordinatorsGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.coordinatorsGrid.FilterMode = Janus.Windows.GridEX.FilterMode.Automatic;
            this.coordinatorsGrid.FilterRowUpdateMode = Janus.Windows.GridEX.FilterRowUpdateMode.WhenValueChanges;
            this.coordinatorsGrid.FocusCellFormatStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.coordinatorsGrid.FocusCellFormatStyle.BackColorGradient = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.coordinatorsGrid.FocusStyle = Janus.Windows.GridEX.FocusStyle.None;
            this.coordinatorsGrid.GridLineStyle = Janus.Windows.GridEX.GridLineStyle.Solid;
            this.coordinatorsGrid.GroupTotalRowFormatStyle.FontBold = Janus.Windows.GridEX.TriState.True;
            this.coordinatorsGrid.HideSelection = Janus.Windows.GridEX.HideSelection.Highlight;
            coordinatorsGrid_Layout_0.DataSource = this.coordinatorsBindingSource;
            coordinatorsGrid_Layout_0.IsCurrentLayout = true;
            coordinatorsGrid_Layout_0.Key = "Simple";
            coordinatorsGrid_Layout_0.LayoutString = resources.GetString("coordinatorsGrid_Layout_0.LayoutString");
            this.coordinatorsGrid.Layouts.AddRange(new Janus.Windows.GridEX.GridEXLayout[] {
            coordinatorsGrid_Layout_0});
            this.coordinatorsGrid.Location = new System.Drawing.Point(0, 0);
            this.coordinatorsGrid.Margin = new System.Windows.Forms.Padding(4);
            this.coordinatorsGrid.Name = "coordinatorsGrid";
            this.coordinatorsGrid.OfficeColorScheme = Janus.Windows.GridEX.OfficeColorScheme.Blue;
            this.coordinatorsGrid.RowHeaders = Janus.Windows.GridEX.InheritableBoolean.True;
            this.coordinatorsGrid.SelectionMode = Janus.Windows.GridEX.SelectionMode.MultipleSelectionSameTable;
            this.coordinatorsGrid.SettingsKey = "CoordinatorsGrid";
            this.coordinatorsGrid.Size = new System.Drawing.Size(1551, 878);
            this.coordinatorsGrid.TabIndex = 7;
            this.coordinatorsGrid.TotalRowFormatStyle.FontBold = Janus.Windows.GridEX.TriState.True;
            this.coordinatorsGrid.VisualStyle = Janus.Windows.GridEX.VisualStyle.Office2010;
            this.coordinatorsGrid.RowDoubleClick += new Janus.Windows.GridEX.RowActionEventHandler(this.grid_RowDoubleClick);
            this.coordinatorsGrid.RowCountChanged += new System.EventHandler(this.grid_RowCountChanged);
            this.coordinatorsGrid.DeletingRecords += new System.ComponentModel.CancelEventHandler(this.grid_DeletingRecords);
            this.coordinatorsGrid.RecordsDeleted += new System.EventHandler(this.grid_RecordsDeleted);
            this.coordinatorsGrid.SelectionChanged += new System.EventHandler(this.grid_SelectionChanged);
            this.coordinatorsGrid.CurrentLayoutChanged += new System.EventHandler(this.grid_CurrentLayoutChanged);
            this.coordinatorsGrid.CurrentLayoutChanging += new System.ComponentModel.CancelEventHandler(this.grid_CurrentLayoutChanging);
            this.coordinatorsGrid.KeyDown += new System.Windows.Forms.KeyEventHandler(this.grid_KeyDown);
            this.coordinatorsGrid.MouseDown += new System.Windows.Forms.MouseEventHandler(this.grid_MouseDown);
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1770, 1075);
            this.Controls.Add(this.coordinatorsPanel);
            this.Controls.Add(this.attractionsPanel);
            this.Controls.Add(this.accommodationsPanel);
            this.Controls.Add(this.toursPanel);
            this.Controls.Add(this.vehiclesPanel);
            this.Controls.Add(this.navigationPanel);
            this.Controls.Add(this.ribbonStatusBar1);
            this.Controls.Add(this.ribbon);
            this.Name = "MainForm";
            this.Text = "Form1";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainForm_FormClosing);
            this.Load += new System.EventHandler(this.MainForm_Load);
            this.Shown += new System.EventHandler(this.MainForm_Shown);
            ((System.ComponentModel.ISupportInitialize)(this.vehiclesBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tourunDataSet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.toursBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.accommodationsBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.attractionsBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.coordinatorsBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelManager)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.navigationPanel)).EndInit();
            this.navigationPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.toursNavigationPanel)).EndInit();
            this.toursNavigationPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.accommodationsNavigationPanel)).EndInit();
            this.accommodationsNavigationPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.attractionsNavigationPanel)).EndInit();
            this.attractionsNavigationPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.coordinatorsNavigationPanel)).EndInit();
            this.coordinatorsNavigationPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.vehiclesNavigationPanel)).EndInit();
            this.vehiclesNavigationPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.vehiclesPanel)).EndInit();
            this.vehiclesPanel.ResumeLayout(false);
            this.vehiclesPanelContainer.ResumeLayout(false);
            ((System.Configuration.IPersistComponentSettings)(this.vehiclesGrid)).LoadComponentSettings();
            ((System.ComponentModel.ISupportInitialize)(this.vehiclesGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.toursPanel)).EndInit();
            this.toursPanel.ResumeLayout(false);
            this.toursPanelContainer.ResumeLayout(false);
            ((System.Configuration.IPersistComponentSettings)(this.toursGrid)).LoadComponentSettings();
            ((System.ComponentModel.ISupportInitialize)(this.toursGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.accommodationsPanel)).EndInit();
            this.accommodationsPanel.ResumeLayout(false);
            this.accommodationsPanelContainer.ResumeLayout(false);
            ((System.Configuration.IPersistComponentSettings)(this.accommodationsGrid)).LoadComponentSettings();
            ((System.ComponentModel.ISupportInitialize)(this.accommodationsGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.attractionsPanel)).EndInit();
            this.attractionsPanel.ResumeLayout(false);
            this.attractionsPanelContainer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.attractionsGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.coordinatorsPanel)).EndInit();
            this.coordinatorsPanel.ResumeLayout(false);
            this.coordinatorsPanelContainer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.coordinatorsGrid)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private Janus.Windows.Ribbon.Ribbon ribbon;
        private Janus.Windows.Ribbon.RibbonTab generaRibbonTab;
        private Janus.Windows.Ribbon.RibbonStatusBar ribbonStatusBar1;
        private Janus.Windows.UI.Dock.UIPanelManager panelManager;
        private Janus.Windows.UI.Dock.UIPanelGroup navigationPanel;
        private Janus.Windows.UI.Dock.UIPanel toursPanel;
        private Janus.Windows.UI.Dock.UIPanelInnerContainer toursPanelContainer;
        private Janus.Windows.UI.Dock.UIPanel attractionsPanel;
        private Janus.Windows.UI.Dock.UIPanelInnerContainer attractionsPanelContainer;
        private Janus.Windows.UI.Dock.UIPanel accommodationsPanel;
        private Janus.Windows.UI.Dock.UIPanelInnerContainer accommodationsPanelContainer;
        private Janus.Windows.UI.Dock.UIPanel toursNavigationPanel;
        private Janus.Windows.UI.Dock.UIPanelInnerContainer toursNavigationPanelContainer;
        private Janus.Windows.UI.Dock.UIPanel accommodationsNavigationPanel;
        private Janus.Windows.UI.Dock.UIPanelInnerContainer accommodationsNavigationPanelContainer;
        private Janus.Windows.UI.Dock.UIPanel attractionsNavigationPanel;
        private Janus.Windows.UI.Dock.UIPanelInnerContainer attractionsNavigationPanelContainer;
        private TourunDataSet tourunDataSet;
        private Janus.Windows.UI.Dock.UIPanel coordinatorsPanel;
        private Janus.Windows.UI.Dock.UIPanelInnerContainer coordinatorsPanelContainer;
        private Janus.Windows.UI.Dock.UIPanel coordinatorsNavigationPanel;
        private Janus.Windows.UI.Dock.UIPanelInnerContainer coordinatorsNavigationPanelContainer;
        private Janus.Windows.UI.Dock.UIPanel vehiclesNavigationPanel;
        private Janus.Windows.UI.Dock.UIPanelInnerContainer vehiclesNavigationPanelContainer;
        private Janus.Windows.UI.Dock.UIPanel vehiclesPanel;
        private Janus.Windows.UI.Dock.UIPanelInnerContainer vehiclesPanelContainer;
        private Janus.Windows.Ribbon.RibbonGroup ribbonGroup1;
        private Janus.Windows.Ribbon.ButtonCommand newTourCommand;
        private Janus.Windows.Ribbon.ButtonCommand newAcommodationCommand;
        private Janus.Windows.Ribbon.ButtonCommand newAttractionCommand;
        private Janus.Windows.Ribbon.ButtonCommand newCoordinatorCommand;
        private Janus.Windows.Ribbon.ButtonCommand newVehicleCommand;
        private Janus.Windows.Ribbon.SeparatorCommand separatorCommand1;
        private Janus.Windows.Ribbon.ButtonCommand editDataCommand;
        private Janus.Windows.Ribbon.ButtonCommand deleteDataCommand;
        private Janus.Windows.Ribbon.RibbonGroup ribbonGroup2;
        private Janus.Windows.Ribbon.ButtonCommand refreshDataCommand;
        private System.Windows.Forms.BindingSource toursBindingSource;
        private System.Windows.Forms.BindingSource accommodationsBindingSource;
        private System.Windows.Forms.BindingSource coordinatorsBindingSource;
        private System.Windows.Forms.BindingSource attractionsBindingSource;
        private System.Windows.Forms.BindingSource vehiclesBindingSource;
        private Janus.Windows.GridEX.GridEX toursGrid;
        private Janus.Windows.Ribbon.SeparatorCommand separatorCommand2;
        private Janus.Windows.Ribbon.ButtonCommand exportToExcelCommand;
        private Janus.Windows.Ribbon.ButtonCommand gridReportCommand;
        private Janus.Windows.Ribbon.RibbonGroup filtersRibbonGroup;
        private Janus.Windows.Ribbon.ButtonCommand clearFiltersCommand;
        private Janus.Windows.Ribbon.StatusBarPanel infoCommand;
        private Janus.Windows.GridEX.Export.GridEXExporter gridExporter;
        private Janus.Windows.GridEX.GridEXPrintDocument gridEXPrintDocument;
        private Janus.Windows.GridEX.GridEX coordinatorsGrid;
        private Janus.Windows.GridEX.GridEX attractionsGrid;
        private Janus.Windows.GridEX.GridEX accommodationsGrid;
        private Janus.Windows.GridEX.GridEX vehiclesGrid;
    }
}

