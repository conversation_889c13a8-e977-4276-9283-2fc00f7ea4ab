﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Tourun.UI.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Tourun.UI.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tourun.
        /// </summary>
        internal static string AppName {
            get {
                return ResourceManager.GetString("AppName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το εφεδρικό αρχείο δεν πρέπει να είναι το ίδιο με το αρχείο που χρησιμοποιεί η εφαρμογή..
        /// </summary>
        internal static string BackupFileConflictMessage {
            get {
                return ResourceManager.GetString("BackupFileConflictMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ο συντονιστής υπάρχει ήδη..
        /// </summary>
        internal static string CoordinatorExistsMessage {
            get {
                return ResourceManager.GetString("CoordinatorExistsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Η ενέργεια απέτυχε διότι τα δεδομένα στην βάση δεδομένων έχουν αλλάξει πριν διεκπεραιώσετε την ενέργεια. Παρακαλώ ανανεώστε τα δεδομένα. .
        /// </summary>
        internal static string DatabaseConcurrencyExceptionMessage {
            get {
                return ResourceManager.GetString("DatabaseConcurrencyExceptionMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Η σύνδεση με την βάση δεδομένων απέτυχε. Παρακαλώ ελέγξτε τις ρυθμίσεις της σύνδεσης με την βάση δεδομένων..
        /// </summary>
        internal static string DatabaseConnectivityErrorMessage {
            get {
                return ResourceManager.GetString("DatabaseConnectivityErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ReckonData.mdb.
        /// </summary>
        internal static string DatabaseFileName {
            get {
                return ResourceManager.GetString("DatabaseFileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Η βάση δεδομένων δεν βρέθηκε στο φάκελο {0}..
        /// </summary>
        internal static string DatabaseNotFoundMessage {
            get {
                return ResourceManager.GetString("DatabaseNotFoundMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Είστε σίγουρος ότι θέλετε να γίνει η διαγραφή;.
        /// </summary>
        internal static string DeleteDataConfirmationMessage {
            get {
                return ResourceManager.GetString("DeleteDataConfirmationMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string <NAME_EMAIL>.
        /// </summary>
        internal static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Η αποστολή του email απέτυχε..
        /// </summary>
        internal static string EmailNotSentMessage {
            get {
                return ResourceManager.GetString("EmailNotSentMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το Email στάλθηκε με επιτυχία..
        /// </summary>
        internal static string EmailSentMessage {
            get {
                return ResourceManager.GetString("EmailSentMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Οι ρυθμίσεις που απαιτούνται για την αποστολή Email δεν είναι συμπληρωμένες. Παρακαλώ πηγαίνετε στις ρυθμίσεις του προγράμματος για να τις συμπληρώσετε..
        /// </summary>
        internal static string EmailSettingsNoFilledMessage {
            get {
                return ResourceManager.GetString("EmailSettingsNoFilledMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Η συμπλήρωση αυτού του πεδίου είναι υποχρεωτική..
        /// </summary>
        internal static string FieldRequiredMessage {
            get {
                return ResourceManager.GetString("FieldRequiredMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πεδία.
        /// </summary>
        internal static string FieldsText {
            get {
                return ResourceManager.GetString("FieldsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το αρχείο που επιλέξατε δεν υπάρχει..
        /// </summary>
        internal static string FileNotExistsMessage {
            get {
                return ResourceManager.GetString("FileNotExistsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ο κατάλογος δεν υπάρχει..
        /// </summary>
        internal static string FolderNotExistsMessage {
            get {
                return ResourceManager.GetString("FolderNotExistsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Παρουσιάστηκε κάποιο απρόσμενο σφάλμα..
        /// </summary>
        internal static string GeneralExceptionMessage {
            get {
                return ResourceManager.GetString("GeneralExceptionMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Υπάρχουν ορισμένα λάθη στην συμπλήρωση της φόρμας. Για να δείτε ποιό είναι το λάθος τοποθετήστε τον κέρσορα πάνω στην ένδειξη λάθους..
        /// </summary>
        internal static string InputDataValidationErrorMessage {
            get {
                return ResourceManager.GetString("InputDataValidationErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Καταχωρήστε πρώτα την εγγραφή (εισάγοντας μια περιγραφή και πατώντας το Enter) και μετά επιλέξτε το αρχείο που θέλετε να εισάγετε..
        /// </summary>
        internal static string InsertFileRecordBeforeSettingFileMessage {
            get {
                return ResourceManager.GetString("InsertFileRecordBeforeSettingFileMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τα στοιχεία πρόσβασης είναι λάθος..
        /// </summary>
        internal static string InvalidCredentialsMessage {
            get {
                return ResourceManager.GetString("InvalidCredentialsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ο επιλεγμένος εκτυπωτής δεν είναι έγκυρος..
        /// </summary>
        internal static string InvalidPrinterError {
            get {
                return ResourceManager.GetString("InvalidPrinterError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ο επιλεγμένος εκτυπωτής δεν είναι έγκυρος..
        /// </summary>
        internal static string InvalidPrinterErrorMessage {
            get {
                return ResourceManager.GetString("InvalidPrinterErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Δεν υπάρχουν εγκατεστημένοι εκτυπωτές..
        /// </summary>
        internal static string NoInstalledPrintersMessage {
            get {
                return ResourceManager.GetString("NoInstalledPrintersMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Επιλογή εφεδρικού αρχείου.
        /// </summary>
        internal static string OpenFileDialogTitle {
            get {
                return ResourceManager.GetString("OpenFileDialogTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap PageSettings16x {
            get {
                object obj = ResourceManager.GetObject("PageSettings16x", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap Preview16x {
            get {
                object obj = ResourceManager.GetObject("Preview16x", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Προεπισκόπηση.
        /// </summary>
        internal static string PreviewText {
            get {
                return ResourceManager.GetString("PreviewText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap Print16x {
            get {
                object obj = ResourceManager.GetObject("Print16x", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πρέπει να επιλέξετε τον εκτυπωτή για τις αναφορές στις ρυθμίσεις των εκτυπώσεων..
        /// </summary>
        internal static string PrinterNotSetErrorMessage {
            get {
                return ResourceManager.GetString("PrinterNotSetErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εκτύπωση.
        /// </summary>
        internal static string PrintText {
            get {
                return ResourceManager.GetString("PrintText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εγγραφές.
        /// </summary>
        internal static string RecordsText {
            get {
                return ResourceManager.GetString("RecordsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Αποθήκευση Excel.
        /// </summary>
        internal static string SaveExcelText {
            get {
                return ResourceManager.GetString("SaveExcelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Επιλογή αρχείου βάσης δεδομένων.
        /// </summary>
        internal static string SearchDatabaseDialogTitle {
            get {
                return ResourceManager.GetString("SearchDatabaseDialogTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ο κατάλογος στον οποίο θα δημιουργηθεί το εφεδρικό αρχείο δεν πρέπει να είναι ο ίδιος με τον κατάλογο που βρίσκεται η βάση δεδομένων η οποία χρησιμοποιείται. Παρακαλώ επιλέξτε άλλον κατάλογο..
        /// </summary>
        internal static string SelectAnotherFolderMessage {
            get {
                return ResourceManager.GetString("SelectAnotherFolderMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Έκδοση.
        /// </summary>
        internal static string VersionString {
            get {
                return ResourceManager.GetString("VersionString", resourceCulture);
            }
        }
    }
}
