﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>Tourun</value>
  </data>
  <data name="BackupFileConflictMessage" xml:space="preserve">
    <value>Το εφεδρικό αρχείο δεν πρέπει να είναι το ίδιο με το αρχείο που χρησιμοποιεί η εφαρμογή.</value>
  </data>
  <data name="CoordinatorExistsMessage" xml:space="preserve">
    <value>Ο συντονιστής υπάρχει ήδη.</value>
  </data>
  <data name="DatabaseConcurrencyExceptionMessage" xml:space="preserve">
    <value>Η ενέργεια απέτυχε διότι τα δεδομένα στην βάση δεδομένων έχουν αλλάξει πριν διεκπεραιώσετε την ενέργεια. Παρακαλώ ανανεώστε τα δεδομένα. </value>
  </data>
  <data name="DatabaseConnectivityErrorMessage" xml:space="preserve">
    <value>Η σύνδεση με την βάση δεδομένων απέτυχε. Παρακαλώ ελέγξτε τις ρυθμίσεις της σύνδεσης με την βάση δεδομένων.</value>
  </data>
  <data name="DatabaseFileName" xml:space="preserve">
    <value>ReckonData.mdb</value>
  </data>
  <data name="DatabaseNotFoundMessage" xml:space="preserve">
    <value>Η βάση δεδομένων δεν βρέθηκε στο φάκελο {0}.</value>
  </data>
  <data name="DeleteDataConfirmationMessage" xml:space="preserve">
    <value>Είστε σίγουρος ότι θέλετε να γίνει η διαγραφή;</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value><EMAIL></value>
  </data>
  <data name="EmailNotSentMessage" xml:space="preserve">
    <value>Η αποστολή του email απέτυχε.</value>
  </data>
  <data name="EmailSentMessage" xml:space="preserve">
    <value>Το Email στάλθηκε με επιτυχία.</value>
  </data>
  <data name="EmailSettingsNoFilledMessage" xml:space="preserve">
    <value>Οι ρυθμίσεις που απαιτούνται για την αποστολή Email δεν είναι συμπληρωμένες. Παρακαλώ πηγαίνετε στις ρυθμίσεις του προγράμματος για να τις συμπληρώσετε.</value>
  </data>
  <data name="FieldRequiredMessage" xml:space="preserve">
    <value>Η συμπλήρωση αυτού του πεδίου είναι υποχρεωτική.</value>
  </data>
  <data name="FieldsText" xml:space="preserve">
    <value>Πεδία</value>
  </data>
  <data name="FileNotExistsMessage" xml:space="preserve">
    <value>Το αρχείο που επιλέξατε δεν υπάρχει.</value>
  </data>
  <data name="FolderNotExistsMessage" xml:space="preserve">
    <value>Ο κατάλογος δεν υπάρχει.</value>
  </data>
  <data name="GeneralExceptionMessage" xml:space="preserve">
    <value>Παρουσιάστηκε κάποιο απρόσμενο σφάλμα.</value>
  </data>
  <data name="InputDataValidationErrorMessage" xml:space="preserve">
    <value>Υπάρχουν ορισμένα λάθη στην συμπλήρωση της φόρμας. Για να δείτε ποιό είναι το λάθος τοποθετήστε τον κέρσορα πάνω στην ένδειξη λάθους.</value>
  </data>
  <data name="InsertFileRecordBeforeSettingFileMessage" xml:space="preserve">
    <value>Καταχωρήστε πρώτα την εγγραφή (εισάγοντας μια περιγραφή και πατώντας το Enter) και μετά επιλέξτε το αρχείο που θέλετε να εισάγετε.</value>
  </data>
  <data name="InvalidCredentialsMessage" xml:space="preserve">
    <value>Τα στοιχεία πρόσβασης είναι λάθος.</value>
  </data>
  <data name="InvalidPrinterError" xml:space="preserve">
    <value>Ο επιλεγμένος εκτυπωτής δεν είναι έγκυρος.</value>
  </data>
  <data name="InvalidPrinterErrorMessage" xml:space="preserve">
    <value>Ο επιλεγμένος εκτυπωτής δεν είναι έγκυρος.</value>
  </data>
  <data name="NoInstalledPrintersMessage" xml:space="preserve">
    <value>Δεν υπάρχουν εγκατεστημένοι εκτυπωτές.</value>
  </data>
  <data name="OpenFileDialogTitle" xml:space="preserve">
    <value>Επιλογή εφεδρικού αρχείου</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="PageSettings16x" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\..\..\..\TrueIncome\TrueIncome Solution\TrueIncome\Properties\..\Resources\PageSettings16x.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Preview16x" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\..\..\..\TrueIncome\TrueIncome Solution\TrueIncome\Properties\..\Resources\Preview16x.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="PreviewText" xml:space="preserve">
    <value>Προεπισκόπηση</value>
  </data>
  <data name="Print16x" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\..\..\..\TrueIncome\TrueIncome Solution\TrueIncome\Properties\..\Resources\Print16x.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="PrinterNotSetErrorMessage" xml:space="preserve">
    <value>Πρέπει να επιλέξετε τον εκτυπωτή για τις αναφορές στις ρυθμίσεις των εκτυπώσεων.</value>
  </data>
  <data name="PrintText" xml:space="preserve">
    <value>Εκτύπωση</value>
  </data>
  <data name="RecordsText" xml:space="preserve">
    <value>Εγγραφές</value>
  </data>
  <data name="SaveExcelText" xml:space="preserve">
    <value>Αποθήκευση Excel</value>
  </data>
  <data name="SearchDatabaseDialogTitle" xml:space="preserve">
    <value>Επιλογή αρχείου βάσης δεδομένων</value>
  </data>
  <data name="SelectAnotherFolderMessage" xml:space="preserve">
    <value>Ο κατάλογος στον οποίο θα δημιουργηθεί το εφεδρικό αρχείο δεν πρέπει να είναι ο ίδιος με τον κατάλογο που βρίσκεται η βάση δεδομένων η οποία χρησιμοποιείται. Παρακαλώ επιλέξτε άλλον κατάλογο.</value>
  </data>
  <data name="VersionString" xml:space="preserve">
    <value>Έκδοση</value>
  </data>
</root>