<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="attractionIDLabel.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="nameLabel.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="typeLabel.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="descriptionLabel.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="addressLine1Label.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="addressLine2Label.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="cityLabel.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="stateRegionLabel.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="postalCodeLabel.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="countryLabel.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="latitudeLabel.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="longitudeLabel.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="websiteLabel.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="attractionsBindingSource.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="tourunDataSet.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>778, 17</value>
  </metadata>
  <metadata name="tourunDataSet.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>778, 17</value>
  </metadata>
  <metadata name="visualStyleManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>590, 17</value>
  </metadata>
  <metadata name="errorProvider.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>443, 17</value>
  </metadata>
  <metadata name="commandManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>257, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="saveAndCloseCmd.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAACxIAAAsSAdLdfvwAAAcDSURBVGhD7dhJbBvXHQbwHI22l5yMBonpSjYlubEtucce
        euu1p25xb22RLmlRt0bRtE2a7k5QN13QQ53GjiVbpjZqo0SJpEw7sZ3YliWRFHcO15nhLByKFBUvAwNf
        8d6Q0nA4haoRCKiF/sAHSRfp++m9N2+kZ57Zn/3Zn/35v5gv+DMHTi2s/a5voezp85W9JN0TUqTTISgd
        JNeKyjdm8jUfU1YXSNLaRx+jqL5USfUmZdWTkNX5hKS6Y6I6GxXUmUhRdYV5dWqVUyeDBXU8UFCdKzl1
        dDmrjjxIq0OLjPrt6VjhmJPz9kxJf/jcFPcJY68dD0GcWlhDn68ebxld4yJsV/jNvOLNo1qtobq+UU8N
        lWoNa5V1lNeqUMoVlJQ1SHIZglRCUZDBFSWwnIA8W0Q2zyGdLSCVziOeyiKaTOPsXAw9ThY9kxJ6JkX/
        ifniJ43ddjSnfGWfHmEK8eRp6eZUmxGlMkRJQVEsgRckvOK4g5cu3sDXL97A1971w30/hmQ6h3gqg2iC
        wVl3DN1jdciEhO6JXWL6fGW/HtFLIM5myPc9OVqYpl6+AZB1CIEiyGqI6H3ThYO/HNtM/80QEkwWsWQG
        kTiDn7ijGoQi6hkXPjg2LH7K2PG/GgrRIXo9rZDvzecgl9Zo8UZ58jUBSLJiQEhgeQEnWyDB+mqkEY6n
        NMgoq0OINHan6Ld0Zvo8CoU0EKaQuSxEWaGlGyFfUwA5E6JMt5OGEFFgiy2QyzcCm6uxGkvix7NRdDUg
        dQTZ0uRn253i7409tx0C0SPMIN+dy9DfuD7kLGgAGXxRAseLm4c7V+Bx8lwz5D3/yuZqhKKJLYgBQXJ0
        VFww9tx2eglEhzg5r7RAvuPO0N94U0j5BoCsgg6RyXMtkEv+FW01okkEI3GcmYmga0SD6BH2MRFHx0S/
        see20zu/BSEIc0ialjWGlNcDsnkemRxLH7Unz003QS4uLNMtFYokEAjHcMYVphAjQoMI1iENBAn5ZnrI
        y7NpWtYsmwByV+RYMOS+yORbIO/6luiWCoTjWFmN4sz0KuwEYkDYxwQcHbUI0SNOzLVCvuVi6KVGCmsh
        n3O0PF0BHYDcFeQx+/LA+/jKOwv4MskFH8ZvBxCkiBiWghH8gECGNYgeYR+1CDmhgxAEhYxKTZDPDzJI
        pvO0ME1W2z6kPJPRA8iFp90V5GBrTyjtcG8iQhEsBsL44lAE9hHODLE7SANxwq2gZ0Jugtj6C/jqeBL9
        9zIYelDPIkmaxnGfwbV7DAZpUrh6N4krHyUw8CFJHJdvx3D5VhSXbkXwz5sRnB6LomOA+U8IHB22CNEj
        SI67S+hySuh0COi4VtRylcPhgTwOD+S09O88tno6B1la3BQxYhFyfK4O0SGOz27lxZl6XCV81iVrmdZy
        jGSKRKLRXgAb707NN7bZ08kUMSLgyDBvEbKXECNFHBmyApnVIHsGMbwLyJ5CDBfRYQ1S8u8lROeQVcjM
        FmQvIHYN2SsICnFYhJghjrtkvJf6GNyGitLHTyBvPIZUe0Qjrj9EsbIBfq0GrrwOVqmiUKogJ5WRFRVk
        hBLSRRkMLyHFCUixAuJ5HrEch2iWxRJTwPm7LOx1iB7R6SjiMw5u55AXGxDDSpxZrOLp06dQVRVPnjzB
        48eP8ejRIzx8+BAbGxuo1WpYX19HtVpFpVJBuVyGoigolUqQZRmSJEEURQiCAJ7nwXEcWJZFoVBAPp9H
        LpfDN31cC6LDwe8CYrKd/h7baCsim83i17cKLQgKGbQCcZX8RgQ5E3+LrLcVkclk8MYH+RZExzWLkGMu
        SYMYDjaBtBPBMAx+9X6uBUEhV61CTJ5Ofw1X24pIpVIU0oIYtAqZ0iDGR+xfVqttRSSTSbx+M9uK2A3E
        iCB3xNuhSlsRiURCgxgRgzwOW4H0EIjJZffn4FpbEfF4HK/5M2YIi5BJAmm9sQmknYhYLEYhJgirEJFC
        jK8d5wPltiKi0Sh+cT1thsDhKxYhRgSFrChtRYTDYfx8gTFD4NBuIB0XYnjh/CKO9DP05e9Py6W2IlZX
        VymElH/+QgKf/uNdPP/2Cmy7gkxIOPgzD5596R3Y3rpNIW8tyW1FhEIhvOpNUghBPHv6Xzj4owkN0s/u
        HNI9IfnIVjr05h08d3YCnf9Ypq/i5x7IbUUEg0H81KNByEoQxHNv+GEbYMm/n3zGnttO97j4W7O/J05f
        l9qKCAQC+NJklp4Jsgo0BDHA4oX+wm+MPbcdmz9zgGC6xoX5LqfobcQ+JnpfvyuGvUlBvp4sKgsJXvHF
        OcUbZRVPpEAzH84p7lCWZiaQVlwrDM30ckqZWkoqkw8SNM77MWXsXpRm9KOI4rgTln/oToZtA5x3KwXv
        of7CPEHYLmUOGHvuz/7sz/78b86/AZsFcmOPhgCtAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="deleteCmd.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAACxIAAAsSAdLdfvwAAAOaSURBVGhD7dZNTxNBHAbw5YyJFy4eFPpCW6AttF6Ub2C8
        SEj8CmK8mBiB9mC9+QnEe7FQSltLaYFKe5MzmCgsiInat+122xrxSBwzOx1Yprt0OzvloH2SJ4EEZn6d
        THf/HNdLL73855nbvd83u1fom90DHfX5bp6b/XiPXO7KAyEtON3d/UGu1/0EPl3j5g9ucz7+Iefnn3Lz
        nwE3vw843wHg/LB8s4caPx+gv/XtA/l/4RpwLbjms71+cjtjmft6nfMfTXP+w1ecn89wfv47wnSr/B/O
        d/gN7QX3PJqWDdTx8futm1xxoYE6fj7dsuBV18enSFbH8WTrx95cA7SrJ4tbB57tOpjAfX/e8QxsDXWr
        Bty4m6iuTQm4NiTgSktfSAd1vNnGDontGrxZZ6r6gXRQx5Orx0m09wytDR+X4U20XngatxojHdTx5hpv
        VOHb3YDD05fAaEpcIB3U8eQaL2ngZ+gO4GOpqtzRpBggHdTxZGsz+JqcwZX32zAcoeWuo44mq49IB3W8
        mcaUKlz5xVSBu2jgCA9GktUHpIM6E7napCZciWYARxWBIyncJR3UcW3/NF8KV14TBdxJAR9ZQ7WtlU2k
        gzruTLm/LVx52gbgchMigHuSDkMZ36z9bgtXotPnTxR1uKgKdyREYE+IJ+T+huPeko51wRWPwk7hct9V
        gD0usBsjcNwb0o5uuBLdAVzGow/AbozAcaWrcXX4ZafdvN+64ai2mMBujMBxpqWFjuFKtA44wlfAcFR4
        Te5vOM6UFKCCK9Ft4LaYgBoVXpD7G44zWZ2hhjfROuDw9MFwRGA3RuA416UpWjiJ1oTDrsq/sxsjcMZS
        lUnDcBmtDYe1rgrAEmE4RuC4UoK5s0chedpNeFQLXgbWCKotwnCMwIGvdmbwJpqEw1pWysAdZDxG4DjW
        xBMaOHnaWnBYc7j0i9yXWRwJ8bg9XPt+y3AFWgmXGy4D03KJ/RiB44gLOzRw+MW8AFeim3BzuCTXtFRi
        P0bg2OOVODN4+CLcvIw6tFRkP0bg2GPCAg2cPG0LgYY1wS7JH4D9GIFjiwoB5vAlZYtgKFRgP0bgWOPC
        DA2cvCYt8BCE4+bZjxE4wzFhSuvloweOr4kq/C3qYLDIfozAga/4rsFhF2Hzd8h9mcUWKg5YI6VTWvj5
        NVGDF8BgMH96I1QcIPdlGstK+YllpVzXD1febw34YgHcChZqN4OFx+R+vfTSyz+ev+El3Kj2XrBGAAAA
        AElFTkSuQmCC
</value>
  </data>
</root>